<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd" v-auth="'biz.contractmanage:ct_biz_liquidated_damages:add'">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'biz.contractmanage:ct_biz_liquidated_damages:importExcel'">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button
          :loading="exportLoading"
          @click="handleExport"
          v-auth="'biz.contractmanage:ct_biz_liquidated_damages:exportXls'"
        >
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button
          v-show="selectedRowKeys.length"
          @click="handleRemove(false)"
          v-auth="'biz.contractmanage:ct_biz_liquidated_damages:deleteBatch'"
        >
          批量删除
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.number"
          placeholder="搜索单据编号"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span
            class="primary-btn"
            @click="handleView(record)"
            v-auth="'biz.contractmanage:ct_biz_liquidated_damages:view'"
          >
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div class="primary-btn" @click="handleAudit(record, true)">审核(临时功能)</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleViewContract(record)">查看合同</div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div class="primary-btn" @click="handleWithdraw(record)">撤回</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleRemove(record)">删除</div>
                </a-menu-item>
                <a-menu-item v-if="['InExecution', 'Modified', 'Suspended', 'Cleared'].includes(record.bizStatus)">
                  <div class="primary-btn" @click="handleAudit(record, false)">反审核</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail
      ref="detailRef"
      @edit="handleEdit"
      @viewContract="handleViewContract"
      @audit="handleAudit"
      @remove="handleRemove"
      @withdraw="handleWithdraw"
    ></detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('违约处置导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <contract-detail ref="contractDetailRef" readonly></contract-detail>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit, withdraw } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag, renderMoney } from '@/utils/render'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import { page as getContractPage } from '@/views/contract/management/apis'
import { hasPermission } from '@/utils/permission'

const params = reactive({
  column: 'number',
  order: 'desc',
  id: undefined,
  number: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  receiveDate: undefined,
  contract: undefined,
  contractClearing: undefined,
  isCharge: undefined,
  signDate: undefined,
  customer: undefined,
  contractType: undefined,
  operator: undefined,
  operatorDepart: undefined,
  startDate: undefined,
  expireDate: undefined,
  leaseUnit: undefined,
  status: undefined,
  amount: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined
})

const loadContractPage = () => getContractPage({ pageNo: 1, pageSize: 50000 })
const searchList = [
  { label: '客户', name: 'customer', type: 'customer-select' },
  {
    label: '合同',
    name: 'contract',
    type: 'api-select',
    asyncFn: loadContractPage,
    fieldNames: { label: 'contractNumber', value: 'id' }
  },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '应收日期', name: 'receiveDate', type: 'date' },
  { label: '创建时间', name: 'createTime', type: 'date' },
  { label: '单据id', name: 'id', type: 'input' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 120 },
  { title: '合同编号', dataIndex: 'contract_dictText', width: 100 },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '租赁单元', dataIndex: 'signDate', width: 120 },
  { title: '违约金额', dataIndex: 'amount', width: 140, customRender: ({ text }) => renderMoney(text) },
  { title: '应收日期', dataIndex: 'receiveDate', width: 130 },
  { title: '业务日期', dataIndex: 'bizDate', width: 130 },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 140,
    customRender: ({ record }) => record.createTime || record.updateTime
  },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}

const handleEdit = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_liquidated_damages:edit')) return
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

const contractDetailRef = ref()
const handleViewContract = (data) => {
  if (!hasPermission('biz.contractmanage:ct_con_lease:view')) return
  contractDetailRef.value.open(data.contract)
}

const handleWithdraw = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_liquidated_damages:edit')) return
  Modal.confirm({
    title: '是否确认撤回该合同违约处置申请？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await withdraw({ id: data.id })
      message.success(msg)
      onTableChange(pagination.value)
      if (detailRef.value && detailRef.value.visible) {
        detailRef.value.loadDetail(data.id)
      }
    }
  })
}

const handleAudit = async (data, result) => {
  if (result && !hasPermission('biz.contractmanage:ct_biz_liquidated_damages:audit')) return
  if (!result && !hasPermission('biz.contractmanage:ct_biz_liquidated_damages:unAudit')) return
  const { message: msg } = await (result ? audit({ id: data.id }) : unAudit({ id: data.id }))
  message.success(msg)
  onTableChange(pagination.value)
  if (detailRef.value && detailRef.value.visible) {
    detailRef.value.loadDetail(data.id)
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除该违约处置？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success(msg)
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
      if (data && detailRef.value && detailRef.value.visible) {
        detailRef.value.handleClose()
      }
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('违约处置数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  const id = sessionStorage.getItem('idFromContractClearing')
  if (id) {
    params.id = id
    sessionStorage.removeItem('idFromContractClearing')
  }
  onTableChange()
})
</script>
