<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex items-center">
        <a-button type="primary" @click="handleAdd" v-auth="'system:user:add'">
          <i class="a-icon-plus"></i>
          新增
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleRemove(false)">删除</div>
              </a-menu-item>
              <a-menu-item>
                <div class="primary-btn" @click="handleFreeze(false, userStatus.FROZEN)">冻结</div>
              </a-menu-item>
              <a-menu-item>
                <div class="primary-btn" @click="handleFreeze(false, userStatus.NORMAL)">解冻</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <s-input
          v-model="params.username"
          placeholder="搜索用户账号"
          class="ml-[40px] !w-[240px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          label-width="70px"
          :search-list="searchList"
          :clear-ignore-keys="['currentCompany']"
          :stat-ignore-keys="['currentCompany']"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'avatar'">
          <a-avatar :src="getFileAccessHttpUrl(record.avatar)"></a-avatar>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)" v-auth="'system:user:edit'">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div class="primary-btn" @click="handleDetail(record)">详情</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handlePassword(record)">密码</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleRemove(record)">删除</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleFreeze(record)">
                    {{ record.status === userStatus.NORMAL ? '冻结' : '解冻' }}
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit-user ref="editUserRef" @refresh="refreshData"></edit-user>
    <modify-password ref="modifyPasswordRef"></modify-password>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message, Modal } from 'ant-design-vue'
import { getUserList, deleteUser, frozenBatch } from './apis'
import EditUser from './components/EditUser.vue'
import { getFileAccessHttpUrl } from '@/apis/common'
import useTableSelection from '@/hooks/useTableSelection'
import { renderDict, renderDictTag } from '@/utils/render'
import ModifyPassword from './components/ModifyPassword.vue'
import { useDictStore } from '@/store/modules/dict'
import useUserStore from '@/store/modules/user'
import { hasPermission } from '@/utils/permission'

const { userStatus } = useDictStore()
const { userInfo } = useUserStore()

const params = reactive({
  column: 'createTime',
  order: 'desc',
  username: '',
  realname: '',
  sex: '',
  phone: '',
  status: '',
  currentCompany: userInfo.value.currentCompany
})
const searchList = [
  { label: '用户姓名', name: 'realname', type: 's-input' },
  { label: '性别', name: 'sex', type: 'dict-select', code: 'sex' },
  { label: '手机号码', name: 'phone', type: 'input' },
  { label: '用户状态', name: 'status', type: 'dict-select', code: 'user_status' }
]

const defaultColumns = [
  { title: '用户账号', dataIndex: 'username', width: 120, fixed: 'left' },
  { title: '用户姓名', dataIndex: 'realname', width: 100 },
  { title: '头像', dataIndex: 'avatar', width: 120 },
  { title: '性别', dataIndex: 'sex', width: 80, customRender: ({ text }) => renderDict(text, 'sex') },
  { title: '生日', dataIndex: 'birthday', width: 100 },
  { title: '手机号', dataIndex: 'phone', width: 100 },
  { title: '所属公司', width: 150, dataIndex: 'currentCompany_dictText' },
  { title: '所属部门', width: 150, dataIndex: 'currentDepart_dictText' },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => renderDictTag(text, 'user_status', 'dot')
  },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getUserList)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const refreshData = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editUserRef = ref()
const handleAdd = () => {
  editUserRef.value.open()
}
const handleEdit = (row) => {
  editUserRef.value.open(row)
}

const handleDetail = (row) => {
  if (!hasPermission('system:user:queryById')) return
  editUserRef.value.open(row, true)
}

const modifyPasswordRef = ref()
const handlePassword = (data) => {
  if (!hasPermission('system:user:changepwd')) return
  modifyPasswordRef.value.open(data)
}

/**
 * 冻结/解冻用户
 * @param {Boolean | Object} data 当data为false时，表示批量冻结/解冻
 * @param {Number} status 冻结/解冻状态，只有批量操作时，才有此参数
 */
const handleFreeze = (data, status) => {
  if (!hasPermission('system:user:frozenBatch')) return
  Modal.confirm({
    title: '系统提示',
    content: data
      ? `是否冻结用户“${data.realname}”`
      : `是否确认${status === userStatus.NORMAL ? '解冻' : '冻结'}选中用户？`,
    centered: true,
    onOk: async () => {
      await frozenBatch({
        ids: data ? data.id : selectedRowKeys.value.join(','),
        status: status || (data.status === userStatus.NORMAL ? userStatus.FROZEN : userStatus.NORMAL)
      })
      let msg = ''
      if (status) {
        msg = status === userStatus.NORMAL ? '已解冻' : '已冻结'
      } else {
        msg = data.status === userStatus.NORMAL ? '已冻结' : '已解冻'
      }
      message.success(msg)
      clearSelection()
      onTableChange(pagination.value)
    }
  })
}

/**
 * 删除用户
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  if (!hasPermission('system:user:delete')) return
  Modal.confirm({
    title: '系统提示',
    content: data ? `是否确认删除用户“${data.realname}”？` : '是否确认删除选中用户？',
    centered: true,
    onOk: async () => {
      await deleteUser({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

onMounted(() => {
  onTableChange()
})
</script>
