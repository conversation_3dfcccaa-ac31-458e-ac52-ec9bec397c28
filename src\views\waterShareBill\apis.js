import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/watershare/waterShareBill/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/watershare/waterShareBill/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/edit',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/unAudit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/watershare/waterShareBill/deleteBatch',
    params
  })
}

// 水电费分摊单-计算
export const waterShareBillList = (project, building, floor, type, date) => {
  let url = `/biz/watershare/waterShareBill/leaseUnit/list/${project}`
  if (building) {
    url += `/${building}`
  }
  if (floor) {
    url += `/${floor}`
  }
  if (type) {
    url += `/${type}`
  }
  if (date) {
    url += `/${date}`
  }
  return request({
    method: 'get',
    url
  })
}

// 水电费分摊单-通过项目、楼栋、楼层 查询单元列表
export const waterShareBillCalc = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/calc',
    data
  })
}

// 水电分摊单分录主表ID查询
export const queryWaterShareBillEntry = (params) => {
  return request({
    method: 'get',
    url: '/biz/watershare/waterShareBill/queryWaterShareBillEntryByMainId',
    params
  })
}

// 生成应收单
export const generateReceiveBill = (params) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareBill/generateReceiveBill',
    params
  })
}
