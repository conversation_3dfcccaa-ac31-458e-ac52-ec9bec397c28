<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item label="名称">
          <a-input v-model:value="params.name" placeholder="搜索名称">
            <template #prefix>
              <i class="a-icon-search text-primary"></i>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="loadData">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
      <div>
        <a-button type="primary" @click="handleAdd">新增</a-button>
        <a-button @click="handleRemove(false)" v-show="checkedKeys.checked.length">批量删除</a-button>
      </div>
    </div>
    <a-tree
      v-model:expanded-keys="expandedKeys"
      v-model:checked-keys="checkedKeys"
      v-model:selected-keys="selectedKeys"
      :auto-expand-parent="autoExpandParent"
      checkable
      show-line
      check-strictly
      :tree-data="list"
    >
      <template #title="scope">
        <div class="flex items-center">
          <span class="line-clamp-1 w-[240px]" :class="{ 'text-error font-bold': nameIsActive(scope.title) }">
            {{ scope.title }}
          </span>
          <span class="primary-btn" @click.stop="handleEdit(scope)">编辑</span>
          <span class="primary-btn" @click.stop="handleRemove(scope)">删除</span>
          <span class="primary-btn" @click.stop="handleAddChildren(scope)">添加下级</span>
        </div>
      </template>
    </a-tree>
    <edit-category ref="editCategoryRef" @refresh="loadData"></edit-category>
  </div>
</template>

<script setup>
import { tree, deleteBatch } from './apis'
import EditCategory from './components/EditCategory.vue'
import { message, Modal } from 'ant-design-vue'

const params = reactive({
  name: '',
  pcode: '',
  async: false
})

const loading = ref(false)
const list = ref([])
const nodeList = ref([])

const loadData = async () => {
  loading.value = true
  const { result } = await tree(params)
  list.value = result
  loading.value = false
  getNodeList(result)
}

const getNodeList = (list) => {
  list.forEach((item) => {
    nodeList.value.push(item)
    if (item.children && item.children.length) {
      getNodeList(item.children)
    }
  })
}

const expandedKeys = ref([])
const selectedKeys = ref([])
const checkedKeys = ref({ checked: [] })
const autoExpandParent = ref(false)

const getParentKey = (key, tree) => {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}
watch(
  () => params.name,
  (value) => {
    if (!value) {
      expandedKeys.value = []
      autoExpandParent.value = false
      return
    }
    const expanded = nodeList.value
      .map((item) => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, list.value)
        }
        return null
      })
      .filter((item, i, self) => item && self.indexOf(item) === i)
    expandedKeys.value = expanded
    autoExpandParent.value = true
  }
)

const nameIsActive = (title) => {
  return params.name && title.toLowerCase().includes(params.name.toLowerCase())
}

const editCategoryRef = ref()
const handleAdd = () => {
  editCategoryRef.value.open()
}
const handleEdit = (row) => {
  editCategoryRef.value.open({
    id: row.key,
    pid: row.parentId,
    name: row.title
  })
}

/**
 * 删除
 * @param {Object | Boolean} row 当批量删除时，row=false
 */
const handleRemove = (row) => {
  Modal.confirm({
    title: row ? '确认删除该分类？' : '确认删除选中分类？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: row ? row.key : checkedKeys.value.checked.join(',') })
      message.success('删除成功')
      loadData()
      checkedKeys.value.checked = []
    }
  })
}

const handleAddChildren = (row) => {
  editCategoryRef.value.open({
    id: '',
    name: '',
    pid: row.key
  })
}

const handleReset = () => {
  params.name = ''
  params.code = ''
  loadData()
}

onMounted(() => {
  loadData()
})
</script>
