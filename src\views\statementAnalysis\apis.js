import request from '@/apis/http'

// 房产情况汇总表
export const houseSummaryList = (data) => {
  return request({
    method: 'post',
    url: '/rpts/houseStatusTotalRpt/query',
    data
  })
}
// 房产情况明细表
export const houseDetailList = (data) => {
  return request({
    method: 'post',
    url: '/rpts/houseStatusDetailRpt/query',
    data
  })
}
// 应收账款统计表
export const accountsReceivableSummaryList = (data) => {
  return request({
    method: 'post',
    url: '/rpts/recAccountTotalRpt/query',
    data
  })
}
// 报表-应收账款明细报表
export const accountsReceivableDetailSummaryList = (data) => {
  return request({
    method: 'post',
    url: '/rpts/recAccountDetailBillTypeRpt/query',
    data
  })
}

// 账龄分析表
export const assetVirtualAgingSummaryList = (data) => {
  return request({
    method: 'post',
    url: '/rpts/assetVirtualAgingRpt/query',
    data
  })
}
