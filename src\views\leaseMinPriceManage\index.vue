<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button
          v-auth="'bas:ct_bas_lease_base_price_management:add'"
          class="mb-[10px]"
          type="primary"
          @click="handleAdd"
        >
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button v-auth="'bas:ct_bas_lease_base_price_management:importExcel'" class="mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'bas:ct_bas_lease_base_price_management:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div
                  v-auth="'bas:ct_bas_lease_base_price_management:deleteBatch'"
                  class="primary-btn"
                  @click="handleRemove(false)"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="">
          <s-input
            v-model="search.number"
            placeholder="搜索规则编号"
            class="ml-[10px] !w-[280px]"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <a-form-item>
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 1500 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>

        <template v-if="column.dataIndex === 'action'">
          <span v-auth="'bas:ct_bas_lease_base_price_management:view'" class="primary-btn" @click="rowView(record)">
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div
                    v-auth="'bas:ct_bas_lease_base_price_management:edit'"
                    class="primary-btn"
                    @click="rowEdit(record)"
                  >
                    编辑
                  </div>
                </a-menu-item>
                <a-menu-item>
                  <div
                    v-auth="'bas:ct_bas_lease_base_price_management:delete'"
                    class="primary-btn"
                    @click="handleRemove(record)"
                  >
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <detail ref="detailRef" @load-data="onTableChange" @handle-use="handleStatusChange"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入订单"
      :download-fn="() => exportExcel('订单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import usePageTable from '@/hooks/usePageTable'
import { renderDict, renderMoney } from '@/utils/render'
import AddEdit from './components/AddEdit.vue'
import Detail from './components/Detail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import { getPage, deleteBatch, updateStatus, exportExcel, importExcel } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
})
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
    item.methodsText = `${renderDict(item.basePriceUnit, 'CT_BASE_ENUM_LeaseBasePriceManagement_BasePriceUnit')}：${renderMoney(item.basePrice, 2)}元`
  })
  return list
})
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
const search = ref({
  column: 'number',
  order: 'desc',
  number: ''
})
const searchFilter = ref({})
const searchList = reactive([
  { label: '规则名称', name: 'name', type: 's-input', placeholder: '请输入规则名称' },
  {
    label: '启用状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择启用状态',
    code: 'CT_BASE_ENUM_BaseStatus'
  },
  // { label: '规则条件', name: '', placeholder: '请输入规则条件' },
  // { label: '底价控制方式', name: '',  placeholder: '请输入底价控制方式' },
  {
    label: '规则优先级',
    name: 'priority',
    type: 'dic',
    placeholder: '请输入规则优先级',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  {
    label: '创建时间',
    name: 'createTime',
    type: 'date',
    placeholder: '请选择状态'
  }
])

const defaultColumns = [
  { title: '规则名称', dataIndex: 'name', width: 180, fixed: true },
  { title: '启用状态', dataIndex: 'status' },
  // { title: '规则条件', dataIndex: '', ellipsis: true },
  { title: '底价控制方式', dataIndex: 'methodsText', ellipsis: true },
  {
    title: '规则优先级',
    dataIndex: 'priority',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_LeaseBasePriceManagement_Priority')
  },
  { title: '规则编号', dataIndex: 'number' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '操作', dataIndex: 'action', width: 150, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const handleStatusChange = async (data, val) => {
  if (data.loading) return
  try {
    data.loading = true
    await updateStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
    data.loading = false
    message.success('保存成功')
    data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    data.loading = false
    data.checked = !val
  }
}

const addEditRef = ref()
// 新增
const handleAdd = () => {
  addEditRef?.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row)
}

const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前租赁底价？' : '确认批量删除选中租赁底价？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('租赁底价数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
