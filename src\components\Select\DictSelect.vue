业务字典通用选择器
<template>
  <a-select
    v-bind="$attrs"
    ref="selectRef"
    show-search
    :value="value || undefined"
    :style="{ width }"
    :options="options"
    :filter-option="filterOption"
    allow-clear
    @change="onchange"
  ></a-select>
</template>

<script setup>
import { useDictStore } from '@/store/modules/dict'

const { code, modelValue } = defineProps({
  width: { type: String, default: '100%' },
  modelValue: { type: [Array, String] },
  code: { required: true, type: String }
})

const store = useDictStore()

const emit = defineEmits(['update:modelValue', 'change'])

const options = computed(() => {
  if (!store.dict) return []
  if (!Object.keys(store.dict).length) return []
  return store.dict[code]
})

const value = computed(() => {
  if (modelValue === undefined) return undefined
  if (modelValue === null) return null
  if (typeof modelValue === 'string') return modelValue
  if (typeof modelValue === 'number') return String(modelValue)
  return modelValue.map((item) => String(item))
})

const onchange = (val) => {
  emit('update:modelValue', val || '')
  emit('change', val || '')
}

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
</script>
