<template>
  <a-drawer
    v-model:open="visible"
    :title="form.id ? '编辑水电分摊公式信息' : '新建水电分摊公式信息'"
    placement="right"
    width="1072px"
    :mask-closable="false"
    class="formula-drawer common-drawer"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form
        :model="form"
        ref="formRef"
        :rules="rules"
        label-align="left"
        :label-col="{ style: { width: '100px' } }"
        autocomplete="off"
      >
        <a-form-item name="formula">
          <div class="formula-generator">
            <div class="formula-generator__editor">
              <div class="formula-generator__editor-title">
                <div>公式编辑器</div>
                <div>
                  <span class="primary-btn ml-[10px]" @click="showScript">查看脚本</span>
                </div>
              </div>
              <div class="formula-generator__editor-content">
                <FormulaCodemirror v-model="form.formulaZn" ref="codeMirrorRef" />
              </div>
            </div>

            <div class="formula-generator__tool">
              <div class="formula-generator__tool-entity">
                <div class="formula-generator__tool-title">业务实体</div>
                <div class="formula-generator__tool-content">
                  <a-collapse accordion expand-icon-position="right">
                    <a-collapse-panel v-for="info in classInfos" :key="info.key" :header="info.name">
                      <div
                        class="field-item"
                        v-for="(field, index) in info.fields"
                        :key="index"
                        @dblclick="insertField(info, field)"
                      >
                        <div class="flex-1">
                          <span class="block" :title="field.name">{{ field.name }}</span>
                          <small class="text-[12px] text-tertiary break-all">{{ field.field }}</small>
                        </div>
                        <div :class="`data_type__${fieldTypeMap[field.type]?.en} h-[24px]`">
                          {{ fieldTypeMap[field.type]?.cn }}
                        </div>
                      </div>
                    </a-collapse-panel>
                  </a-collapse>
                </div>
              </div>
              <div class="formula-generator__tool-function">
                <div class="formula-generator__tool-title">常用函数</div>
                <div class="formula-generator__tool-content">
                  <a-popover title="函数说明" v-for="(item, index) in functions" :key="index">
                    <template #content>
                      <p>{{ item.desc }}</p>
                      <p v-for="(p, pi) in item.params" :key="pi">{{ p.text }}</p>
                    </template>
                    <div class="function-item" @dblclick="insertFunction(item)">
                      <div class="function-item__value">{{ item.value }}</div>
                      <div class="function-item__title">{{ item.title }}</div>
                    </div>
                  </a-popover>
                </div>
              </div>
              <div class="formula-generator__tool-variable">
                <div class="formula-generator__tool-title">
                  <span>常用语法</span>
                </div>
                <div class="formula-generator__tool-content">
                  <div class="keyboard-wrapper">
                    <div class="keyboard">
                      <div
                        class="key-item"
                        v-for="(key, index) in keyboards"
                        :key="index"
                        :style="`width: ${key.width}`"
                        @click="insertKey(key)"
                      >
                        {{ key.text }}
                      </div>
                    </div>
                  </div>

                  <div class="keyboard-wrapper">
                    <div class="keyboard">
                      <div
                        class="key-item"
                        v-for="(key, index) in operators"
                        :key="index"
                        :style="`width: ${key.width}`"
                        @click="insertKey(key)"
                      >
                        {{ key.text }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-form-item>
        <div class="flex gap-x-[40px]">
          <a-form-item label="公式名称" name="name" class="w-[calc(50%-20px)]">
            <a-input v-model:value="form.name" placeholder="请输入公式名称" />
          </a-form-item>
          <a-form-item label="计算结果" name="calculResult" class="w-[calc(50%-20px)]">
            <dict-select
              v-model="form.calculResult"
              code="CT_BASE_ENUM_WaterShareFormulaManagement_CalculResult"
            ></dict-select>
          </a-form-item>
        </div>
        <div class="flex gap-x-[40px]">
          <a-form-item label="物业管理公司" name="manageCompany" class="w-[calc(50%-20px)]">
            <company-select v-model="form.manageCompany" disabled></company-select>
          </a-form-item>
        </div>
        <a-form-item label="计算逻辑解释" name="calcalLoginExplain">
          <a-textarea
            v-model:value="form.calcalLoginExplain"
            placeholder="请输入计算逻辑解释"
            show-count
            :maxlength="255"
            :auto-size="{ minRows: 4, maxRows: 4 }"
          ></a-textarea>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="form.remark"
            placeholder="请输入备注(选填)"
            show-count
            :maxlength="255"
            :auto-size="{ minRows: 4, maxRows: 4 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" @click="mock">模拟</a-button>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>

  <formula-detail ref="formulaDetailRef" @mock="mock" />
  <formula-mock ref="formulaMockRef" />
</template>

<script setup>
import { add, edit, detail } from '../apis.js'
import { message } from 'ant-design-vue'
import useFormula from '../useFormual.js'
import FormulaDetail from './FormulaDetail.vue'
import FormulaMock from './FormulaMock.vue'
import useUserStore from '@/store/modules/user'

const { classInfos, getScriptZn, getScript } = useFormula()

const { userInfo } = useUserStore()

const emit = defineEmits(['refresh'])

const codeMirrorRef = ref()

const lastCursor = computed(() => {
  if (!codeMirrorRef.value) return form.formulaZn.length
  if (codeMirrorRef.value.lastCursor === -1) return form.formulaZn.length
  return codeMirrorRef.value.lastCursor
})

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  } else {
    form.manageCompany = userInfo.value.currentCompany
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.name = result.name
  form.formula = result.formula
  form.formulaZn = getScriptZn(result.formula)
  form.calculResult = result.calculResult
  form.calcalLoginExplain = result.calcalLoginExplain
  loading.value = false
}

const form = reactive({
  id: '',
  name: '',
  formula: '',
  formulaZn: '',
  calculResult: '',
  calcalLoginExplain: '',
  manageCompany: ''
})

const rules = {
  name: [{ required: true, message: '请输入公式名称', trigger: 'blur' }],
  formulaZn: [{ required: true, message: '请输入公式', trigger: 'blur' }],
  calculResult: [{ required: true, message: '请输入计算结果', trigger: 'blur' }],
  calcalLoginExplain: [{ required: true, message: '请输入计算逻辑解释', trigger: 'blur' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  if (!form.formulaZn) {
    message.warning('请输入公式')
    return
  }
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.formula = getScript(form.formulaZn)
    const testResult = await formulaMockRef.value?.test(classInfos.value, form.formula)
    if (testResult !== true) {
      message.error(testResult)
      return
    }
    form.status = form.status || 'ENABLE'
    const params = { ...form }
    const data = await (form.id ? edit(params) : add(params))
    message.success(data.message)
    confirmLoading.value = false
    handleCancel()
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.name = ''
  form.formula = ''
  form.formulaZn = ''
  form.calculResult = ''
  form.calcalLoginExplain = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

const fieldTypeMap = {
  'java.lang.String': {
    cn: '文本',
    en: 'string'
  },
  'java.lang.Integer': {
    cn: '整数',
    en: 'integer'
  },
  'java.lang.Double': {
    cn: '小数',
    en: 'double'
  },
  'java.lang.Float': {
    cn: '小数',
    en: 'float'
  },
  'java.lang.Boolean': {
    cn: '布尔',
    en: 'boolean'
  },
  'java.math.BigDecimal': {
    cn: '金额',
    en: 'bigDecimal'
  },
  'java.util.Date': {
    cn: '日期',
    en: 'date'
  },
  'java.util.List': {
    cn: '列表',
    en: 'list'
  }
}

const functions = [
  {
    title: '求和',
    value: 'sum',
    desc: 'sum(a), 返回合计数',
    params: [
      {
        text: '参数a为列表或数组',
        value: '参数a'
      }
    ]
  },
  {
    title: '求平均值',
    value: 'avg',
    desc: 'avg(a), 返回列表或数组中所有值的平均值',
    params: [
      {
        text: '参数a为列表或数组',
        value: '参数a'
      }
    ]
  },
  {
    title: '求个数',
    value: 'count',
    desc: 'count(a), 返回列表或数组元素的个数',
    params: [{ text: '参数a为列表或数组', value: '参数a' }]
  },
  {
    title: '求最大值',
    value: 'max',
    desc: 'max(a), 返回最大值',
    params: [{ text: '参数a为列表或数组', value: '参数a' }]
  },
  {
    title: '求最小值',
    value: 'min',
    desc: 'min(a),返回最小值',
    params: [{ text: '参数a为列表或数组', value: '参数a' }]
  },
  {
    title: '取当前时间',
    value: 'now',
    desc: 'now(),返回当前日期时间字符串。 格式："2004-4-17 14:43:05" ',
    params: [{ text: '无参数' }]
  },
  {
    title: '取时间差',
    value: 'dateDiff',
    desc: 'dateDiff(a,b),返回参数1和参数2间相差多少天',
    params: [
      { text: '参数a：时间日期字符串', value: '参数a' },
      { text: '参数b：时间日期字符串', value: '参数b' }
    ]
  },
  {
    title: '四舍五入',
    value: 'round',
    desc: 'round(number, precision),小数点后指定位数的四舍五入',
    params: [
      { text: '参数number：需要四舍五入的数', value: '参数number' },
      { text: '参数precision：小数点后保留位数', value: '参数precision' }
    ]
  },
  {
    title: '映射',
    value: 'map',
    desc: 'map(objects, key),提取列表对象的指定属性输出列表',
    params: [
      { text: '参数objects：列表对象', value: '参数objects' },
      // eslint-disable-next-line quotes
      { text: "参数key：需要提取的属性，例如：'name'", value: '参数key' }
    ]
  }
]

const keyboards = [
  { text: '如果', width: '30%', value: 'if(){ \n// TODO  \n}' },
  { text: '如果...否则...', width: '40%', value: 'if(){  \n// TODO  \n }else{ \n// TODO  \n}' },
  { text: '循环', width: '30%', value: 'for(i=0;i<10;i++){ \n// TODO 循环体 \n}' },
  { text: '且', width: '25%', value: '&&', space: true },
  { text: '或', width: '25%', value: '||', space: true },
  { text: '等于', width: '25%', value: '==', space: true },
  { text: '不等于', width: '25%', value: '!=', space: true },
  { text: '大于', width: '25%', value: '>', space: true },
  { text: '小于', width: '25%', value: '<', space: true },
  { text: '大于等于', width: '25%', value: '>=', space: true },
  { text: '小于等于', width: '25%', value: '<=', space: true },
  { text: '小括号', width: '25%', value: '()' },
  { text: '中括号', width: '25%', value: '[]' },
  { text: '大括号', width: '25%', value: '｛｝' },
  { text: '返回', width: '25%', value: 'return ' }
]
const operators = [
  { text: '加', width: '25%', value: '+', space: true },
  { text: '减', width: '25%', value: '-', space: true },
  { text: '乘', width: '25%', value: '*', space: true },
  { text: '除', width: '25%', value: '/', space: true },
  { text: '递加', width: '25%', value: '++', space: true },
  { text: '递减', width: '25%', value: '--', space: true },
  { text: '=', width: '25%', value: '=', space: true },
  { text: '+=', width: '25%', value: '+=', space: true },
  { text: '-=', width: '25%', value: '-=', space: true },
  { text: '*=', width: '25%', value: '*=', space: true },
  { text: '/=', width: '25%', value: '/=', space: true },
  { text: '%=', width: '25%', value: '%=', space: true }
]

const insertField = (info, field) => {
  const start = form.formulaZn.slice(0, lastCursor.value)
  const value = start.endsWith('.') ? field.name : ` ${info.name}.${field.name}`
  form.formulaZn = `${start}${value}${form.formulaZn.slice(lastCursor.value)}`
}

const insertFunction = (func) => {
  const params = func.params.map((item) => item.value)
  form.formulaZn += `${func.value}( ${params.join(', ')} )`
}

const insertKey = (key) => {
  form.formulaZn += ` ${key.value}`
}

const formulaDetailRef = ref(null)
const showScript = () => {
  const formula = getScript(form.formulaZn)
  formulaDetailRef?.value?.open(formula, '查看脚本')
}

const formulaMockRef = ref(null)
const mock = () => {
  const formula = getScript(form.formulaZn)
  formulaMockRef.value?.open(classInfos.value, formula)
}

defineExpose({ open })
</script>

<style lang="less" scoped>
:deep(.ant-collapse) {
  border: none;
  .ant-collapse-item {
    border: none;
    background: #edeff3;
    .ant-collapse-header {
      padding: 5px 16px !important;
    }
    .ant-collapse-content {
      border: none;
      .ant-collapse-content-box {
        padding: 5px 0 !important;
      }
    }
  }
}

.formula-generator {
  width: 100%;
  height: 600px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  &__editor {
    flex-basis: 200px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    &-title {
      display: flex;
      align-items: center;
      padding: 5px 10px;
    }
    &-content {
      flex: 1;
      overflow: auto;
    }
  }
  &__tool {
    flex: 1;
    display: flex;
    height: 360px;
    &-entity {
      width: 25%;
      border-top: 1px solid #d9d9d9;
      border-right: 1px solid #d9d9d9;
      padding: 1px;
      display: flex;
      flex-direction: column;
      .field-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        cursor: pointer;
        user-select: none;
        border-bottom: 1px solid #e0e0e0;
        &:last-child {
          border-bottom: none;
        }
        &:hover {
          background: #ebedf1;
        }
      }
    }
    &-function {
      width: 25%;
      border-top: 1px solid #d9d9d9;
      border-right: 1px solid #d9d9d9;
      padding: 1px;
      display: flex;
      flex-direction: column;
      .function-item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 5px 16px;
        cursor: pointer;
        user-select: none;
        &:hover {
          background: #ebedf1;
        }
        &__value {
          color: rgba(0, 0, 0, 0.85);
          font-size: 16px;
        }
        &__title {
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
        }
      }
    }
    &-variable {
      width: 50%;
      border-top: 1px solid #d9d9d9;
      padding: 1px;
      display: flex;
      flex-direction: column;
    }
    &-title {
      padding: 5px 10px;
      /*background: #eceff7;*/
      background: white;
      border-bottom: 1px solid #d9d9d9;
      user-select: none;
      display: flex;
      justify-content: space-between;
    }
    &-content {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
    .variable-wrapper {
      padding: 5px;
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      border-bottom: 1px solid #d9d9d9;
      display: flex;
      flex-direction: column;
      position: relative;
      .variable-header {
        background: #ebedf1;
        display: flex;
        justify-content: space-between;
        padding: 5px 16px;
        user-select: none;
        position: absolute;
        left: 5px;
        right: 5px;
      }
      .variable-body {
        overflow: auto;
        margin-top: 31px;
      }
      .variable-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 16px;
        cursor: pointer;
        user-select: none;
        &:hover {
          background: #ebedf1;
        }
        &__name {
          width: 40%;
        }
        &__type {
          width: 30%;
          text-align: center;
        }
        &__value {
          width: 30%;
          text-align: center;
        }
      }
    }
    .keyboard-wrapper {
      padding: 5px;
      flex: 1;
      .keyboard {
        display: flex;
        flex-wrap: wrap;
        border-top: 1px solid #d9d9d9;
        border-left: 1px solid #d9d9d9;
        .key-item {
          height: 30px;
          border-right: 1px solid #d9d9d9;
          border-bottom: 1px solid #d9d9d9;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          user-select: none;
          &:hover {
            background: rgba(24, 143, 253, 0.2);
            color: rgb(24, 143, 253);
          }
          &:active {
            background: rgb(24, 143, 253);
            color: white;
          }
        }
      }
    }
  }
  .data_type {
    &__string {
      color: rgba(40, 132, 227, 1);
      background: rgba(40, 132, 227, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__integer {
      color: rgb(5, 99, 43);
      background: rgba(5, 99, 43, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__double,
    &__float {
      color: rgb(63, 176, 218);
      background: rgba(63, 176, 218, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__boolean {
      color: rgba(123, 94, 186, 1);
      background: rgba(123, 94, 186, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__date {
      color: rgb(251, 193, 40);
      background: rgba(251, 193, 40, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__bigDecimal {
      color: rgb(227, 139, 82);
      background: rgba(227, 139, 82, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__list {
      color: rgb(136, 117, 104);
      background: rgba(92, 56, 34, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__unknown {
      color: rgb(239, 26, 147);
      background: rgba(239, 26, 147, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
  }
}
</style>
