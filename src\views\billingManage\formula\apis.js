import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/watershare/waterShareFormulaBaseInfo/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/watershare/waterShareFormulaBaseInfo/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareFormulaBaseInfo/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareFormulaBaseInfo/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareFormulaBaseInfo/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/watershare/waterShareFormulaBaseInfo/edit',
    data
  })
}
export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/watershare/waterShareFormulaBaseInfo/deleteBatch',
    params
  })
}

export const enable = (id) => {
  return request({
    method: 'post',
    url: `/biz/watershare/waterShareFormulaBaseInfo/enable/${id}`
  })
}

export const disable = (id) => {
  return request({
    method: 'post',
    url: `/biz/watershare/waterShareFormulaBaseInfo/disable/${id}`
  })
}

// 水电分摊-公式编辑的实体类-根据key数组查询实体信息
export const queryEntityByKeys = (keys) => {
  return request({
    method: 'get',
    url: '/bas/waterShareFormulaEntity/queryEntityByKeys',
    params: { keys }
  })
}
// 水电分摊-公式编辑的实体类-通过类路径查询实体信息
export const queryEntityByClassPath = (classPath) => {
  return request({
    method: 'get',
    url: '/bas/waterShareFormulaEntity/queryEntityByClassPath',
    params: { classPath }
  })
}

// 水电分摊-公式模拟验证
export const simulate = (data) => {
  return request({
    method: 'post',
    url: '/formula/simulate',
    data
  })
}
