<template>
  <div>
    <div class="flex items-center mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新增
      </a-button>
      <a-form layout="inline" ref="formRef" class="!ml-[40px]" autocomplete="off">
        <a-form-item label="字典名称">
          <s-input
            v-model="params.dictName"
            placeholder="搜索字典名称"
            @keyup.enter="onTableChange({ current: 1, pageSize: pagination.pageSize })"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <a-form-item label="字典编码">
          <s-input
            v-model="params.dictCode"
            placeholder="搜索字典编码"
            @keyup.enter="onTableChange({ current: 1, pageSize: pagination.pageSize })"
            @input="handleInput"
          ></s-input>
        </a-form-item>
      </a-form>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleConfig(record)">字典配置</span>
          <a-popconfirm title="是否确认删除？" ok-text="确认" cancel-text="取消" @confirm="handleRemove(record)">
            <span class="primary-btn">删除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <edit-dict ref="editDictRef" @refresh="refreshData"></edit-dict>
    <config-dict ref="configDictRef"></config-dict>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message } from 'ant-design-vue'
import { getDictList, deleteDict } from './apis'
import EditDict from './components/EditDict.vue'
import ConfigDict from './components/ConfigDict.vue'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  dictName: '',
  dictCode: ''
})

const columns = [
  { title: '字典名称', dataIndex: 'dictName' },
  { title: '字典编码', dataIndex: 'dictCode' },
  { title: '描述', dataIndex: 'description' },
  { title: '操作', dataIndex: 'action', width: 200 }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getDictList)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const editDictRef = ref()
const handleAdd = () => {
  editDictRef.value.open()
}
const handleEdit = (row) => {
  editDictRef.value.open({
    id: row.id,
    dictName: row.dictName,
    dictCode: row.dictCode,
    description: row.description
  })
}

const configDictRef = ref()
const handleConfig = (row) => {
  configDictRef.value.open(row)
}

const refreshData = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const handleRemove = async (data) => {
  await deleteDict({ id: data.id })
  message.success('删除成功')
  let pageNo = pagination.value.current
  if (pagination.value.pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

onMounted(() => {
  onTableChange()
})
</script>
