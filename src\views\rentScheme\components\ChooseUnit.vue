<template>
  <a-modal
    v-model:open="visible"
    title="选择租赁单元"
    width="800px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center mb-[16px]">
      <s-input v-model="params.name" placeholder="搜索租赁单元名称" @input="handleInput"></s-input>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: '50vh' }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'detailAddress'">
          {{ record.province }}{{ record.city }}{{ record.district }}{{ record.detailAddress }}
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit.js'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message } from 'ant-design-vue'
import { renderDictTag } from '@/utils/render'

const emit = defineEmits(['updateUnitList'])

const visible = ref(false)
const open = (list) => {
  selectedRowKeys.value = list.map((item) => item.id)
  selectedRows.value = list
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
}

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getLeaseUnitList)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const params = reactive({
  name: ''
})

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 150, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 200, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 120 },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', width: 120 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 120, ellipsis: true },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_BaseStatus', 'dot')
  },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 150, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权用途', dataIndex: 'propertyUse_dictText', width: 120 },
  { title: '所属项目', dataIndex: 'wyProject_dictText', width: 120 },
  { title: '单元编码', dataIndex: 'number', width: 200 }
]

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const handleConfirm = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择租赁单元')
    return
  }
  emit('updateUnitList', [...selectedRows.value])
  handleCancel()
}

const handleCancel = () => {
  params.name = ''
  list.value = []
  clearSelection()
  visible.value = false
}

defineExpose({ open })
</script>
