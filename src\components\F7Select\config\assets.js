import { getF7List } from '@/views/assets/manage/apis'
import { renderDict, renderDictTag } from '@/utils/render'
import { getF7List as projectF7 } from '@/views/projects/apis.js'
import { isOrNotDic } from '@/store/modules/dict.js'
import areaList from '@/json/region.json'
import { getQueryWyBuildingByMainId, getQueryWyFloorByMainId } from '@/views/assets/manage/apis.js'
const wyBuildingPar = reactive({ id: '' })
const getQueryWyBuildingByMainIdFunc = computed(() => {
  wyBuildingPar.id
  return () => getQueryWyBuildingByMainId(wyBuildingPar)
})
const wyFloorPar = reactive({ id: '' })
const getQueryWyFloorByMainIdFunc = computed(() => {
  wyFloorPar.id
  return () => getQueryWyFloorByMainId(wyFloorPar)
})
export default {
  modalTitle: '选择资产',
  request: getF7List,
  params: {},
  rowKey: 'id',
  displayKey: 'number',
  keywordKey: 'number',
  keywordPlaceholder: '搜索资产编号',
  clearIgnoreKeys: ['status'],
  scrollX: 2000,
  columns: [
    { title: '资产名称', dataIndex: 'name', width: 180, fixed: true },
    { title: '权证号', dataIndex: 'ownerNumber', ellipsis: true },
    { title: '产权用途', dataIndex: 'propertyUse' },
    {
      title: '使用权类型',
      dataIndex: 'landNature',
      customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
    },
    { title: '地址', dataIndex: 'allAddress', ellipsis: true },
    {
      title: '单据审核状态',
      dataIndex: 'status',
      width: 120,
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
    },
    {
      title: '业务状态',
      dataIndex: 'bizStatus',
      customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_BizStatus')
    },
    { title: '建筑面积', dataIndex: 'structureArea' },
    { title: '宗地面积', dataIndex: 'floorArea' },
    {
      title: '房产类型',
      dataIndex: 'houseType',
      customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_HouseType')
    },
    {
      title: '资产类型',
      dataIndex: 'assetsType',
      customRender: ({ text }) => renderDict(text, 'CT_BAS_AssetsType')
    },
    { title: '归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
    { title: '权属公司', dataIndex: 'ownerCompany_dictText', ellipsis: true },
    { title: '管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
    { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
    { title: '备注', dataIndex: 'remark', ellipsis: true },
    { title: '资产编号', dataIndex: 'number', width: 180 }
  ],
  searchList: [
    { label: '资产编号', name: 'number', type: 'input', placeholder: '请输入资产编号' },
    { label: '权证号', name: 'ownerNumber', type: 'input', placeholder: '请输入权证号' },
    {
      label: '关联项目',
      name: 'wyProject',
      type: 'api-select',
      placeholder: '请选择关联项目',
      asyncFn: () => projectF7({ pageNo: 1, pageSize: 10000 })
    },
    {
      label: '楼栋',
      name: 'wyBuilding',
      type: 'api-select',
      placeholder: '请选择楼栋',
      asyncFn: getQueryWyBuildingByMainIdFunc
    },
    {
      label: '楼层',
      name: 'wyFloor',
      type: 'api-select',
      placeholder: '请选择楼层',
      asyncFn: getQueryWyFloorByMainIdFunc
    },
    { label: '区域', name: 'pcaCode', type: 'cascader', placeholder: '请选择区域', list: areaList },
    {
      label: '租金归集公司',
      name: 'collectionCompany',
      type: 'companySelect',
      companyType: 'all',
      placeholder: '请选择租金归集公司'
    },
    {
      label: '资产权属公司',
      name: 'ownerCompany',
      type: 'companySelect',
      companyType: 'all',
      placeholder: '请选择资产权属公司'
    },
    { label: '物业管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择物业管理公司' },
    { label: '资产类型', name: 'assetsType', type: 'dic', placeholder: '请选择资产类型', code: 'CT_BAS_AssetsType' },
    {
      label: '业务状态',
      name: 'bizStatus',
      type: 'dic',
      placeholder: '请选择业务状态',
      code: 'CT_BASE_ENUM_HouseOwner_BizStatus'
    },
    {
      label: '单据审核状态',
      name: 'status',
      type: 'dic',
      placeholder: '请选择单据审核状态',
      code: 'CT_BASE_ENUM_AuditStatus'
    },
    {
      label: '取得来源',
      name: 'acquisitionMethod',
      type: 'dic',
      placeholder: '请选择取得来源',
      code: 'CT_BAS_AcquisitionMethod'
    },
    {
      label: '产权情况',
      name: 'propertyRightStatus',
      type: 'dic',
      placeholder: '请选择产权情况',
      code: 'CT_BASE_ENUM_HouseOwner_PropertyRightStatus'
    },
    { label: '产权用途', name: 'propertyUse', type: 'dic', placeholder: '请选择产权用途', code: 'CT_BAS_PropertyUse' },
    {
      label: '使用权类型',
      name: 'landNature',
      type: 'dic',
      placeholder: '请选择使用权类型',
      code: 'CT_BAS_LandNature'
    },
    {
      label: '土地建设情况',
      name: 'landConstructionSituation',
      type: 'dic',
      placeholder: '请选择土地建设情况',
      code: 'CT_BAS_LandCS'
    },
    {
      label: '房产类型',
      name: 'houseType',
      type: 'dic',
      placeholder: '请选择房产类型',
      code: 'CT_BASE_ENUM_HouseOwner_HouseType'
    },
    {
      label: '建筑结构',
      name: 'buildStructrue',
      type: 'dic',
      placeholder: '请选择房产类型',
      code: 'CT_BAS_BuildStructrue'
    },
    { label: '户型', name: 'houseModel', type: 'input', placeholder: '请输入户型' },
    {
      label: '消防等级',
      name: 'firefightingRate',
      type: 'dic',
      placeholder: '请选择消防等级',
      code: 'CT_BAS_FirefightingRate'
    },
    {
      label: '房屋安全等级',
      name: 'houseSafeRate',
      type: 'dic',
      placeholder: '请选择房屋安全等级',
      code: 'CT_BAS_HouseSafeRate'
    },
    { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
    {
      label: '创建人',
      name: 'createBy',
      type: 'userSelect',
      placeholder: '请选择创建人'
    },
    { label: '最近修改时间', name: 'updateTime', type: 'date', placeholder: '请选择最近修改时间' },
    {
      label: '最近修改人',
      name: 'updateBy',
      type: 'userSelect',
      placeholder: '请选择最近修改人'
    },
    { label: '审核时间', name: 'auditTime', type: 'date', placeholder: '请选择审核时间' },
    {
      label: '审核人',
      name: 'auditBy',
      type: 'userSelect',
      placeholder: '请选择审核人'
    },
    { label: '详细地址', name: 'detailAddress', type: 'input', placeholder: '请输入详细地址' },
    { label: '权证获得日期', name: 'warrantsDate', type: 'date', placeholder: '请选择权证获得日期' },
    {
      label: '房地权证合一',
      name: 'isUnionCertificate',
      type: 'select',
      placeholder: '请选择房地权证合一',
      list: isOrNotDic
    },
    { label: '建筑年份', name: 'buildYear', type: 'year', format: 'YYYY', placeholder: '请选择建筑年份' }
  ]
}
