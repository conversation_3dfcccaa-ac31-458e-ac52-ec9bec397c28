<template>
  <div>
    <a-tabs v-model:active-key="params.logType">
      <a-tab-pane key="4" tab="异常日志"></a-tab-pane>
      <a-tab-pane key="1" tab="登录日志"></a-tab-pane>
      <a-tab-pane key="2" tab="操作日志"></a-tab-pane>
    </a-tabs>
    <a-form layout="inline" class="!mb-[16px] !mt-[24px]">
      <a-form-item label="日志">
        <a-input v-model:value="params.keyWord" placeholder="搜索日志" @input="handleInput"></a-input>
      </a-form-item>
      <a-form-item label="创建时间">
        <a-range-picker v-model:value="params.dateRange" value-format="YYYY-MM-DD" />
      </a-form-item>
      <a-form-item label="操作类型" v-if="params.logType === '2'">
        <dict-select v-model="params.operateType" code="operate_type" width="200px"></dict-select>
      </a-form-item>
      <a-form-item>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
      </a-form-item>
    </a-form>
    <section v-if="params.logType === '4'">
      <a-table
        :data-source="list"
        :columns="exceptionColumns"
        :loading="tableLoading"
        :pagination="pagination"
        :scroll="{ y: tableHeight, x: 1500 }"
        row-key="id"
        v-model:expanded-row-keys="expandedRowKeys"
        @change="onTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'logContent'">
            <div class="line-clamp-2" :title="record.logContent">{{ record.logContent }}</div>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          {{ record.requestParam }}
        </template>
      </a-table>
    </section>
    <section v-else-if="params.logType === '1'">
      <a-table
        :data-source="list"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        :scroll="{ y: tableHeight }"
        @change="onTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'logContent'">
            <div class="line-clamp-2" :title="record.logContent">{{ record.logContent }}</div>
          </template>
        </template>
      </a-table>
    </section>
    <section v-else>
      <a-table
        :data-source="list"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        :scroll="{ y: tableHeight }"
        row-key="id"
        v-model:expanded-row-keys="expandedRowKeys"
        @change="onTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'logContent'">
            <div class="line-clamp-2" :title="record.logContent">{{ record.logContent }}</div>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <div class="mb-[10px]">
            <strong class="mr-[10px]">请求方法:</strong>
            <span>{{ record.method }}</span>
          </div>
          <div>
            <strong class="mr-[10px]">请求参数:</strong>
            <span>{{ record.requestParam }}</span>
          </div>
        </template>
      </a-table>
    </section>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { page } from './apis'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  logType: '4', // 1=登录日志 2操作日志 4=异常日志
  operateType: '',
  keyWord: '',
  createTime_begin: '',
  createTime_end: '',
  dateRange: []
})

const columnList = [
  { title: '日志内容', dataIndex: 'logContent' },
  { title: '操作人', dataIndex: 'username', customRender: ({ record }) => `${record.username}(账号:${record.userid})` },
  { title: 'IP', dataIndex: 'ip' },
  { title: '耗时(ms)', dataIndex: 'costTime' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '客户端类型', dataIndex: 'clientType_dictText' },
  { title: '操作类型', dataIndex: 'operateType_dictText' }
]
const columns = computed(() => {
  if (params.logType === '2') {
    return columnList
  }
  return columnList.slice(0, 3).concat(columnList.slice(4, 6))
})

const exceptionColumns = [
  { title: '异常标题', dataIndex: 'logContent' },
  { title: '请求地址', dataIndex: 'requestUrl' },
  { title: '请求参数', dataIndex: 'method' },
  { title: '操作人', dataIndex: 'username', customRender: ({ record }) => `${record.username}(账号:${record.userid})` },
  { title: 'IP', dataIndex: 'ip' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '客户端类型', dataIndex: 'clientType_dictText' }
]

const expandedRowKeys = ref([])

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

watch(
  () => params.logType,
  () => {
    expandedRowKeys.value = []
    onTableChange()
  },
  {
    immediate: true
  }
)
</script>
