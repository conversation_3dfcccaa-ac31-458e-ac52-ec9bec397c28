<template>
  <div class="flex items-center justify-between mb-[16px]">
    <div class="flex items-center">
      <a-button>
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <!-- <s-input v-model="keyword"></s-input> -->
    </div>
    <columns-set ref="columnsRef" :default-columns="defaultColumns"></columns-set>
  </div>
  <a-table
    :data-source="detail.writeOffList"
    :columns="columns"
    :pagination="false"
    :scroll="{ x: 3000, y: '50vh' }"
  ></a-table>
</template>

<script setup>
import { renderMoney } from '@/utils/render'

defineProps({
  detail: { required: true, type: Object }
})

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '单据类型', dataIndex: 'billType' },
  { title: '单据日期', dataIndex: 'billDate' },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText' },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '客户', dataIndex: 'customer_dictText' },
  { title: '合同号', dataIndex: 'contractNum' },
  { title: '业务员', dataIndex: 'operator_dictText' },
  { title: '业务部门', dataIndex: 'operatorDepart_dictText' },
  { title: '服务处', dataIndex: 'serviceCenter' },
  { title: '车位号', dataIndex: 'carportNum' },
  { title: '停车场', dataIndex: 'park' },
  { title: '本次核销金额', dataIndex: 'thisConsumedAmt', customRender: ({ text }) => renderMoney(text) },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '应收开始日期', dataIndex: 'receiveBeginDate' },
  { title: '应收结束日期', dataIndex: 'receiveEndDate' },
  { title: '核销人', dataIndex: 'consumedPerson_dictText' },
  { title: '核销方式', dataIndex: 'consumedType_dictText' },
  { title: '核销时间', dataIndex: 'consumedTime' },
  { title: '本次核销记录标识', dataIndex: 'thisConsumedMark', width: 200 }
]
const columnsRef = ref()
const columns = computed(() => columnsRef.value?.columns)
</script>
