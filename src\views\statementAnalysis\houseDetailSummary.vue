<template>
  <div class="house-detail-summary">
    <div class="flex items-center mb-[16px]">
      <a-form layout="inline">
        <a-form-item label="物业管理公司">
          <company-select v-model="params.manageCompanyList" type="all" disabled width="240px"></company-select>
        </a-form-item>
        <a-form-item label="查询截止日期">
          <a-date-picker
            v-model:value="params.endTime"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="loadData"
            :allow-clear="false"
          ></a-date-picker>
        </a-form-item>
      </a-form>
      <a-button @click="loadData">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <filter-more
        :params="params"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        :clear-ignore-keys="['manageCompanyList', 'endTime']"
        :stat-ignore-keys="['manageCompanyList', 'endTime']"
        @query="loadData"
      ></filter-more>
    </div>
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="false"
      :scroll="{ y: 'calc(100vh - 300px)' }"
      bordered
    >
      <template #summary>
        <a-table-summary-row>
          <a-table-summary-cell>合计</a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell>{{ totalLeaseArea }}</a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
        </a-table-summary-row>
      </template>
    </a-table>
    <lease-unit-detail ref="leaseUnitDetailRef" readonly></lease-unit-detail>
  </div>
</template>

<script setup>
import { houseDetailList } from './apis'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import { renderDictTag } from '@/utils/render'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import { getF7List } from '@/views/leaseUnit/manage/apis/leaseUnit'

const { userInfo } = useUserStore()
const getLeaseUnitList = () => getF7List({ pageNo: 1, pageSize: 5000, manageCompany: userInfo.value.currentCompany })
const params = reactive({
  manageCompanyList: userInfo.value.currentCompany,
  ownerCompanyList: '',
  houseOwnerNumberList: '',
  leaseUseList: '',
  leaseUnitNumberList: '',
  areaManagerList: '',
  bizStatusList: '',
  endTime: dayjs().format('YYYY-MM-DD')
})

const searchList = [
  { label: '权属单位', name: 'ownerCompanyList', type: 'company-select', companyType: 'all' },
  { label: '租赁用途', name: 'leaseUseList', type: 'dict-select', code: 'CT_BAS_LeaseUse' },
  {
    label: '租赁单元编码',
    name: 'leaseUnitNumberList',
    type: 'api-select',
    asyncFn: getLeaseUnitList,
    fieldNames: { label: 'number', value: 'number' }
  },
  { label: '房屋权证编码', name: 'houseOwnerNumberList', type: 'input' },
  { label: '片区管理员', name: 'areaManagerList', type: 'user-select' },
  { label: '业务状态', name: 'bizStatusList', type: 'dict-select', code: 'CT_BASE_ENUM_LeaseUnit_BizStatus' }
]

const loading = ref(false)
const tableData = ref([])
const totalLeaseArea = ref(0)

const loadData = async () => {
  if (loading.value) return
  try {
    tableData.value = []
    loading.value = true
    const tempParams = {}
    for (const key in params) {
      if (key === 'endTime') {
        tempParams[key] = params[key]
      } else {
        tempParams[key] = params[key] && params[key].length ? [params[key]] : []
      }
    }
    const { result } = await houseDetailList(tempParams)
    totalLeaseArea.value = result.totalLeaseArea
    result.dataList.forEach((item) => {
      item.leaseUnits.forEach((leaseUnit, leaseUnitIndex) => {
        if (leaseUnitIndex === 0) {
          leaseUnit.rowSpan = item.leaseUnits.length
        } else {
          leaseUnit.rowSpan = 0
        }
        leaseUnit.houseNumber = item.number
        leaseUnit.houseName = item.name
        tableData.value.push(leaseUnit)
      })
    })
  } finally {
    loading.value = false
  }
}

const leaseUnitDetailRef = ref()

const customCell = (record) => {
  return {
    style: {
      cursor: 'pointer'
    },
    onDblclick: () => {
      leaseUnitDetailRef.value.open(record)
    }
  }
}

const columns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
  {
    title: '房产权证编号',
    dataIndex: 'houseNumber',
    customCell: (record) => {
      return {
        rowSpan: record.rowSpan
      }
    }
  },
  {
    title: '资产权证名称',
    dataIndex: 'houseName',
    customCell: (record) => {
      return {
        rowSpan: record.rowSpan
      }
    }
  },
  { title: '租赁单元编号', dataIndex: 'number', customCell },
  { title: '租赁单元名称', dataIndex: 'name', customCell },
  { title: '租赁单元地址', dataIndex: 'detailAddress', customCell },
  {
    title: '租赁状态',
    dataIndex: 'bizStatus',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_LeaseUnit_BizStatus', 'dot'),
    customCell
  },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', customCell },
  { title: '租赁用途', dataIndex: 'leaseUseName', customCell },
  { title: '归集公司', dataIndex: 'ownerCompany', customCell },
  { title: '权属公司', dataIndex: 'collectionCompany', customCell },
  { title: '片区管理员', dataIndex: 'areaManagerName', customCell }
]

const height = ref('50vh')

let page // BasicLayout右侧的router-view
let table // a-table
let tableHead // a-table的表格头

function getDom() {
  page = document.getElementById('basic-router-view')
  table = page.querySelector('.ant-table-wrapper')
  tableHead = table.querySelector('.ant-table-thead')
}

function getHeight() {
  const tableHeadHeight = tableHead.getBoundingClientRect().height // 表格头的高度
  const tableTop = table.getBoundingClientRect().top // a-table在浏览器可视区域里的top值
  // 最后减去的2px是ant-table的border
  height.value = `${document.documentElement.offsetHeight - tableTop - tableHeadHeight - 32 - 4}px`
}

onMounted(() => {
  loadData()
  getDom()
  getHeight()
  window.addEventListener('resize', getHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', getHeight)
})
defineExpose({ open })
</script>

<style lang="less" scoped>
.house-detail-summary {
  :deep(.ant-table-wrapper) {
    .ant-table-tbody > tr.ant-table-row:hover > td {
      background: transparent;
    }
    .ant-table-tbody > tr > td.ant-table-cell-row-hover {
      background: transparent;
    }
    .ant-table-tbody > tr.ant-table-row > td:hover {
      background-color: #eaf0fe;
    }
    .ant-table-tbody {
      .sum-row td {
        background-color: rgba(var(--color-primary-rgb), 0.1) !important;
      }
    }
    .ant-table-summary {
      .ant-table-cell {
        background-color: rgba(var(--color-primary-rgb), 0.1);
      }
    }
  }
}
</style>
