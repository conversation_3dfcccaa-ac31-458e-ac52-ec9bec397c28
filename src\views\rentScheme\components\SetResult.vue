设置招标结果
<template>
  <a-modal
    v-model:open="visible"
    title="招标结果"
    width="560px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-radio-group v-model:value="form.status">
      <a-radio value="SuccessBidding">中标</a-radio>
      <a-radio value="FailedBidding">流标</a-radio>
    </a-radio-group>
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '68px' } }"
      label-align="left"
      class="!mt-[24px]"
      autocomplete="off"
    >
      <a-form-item label="中标客户" name="customer" v-if="form.status === 'SuccessBidding'">
        <common-select
          v-model="form.customer"
          :options="customerList"
          :field-names="{ label: 'name', value: 'id' }"
        ></common-select>
      </a-form-item>
      <a-form-item label="流标说明" name="remark" v-else>
        <a-input v-model:value="form.remark" :maxlength="200"></a-input>
      </a-form-item>
    </a-form>
    <files-upload v-model="form.attachmentIds"></files-upload>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { setSuccessResult, setFailureResult, queryRentSchemeCustomers } from '../apis'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'

const emit = defineEmits(['refresh'])

const { userInfo } = useUserStore()

const visible = ref(false)
const open = (id) => {
  form.parent = id
  form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
  form.operator = userInfo.value.id
  visible.value = true
  loadCustomerList()
}

const customerList = ref([])
const loadCustomerList = async () => {
  const { result } = await queryRentSchemeCustomers({ id: form.parent })
  customerList.value = result
}

const form = reactive({
  parent: '',
  status: 'SuccessBidding',
  operator: '',
  bizDate: '',
  customer: '',
  attachmentIds: '',
  remark: ''
})
const rules = {
  customer: [{ required: true, message: '请选择中标客户', trigger: 'none' }],
  remark: [{ required: true, message: '请输入流标说明', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.name = form.status === 'SuccessBidding' ? '中标' : '流标'
    form.status === 'SuccessBidding' ? await setSuccessResult(form) : await setFailureResult(form)
    handleCancel()
    message.success('处理成功')
    emit('refresh', form.parent)
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.status = 'SuccessBidding'
  form.customer = ''
  form.attachmentIds = ''
  form.remark = ''
  visible.value = false
}

defineExpose({ open })
</script>
