<template>
  <a-modal
    v-model:open="visible"
    title="bpmn20.xml文件导入"
    width="460px"
    wrap-class-name="common-modal common-import-modal"
    :confirm-loading="confirmLoading"
    :ok-button-props="{ disabled: !fileData.name }"
    :mask-closable="false"
    ok-text="导入"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="import-box mb-[15px]">
      <img src="@/assets/svgs/upload.svg" width="32" height="32" class="mb-[8px]" />
      <span class="text-tertiary">点击或拖拽文件到这里上传</span>
      <span class="text-tertiary">仅允许导入“bpmn20.xml”格式文件！</span>
      <div
        class="absolute z-[10] left-[0] top-[0] w-full h-full px-[16px] pt-[20px] bg-white rounded-[8px]"
        v-if="fileData.name"
      >
        <div class="flex items-center mb-[8px]">
          <img src="@/assets/svgs/unknown-file.svg" width="36" height="36" class="mr-[8px]" />
          <div>
            <div class="line-clamp-1 mb-[4px]">{{ fileData.name }}</div>
            <small class="text-[12px] text-tertiary">{{ fileData.size }}</small>
          </div>
        </div>
        <div class="text-error cursor-pointer" @click="resetFileData" v-if="fileData.error">重新上传</div>
        <div class="flex items-center justify-between" v-if="fileData.progress">
          <div class="w-[294px] h-[8px] rounded-[4px] bg-[#eaf0fe]">
            <div :style="{ width: fileData.progress || '0' }" class="h-full bg-primary rounded-[4px]"></div>
          </div>
          <span class="text-success">
            {{ fileData.progress === '100%' ? '上传完成' : `上传中${fileData.progress}` }}
          </span>
        </div>
      </div>
      <input
        type="file"
        class="w-full h-full absolute left-[0] top-[0] cursor-pointer opacity-0"
        accept=".xml"
        @change="onchange"
      />
    </div>
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '100px' } }" autocomplete="off">
      <a-form-item label="流程名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入流程名称" />
      </a-form-item>
      <a-form-item label="流程使用公司" name="company">
        <company-select v-model="form.company" placeholder="请选择流程使用公司"></company-select>
      </a-form-item>
      <a-form-item label="流程业务单据" name="classNameKey">
        <a-select v-model:value="form.classNameKey" placeholder="请选择流程业务单据">
          <a-select-option v-for="f in flowBizBillList" :key="f.classNameKey" :value="f.classNameKey">
            {{ f.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { importXml } from '../apis'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const flowBizBillList = ref([])
const open = (flowBizBill) => {
  flowBizBillList.value = flowBizBill
  visible.value = true
}

const fileData = reactive({
  name: '',
  size: '',
  progress: '',
  error: false
})

const form = reactive({
  file: '',
  name: '',
  company: '',
  classNameKey: ''
})

const rules = {
  name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
  company: [{ required: true, message: '请选择流程使用公司', trigger: 'change' }],
  classNameKey: [{ required: true, message: '请选择流程业务单据', trigger: 'change' }]
}

const onchange = (e) => {
  const files = e.target.files
  if (!(files && files.length)) return
  const file = files[0]
  form.file = file
  fileData.name = file.name
  fileData.size = getFileSize(file.size)
  fileData.progress = ''
  fileData.error = false
  e.target.value = '' // 清空已选文件
}

const resetFileData = () => {
  fileData.name = ''
  fileData.size = ''
  fileData.progress = ''
  fileData.error = false
}

const getFileSize = (bytes) => {
  const KB = 1024
  const MB = 1024 * 1024
  if (bytes < KB) return `${bytes}b`
  if (bytes >= MB) {
    const sizeInMB = bytes / MB
    return Number.isInteger(sizeInMB) ? `${sizeInMB}mb` : `${sizeInMB.toFixed(2)}mb`
  }
  const sizeInKB = bytes / KB
  return Number.isInteger(sizeInKB) ? `${sizeInKB}kb` : `${sizeInKB.toFixed(2)}kb`
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const formData = new FormData()
    formData.append('file', form.file)
    formData.append('name', form.name)
    formData.append('company', form.company)
    formData.append('classNameKey', form.classNameKey)
    const data = await importXml(formData)
    message.success(data.message)
    confirmLoading.value = false
    handleCancel()
    emit('refresh')
  } catch {
    confirmLoading.value = false
    fileData.progress = ''
    fileData.error = true
  }
}
const handleCancel = () => {
  resetFileData()
  visible.value = false
  form.name = ''
  form.company = ''
  form.classNameKey = ''
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.common-import-modal {
  .ant-radio-wrapper {
    &:last-child {
      margin-right: 0;
    }
    span.ant-radio + * {
      padding-inline-end: 0;
    }
  }
  .import-box {
    height: 106px;
    border-radius: 8px;
    background-color: #f7f8fa;
    border: 1px solid #e6e9f0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
  }
}
</style>
