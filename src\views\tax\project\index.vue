<template>
  <div class="flex flex-col h-full">
    <div class="flex-1 flex content overflow-hidden">
      <div class="left flex flex-col w-[300px]">
        <div class="text-[18px] font-bold mb-[10px]">公司</div>
        <div class="flex-1 overflow-y-auto">
          <div
            v-for="company in companyList"
            :key="company.id"
            :class="['company-item', { active: company.id === activeCompany.id }]"
            @click="selectCompany(company)"
          >
            {{ company.departName }}
          </div>
        </div>
      </div>
      <div class="right flex-1 p-[16px]">
        <div class="text-[18px] font-bold mb-[24px]">{{ activeCompany.departName || '' }}</div>
        <a-tabs v-model:active-key="activeTab" size="large">
          <a-tab-pane key="paymentTypeRate" tab="项目税率">
            <payment-type-rate :manage-company="activeCompany.id" class="pt-[16px]" />
          </a-tab-pane>
          <a-tab-pane key="invoiceLimit" tab="项目发票限额">
            <invoice-limit :manage-company="activeCompany.id" class="pt-[16px]" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getCurrentUserCompanies } from '@/views/system/depart/apis'
import PaymentTypeRate from './paymentTypeRate/index.vue'
import invoiceLimit from './invoiceLimit/index.vue'

const companyList = ref([])
const activeCompany = reactive({})

const activeTab = ref('paymentTypeRate')

onMounted(async () => {
  const { result } = await getCurrentUserCompanies()
  companyList.value = result.companyList || []
  if (result?.companyList?.length) {
    Object.assign(activeCompany, result.companyList[0])
  }
})

const selectCompany = (company) => {
  Object.assign(activeCompany, company)
}
</script>

<style lang="less" scoped>
.content {
  border-radius: 10px;
  border: 1px solid #e6e9f0;
}

.left {
  padding: 16px;
  border-right: 1px solid #e6e9f0;
  flex-shrink: 0; /* 设置为 0 防止被挤压 */
}

.right {
  overflow: hidden;
}

.company-item {
  margin-top: 16px;
  padding: 16px;
  cursor: pointer;
  color: #1d335c;
  transition: background 0.2s;
  background: #f7f8fa;
  border: 1px solid #e6e9f0;
  border-radius: 8px;
  &.active {
    background: #eaf0fe;
    border: 1px solid var(--color-primary);
  }
}
</style>
