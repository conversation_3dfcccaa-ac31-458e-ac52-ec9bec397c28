import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'
import zipPack from 'vite-plugin-zip-pack'

// https://vite.dev/config/
export default defineConfig((env) => {
  const envData = loadEnv(env.mode, __dirname)
  return {
    base: envData.VITE_BASE,
    build: {
      sourcemap: true // 设为true以后，在线上版本报错时，可以看到具体错误
    },
    plugins: [
      vue(),
      zipPack({
        inDir: 'dist', // 默认打包 vite 构建输出目录
        outDir: resolve(__dirname, 'deploy'), // 指定 zip 文件输出目录
        outFileName: 'dist.zip' // 指定 zip 文件名称
      }),
      tailwindcss(),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        dts: false, // dts: 类型定义文件，针对ts项目，才需要做这个配置
        dirs: ['./src/components'], // 针对自己写的模块，进行自动导入，比如自己写的全局组件
        vueTemplate: true,
        eslintrc: {
          // 注意此属性，第一次要设置为true，则项目会根据filepath变量，生成对应的json文件
          // 这是为了解决eslint的报错，否则配置imports之后，eslint会报错如'ref' is undefined
          // 当项目中，已经生成了filepath变量对应的文件时，就将这个enabled设为false，避免每次启动项目都要生成一次
          enabled: false,
          filepath: './.eslintrc-auto-import.json', // 文件名称，按照插件官方文档来
          globalsPropValue: true
        }
      }),
      // 配置src/components里的组件自动导入，而不需要写import语句
      Components({
        dirs: ['./src/components'],
        dts: false,
        resolvers: [
          AntDesignVueResolver({
            importStyle: false
          })
        ]
      })
    ],
    css: {
      preprocessorOptions: {
        less: {
          additionalData: '@import "./src/styles/index.less";'
        }
      }
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    server: {
      port: 5173,
      proxy: {
        [envData.VITE_BASE_URL]: {
          target: envData.VITE_GLOB_DOMAIN_URL.replace(envData.VITE_BASE_URL, ''),
          changeOrigin: true
        }
      }
    }
  }
})
