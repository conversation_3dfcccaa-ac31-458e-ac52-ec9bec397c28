<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="70%"
    title="自定义常用功能"
    @ok="handleOk"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <div class="flex justify-between">
      <div class="left-wrapper w-[70%] pr-[24px]">
        <!-- 锚点导航 -->
        <anchor-tabs :tab-list="navList" height="calc(100vh - 590px)">
          <template v-for="item in navList" :key="item.name" #[item.name]>
            <div class="flex items-center flex-wrap gap-5 w-[100%]">
              <div
                v-for="i in getItemList(item.name)"
                :key="i.id"
                class="w-[31%] flex justify-between items-center p-[10px] bg-[#F5F5F5] mb-[12px] rounded-[8px]"
              >
                <div>
                  <span>{{ i.name }}</span>
                </div>
                <span class="a-icon-plus cursor-pointer hover:text-success" @click="rowAdd(i)"></span>
              </div>
            </div>
          </template>
        </anchor-tabs>
      </div>
      <div class="right-wrapper pl-[24px] w-[30%] border-l border-[#EAEAEA]">
        <div class="text-secondary mb-[22px]">
          <span class="a-icon-tips mr-[5px]"></span>
          <span>拖动可调整显示列顺序</span>
        </div>
        <draggable v-model="customList" handle=".a-icon-move" item-key="id">
          <template #item="{ element, index }">
            <div class="flex justify-between items-center p-[10px] bg-[#F5F5F5] mb-[12px] rounded-[8px]">
              <div>
                <i class="a-icon-move cursor-move text-secondary mr-[5px]"></i>
                <span>{{ element.name }}</span>
              </div>
              <span class="a-icon-remove cursor-pointer hover:text-error" @click="rowDel(index)"></span>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { Modal, message } from 'ant-design-vue'
import draggable from 'vuedraggable'
import { useDictStore } from '@/store/modules/dict'
import { getPage, userCustomFunctionAddBatch } from '@/views/customShortcuts/apis'
const emits = defineEmits('refreshUserList')
const visible = ref(false)
const open = (list = []) => {
  customList.value = list
  visible.value = true
  getPageList()
}
defineExpose({ open })
const store = useDictStore()
// 导航项
const navList = computed(() => {
  if (!store.dict) return []
  if (!Object.keys(store.dict).length) return []
  const dic = store.dict.CT_BASE_ENUM_CustomCommonFunction_Module
  return dic.map((item) => {
    return {
      title: item.title,
      name: item.value
    }
  })
})
const list = ref([])
const getPageList = async () => {
  const { result } = await getPage({ pageNo: 1, pageSize: 10000 })
  list.value = result.records
}
const getItemList = (module) => {
  return list.value.filter((item) => item.module === module)
}
// 添加需要展示的自定义常用功能

const rowAdd = (item) => {
  const urls = customList.value.map((item) => item.customCommonFunction)
  if (urls.includes(item.id)) {
    return message.warning('已有常用功能，请勿重复添加！')
  }
  customList.value.push({
    id: '',
    customCommonFunction: item.id,
    name: item.name
  })
}
const customList = ref([
  { name: '新建合同', id: '1' },
  { name: '新建客户', id: '2' },
  { name: '导入客户', id: '3' },
  { name: '导入合同', id: '4' },
  { name: '添加客户跟进', id: '5' }
])

const rowDel = (index) => {
  Modal.confirm({
    title: '确定删除当前常用工能吗？',
    content: '',
    centered: true,
    onOk: () => {
      customList.value.splice(index, 1)
    }
  })
}
const loading = ref(false)
const handleOk = async () => {
  if (loading.value) return
  loading.value = true
  try {
    const { result, message: msg } = await userCustomFunctionAddBatch(customList.value)
    customList.value = result.map((item) => {
      return {
        id: item.id,
        customCommonFunction: item.customCommonFunction,
        name: item.customCommonFunction_dictText
      }
    })
    message.success(msg)
    emits('refreshUserList')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}
</script>
