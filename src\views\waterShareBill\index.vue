<template>
  <div>
    <a-tabs v-model:active-key="params.status" size="large" @change="refresh">
      <a-tab-pane v-for="item in statusList" :key="item.value" :tab="item.label"></a-tab-pane>
    </a-tabs>
    <div class="flex items-center justify-between my-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleRemove(false)">批量删除</div>
              </a-menu-item>
              <a-menu-item>
                <div class="primary-btn" @click="handleGenerateReceiveBill">生成应收单</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.number"
          placeholder="搜索单据编号"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <span class="primary-btn" v-if="record.status === 'AUDITING'" @click="handleAudit(record)">审核</span>
          <a-dropdown v-else>
            <span class="primary-btn">
              <span>更多</span>
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleUnAudit(record)">反审核</div>
                </a-menu-item>
                <a-menu-item v-if="record.status === 'AUDITOK' && !record.generatedBill">
                  <div class="primary-btn" @click="handleItemGenerateReceiveBill(record)">生成应收单</div>
                </a-menu-item>
                <a-menu-item v-if="record.generatedBill">
                  <div class="primary-btn" @click="handleViewReceiveBill(record)">查看应收单</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleRemove(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail ref="detailRef" @edit="handleEdit" @audit="handleAudit" @refresh="refreshFromDetail"></detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('水电计费单.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { renderDictTag, renderMoney } from '@/utils/render'
import { page, deleteBatch, audit, unAudit, exportExcel, importExcel, generateReceiveBill } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'

const router = useRouter()

const statusList = ref([
  { label: '全部', value: '' },
  { label: '暂存', value: 'TEMP' },
  { label: '已提交', value: 'AUDITING' },
  { label: '已通过', value: 'AUDITOK' },
  { label: '未通过', value: 'AUDITNO' }
])

const params = reactive({
  column: 'number',
  order: 'desc',
  status: undefined,
  number: undefined,
  receiveDate: undefined,
  shareType: undefined,
  operatorDepart: undefined,
  operator: undefined,
  serviceCenter: undefined,
  shareExplain: undefined,
  incomeBelongYm: undefined,
  remark: undefined,
  createTime: undefined
})

const searchList = [
  { label: '应收日期', name: 'receiveDate', type: 'date' },
  { label: '分摊类别', name: 'shareType', type: 'dict-select', code: 'CT_BASE_ENUM_WaterShareBill_ShareType' },
  { label: '经办部门', name: 'operatorDepart', type: 'depart-select' },
  { label: '经办人', name: 'operator', type: 'user-select' },
  { label: '服务处', name: 'serviceCenter', type: 'dict-select', code: 'CT_BAS_ServiceCenter' },
  { label: '分摊说明', name: 'shareExplain', type: 's-input' },
  {
    label: '收入归属年月',
    name: 'incomeBelongYm',
    type: 'date',
    picker: 'month',
    valueFormat: 'YYYY-MM',
    format: 'YYYY-MM'
  },
  { label: '创建时间', name: 'createTime', type: 'date', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: 'left' },
  {
    title: '项目/楼栋/楼层',
    dataIndex: 'project',
    width: 200,
    customRender: ({ record }) => {
      const building = record.wyBuilding_dictText ? `/${record.wyBuilding_dictText}` : ''
      const floor = record.wyFloor_dictText ? `/${record.wyFloor_dictText}` : ''
      return `${record.wyProject_dictText || ''}${building}${floor}`
    }
  },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '分摊类别', dataIndex: 'shareType_dictText', width: 120 },
  { title: '经办人', dataIndex: 'operator_dictText', width: 120 },
  { title: '经办部门', dataIndex: 'operatorDepart_dictText', width: 120 },
  { title: '待摊用量', dataIndex: 'dtUseQuantity', width: 120, customRender: ({ text }) => renderMoney(text, 4) },
  { title: '服务处', dataIndex: 'serviceCenter_dictText', width: 240 },
  { title: '单价(元)', dataIndex: 'price', width: 180, customRender: ({ text }) => renderMoney(text, 6) },
  { title: '待摊总额', dataIndex: 'dtUseTotalAmount', width: 100, customRender: ({ text }) => renderMoney(text) },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 180 },
  { title: '分摊说明', dataIndex: 'shareExplain', width: 200, ellipsis: true },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除水电计费单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
      if (detailRef.value && detailRef.value.visible) {
        detailRef.value.handleClose()
      }
    }
  })
}

const handleAudit = (data) => {
  Modal.confirm({
    title: '确认通过该水电计费单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await audit(data)
      message.success(msg)
      onTableChange(pagination.value)
      if (detailRef.value && detailRef.value.visible) {
        detailRef.value.loadData(data.id)
      }
    }
  })
}

const handleUnAudit = (data) => {
  Modal.confirm({
    title: '确认取消通过该水电计费单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await unAudit(data)
      message.success(msg)
      onTableChange(pagination.value)
      if (detailRef.value && detailRef.value.visible) {
        detailRef.value.loadData(data.id)
      }
    }
  })
}

const generateLoading = ref(false)
// 生成应收单
const handleGenerateReceiveBill = async () => {
  if (generateLoading.value) return
  if (!selectedRowKeys.value.length) {
    message.warning('请至少选择一条数据')
    return
  }
  const list = selectedRows.value.filter((item) => item.status === 'AUDITOK')
  if (list.length === 0) {
    Modal.info({
      title: '系统提示',
      content:
        '只有审核通过，并且还未生成应收单的水电计费单，才可以生成应收单，您本次选择的水电计费单，不符合生成应收单的条件。',
      centered: true
    })
    return
  }
  try {
    generateLoading.value = true
    await generateReceiveBill({ ids: list.map((item) => item.id).join(',') })
    message.success('生成成功')
  } finally {
    generateLoading.value = false
  }
}

// 单条数据生成应收单
const handleItemGenerateReceiveBill = async (data) => {
  if (data.loading) return
  try {
    data.loading = true
    await generateReceiveBill({ ids: data.id })
    message.success('已生成应收单')
    onTableChange(pagination.value)
  } finally {
    data.loading = false
  }
}

// 查看应收单
const handleViewReceiveBill = (data) => {
  sessionStorage.setItem('sourceBillIdFromWaterShareBill', data.id)
  router.push({ path: '/statement/receiveCertificate' })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('水电计费单数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
