<template>
  <div>
    <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">资产基础信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">申请编号：{{ approval.code }}</span>
      <span class="w-[50%]">申请事项：{{ approval.subject }}</span>
      <span class="w-[50%]">创建时间：{{ approval.createTime }}</span>
      <span class="w-[50%]">意向客户：{{ approval.customer }}</span>
      <span class="w-[50%]">手机号：{{ approval.phone }}</span>
      <span class="w-[50%]">意向周期：{{ approval.period }}</span>
      <span class="w-[50%]">客户类型：{{ approval.customerType }}</span>
      <span class="w-[50%]">意向日期：{{ approval.intentDate }}</span>
      <span class="w-[50%]">预约签约日期：{{ approval.prepayDate }}</span>
      <span class="w-[50%]">月租金：{{ approval.monthlyRent }}</span>
      <span class="w-[100%]">其他客户内容：{{ approval.description }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">租赁单元</h4>
    <a-table :columns="columns" :data-source="leaseUnit" :pagination="false" />
  </div>
</template>

<script setup>
const { approval, leaseUnit } = defineProps({
  approval: {
    type: Object,
    required: true
  },
  leaseUnit: {
    type: Array,
    required: true
  }
})

/**
 * 租赁单元表格列配置
 */
const columns = [
  {
    title: '租赁单元名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '租赁用途',
    dataIndex: 'usage',
    key: 'usage'
  },
  {
    title: '租赁面积平方米',
    dataIndex: 'area',
    key: 'area'
  },
  {
    title: '租赁面积',
    dataIndex: 'areaDetail',
    key: 'areaDetail'
  },
  {
    title: '设备重量',
    dataIndex: 'weight',
    key: 'weight'
  },
  {
    title: '月租金',
    dataIndex: 'rent',
    key: 'rent'
  },
  {
    title: '收费公司',
    dataIndex: 'company',
    key: 'company'
  },
  {
    title: '内含公司',
    dataIndex: 'innerCompany',
    key: 'innerCompany'
  },
  {
    title: '开立账户名',
    dataIndex: 'account',
    key: 'account'
  },
  {
    title: '产权联系家',
    dataIndex: 'contact',
    key: 'contact'
  }
]
</script>
