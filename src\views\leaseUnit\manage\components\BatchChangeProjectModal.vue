<template>
  <a-modal
    v-model:open="visible"
    title="批量维护项目"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    width="500px"
    class="common-modal"
    :mask-closable="false"
  >
    <a-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="所属项目" name="wyProjectArray" :rules="[{ required: true, message: '请选择项目' }]">
        <a-cascader
          v-model:value="formData.wyProjectArray"
          :options="projectOptions"
          :field-names="{ label: 'label', value: 'value', children: 'children' }"
          :load-data="loadBuildingFloorData"
          placeholder="请选择项目/楼栋/楼层"
          @change="handleProjectChange"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { projectPage, queryBuilding } from '@/views/projects/apis.js'
import { queryFloor } from '@/views/building/apis/building.js'
import { batchChangeProject } from '../apis/leaseUnit'
import { houseOwnerBatchChangeWyProject } from '@/views/assets/manage/apis'
const { sourceType } = defineProps({
  sourceType: {
    default: 'leaseUnit',
    type: String
  }
})
const emits = defineEmits(['refresh'])

const formRef = ref()
const visible = ref(false)
const loading = ref(false)
const selectedRecords = ref([])
const projectOptions = ref([])

const formData = reactive({
  wyProjectArray: undefined,
  wyProject: undefined,
  wyBuilding: undefined,
  wyFloor: undefined
})

/**
 * 打开弹窗
 */
const open = (records = []) => {
  if (!records || records.length === 0) {
    message.warning('请先选择要维护的租赁单元')
    return
  }

  selectedRecords.value = records

  // 重置表单数据
  formRef.value?.resetFields()
  formData.wyProjectArray = undefined
  formData.wyProject = undefined
  formData.wyBuilding = undefined
  formData.wyFloor = undefined

  // 打开弹窗并加载项目列表
  visible.value = true
  loadProjectList()
}

/**
 * 取消操作
 */
const handleCancel = () => {
  // 重置表单
  formRef.value?.resetFields()

  // 重置数据
  formData.wyProjectArray = undefined
  formData.wyProject = undefined
  formData.wyBuilding = undefined
  formData.wyFloor = undefined
  selectedRecords.value = []
  projectOptions.value = []

  // 关闭弹窗
  visible.value = false

  // 触发刷新事件
  emits('refresh')
}

/**
 * 处理项目楼栋楼层选择变更事件
 * @param {Array} value - 选中的项目楼栋楼层ID数组
 */
const handleProjectChange = (value) => {
  formData.wyProjectArray = value
  ;[formData.wyProject, formData.wyBuilding, formData.wyFloor] = value || []
}

/**
 * 动态加载楼栋和楼层数据
 */
const loadBuildingFloorData = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true

  try {
    const isLoadingBuilding = selectedOptions.length === 1
    const api = isLoadingBuilding ? queryBuilding : queryFloor
    const res = await api({ id: targetOption.value })

    if (res?.result && Array.isArray(res.result)) {
      targetOption.children = res.result.map((item) => ({
        label: item.name,
        value: item.id,
        isLeaf: !isLoadingBuilding
      }))
    } else {
      targetOption.children = []
      if (res?.result === null) {
        message.warning(`暂无${isLoadingBuilding ? '楼栋' : '楼层'}数据`)
      }
    }
  } finally {
    targetOption.loading = false
  }
}

/**
 * 加载项目列表数据
 */
const loadProjectList = async () => {
  const { result } = await projectPage({ status: 'ENABLE', pageSize: 1000 })
  if (result?.records && Array.isArray(result.records)) {
    projectOptions.value = result.records.map((item) => ({
      label: item.name,
      value: item.id,
      isLeaf: false
    }))
  } else {
    projectOptions.value = []
    message.warning('暂无可用项目数据')
  }
}

/**
 * 提交批量维护项目
 */
const handleSubmit = async () => {
  await formRef.value?.validate()

  if (!formData.wyProject) {
    message.warning('请选择项目')
    return
  }

  if (!selectedRecords.value || selectedRecords.value.length === 0) {
    message.warning('请选择要更新的租赁单元')
    return
  }

  loading.value = true
  try {
    const params = {
      wyProject: formData.wyProject,
      wyBuilding: formData.wyBuilding,
      wyFloor: formData.wyFloor
    }
    if (sourceType === 'houseOwner') {
      params.houseOwnerList = selectedRecords.value.map((record) => {
        return { id: record.id }
      })
    } else {
      params.leaseUnitList = selectedRecords.value.map((record) => {
        return { id: record.id }
      })
    }
    const { message: msg } = await (sourceType === 'houseOwner'
      ? houseOwnerBatchChangeWyProject(params)
      : batchChangeProject(params))
    message.success(msg)
  } finally {
    loading.value = false
  }
  handleCancel()
}

defineExpose({
  open
})
</script>
