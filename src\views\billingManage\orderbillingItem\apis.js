import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/bas/orderChargeProject/list',
    params
  })
}
// 租赁单元-f7分页查询
export const getF7List = (params) => {
  return request({
    method: 'get',
    url: '/bas/orderChargeProject/f7list',
    params
  })
}
export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/orderChargeProject/add',
    data
  })
}
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/orderChargeProject/edit',
    data
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/bas/orderChargeProject/deleteBatch?ids=${ids}`
  })
}
export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/orderChargeProject/updateEnableDisableStatus',
    data
  })
}
// 订单计费项阶梯定价明细主表ID查询
export const queryOrderChargeProjectLadderDetailByMainId = (params) => {
  return request({
    method: 'get',
    url: '/bas/orderChargeProject/queryOrderChargeProjectLadderDetailByMainId',
    params
  })
}

// 服务类型list
export const orderChargeProjectServiceTypeList = (params) => {
  return request({
    method: 'get',
    url: '/bas/orderChargeProjectServiceType/list',
    params
  })
}
// 服务类型-新增
export const serviceTypeAdd = (data) => {
  return request({
    method: 'post',
    url: '/bas/orderChargeProjectServiceType/add',
    data
  })
}

// 服务类型-编辑
export const serviceTypeEdit = (data) => {
  return request({
    method: 'post',
    url: '/bas/orderChargeProjectServiceType/edit',
    data
  })
}
// 服务类型-删除
export const serviceTypeDelete = (params) => {
  return request({
    method: 'delete',
    url: '/bas/orderChargeProjectServiceType/delete',
    params
  })
}
// 服务类型-重复校验
export const serviceTypeDictItemCheck = (params) => {
  return request({
    method: 'get',
    url: '/bas/orderChargeProjectServiceType/dictItemCheck',
    params
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/orderChargeProject/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/orderChargeProject/importExcel', data, controller)
}
