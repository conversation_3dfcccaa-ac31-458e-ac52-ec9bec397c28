<!-- 收付款选择弹窗 -->
<template>
  <a-modal
    v-model:open="visible"
    title="选择收付款记录"
    width="1072px"
    :mask-closable="false"
    class="common-modal"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <!-- 搜索区域 -->
    <div class="mb-[12px]">
      <s-input v-model="searchParams.number" placeholder="搜索名称" class="!w-[280px]" @input="handleSearch"></s-input>
      <filter-more
        :params="searchParams"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="handleFilterSearch"
      ></filter-more>
    </div>
    <!-- 已选择提示 -->
    <div class="mb-[12px] px-[12px] py-[8px] bg-blue-50 rounded border border-blue-200" v-if="multiple">
      <span class="text-blue-600">已选择 {{ selectedCount }} 个收付款记录</span>
      <span class="primary-btn ml-2 !text-gray-400" @click="handleClearAll" v-if="selectedCount > 0">清空选择</span>
    </div>
    <!-- 表格区域 -->
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        selectedRows,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_AuditStatus" type="dot"></status-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import { renderDict, renderDictTag } from '@/utils/render'
import { getPage } from '@/views/receivePayRecords/apis.js'
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
const visible = ref(false)
/**
 * 打开选择器
 */
const open = (selectedUnits = []) => {
  onTableChange()
  if (selectedUnits && selectedUnits.length > 0) {
    const selectedIds = selectedUnits.map((unit) => unit.id)
    selectedRowKeys.value = selectedIds
    // 根据 id 从 list 中获取完整的对象数据
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  searchParams.value = params
  visible.value = true
}
defineExpose({ open })
const { multiple, asyncFunc, params } = defineProps({
  multiple: { default: true, type: Boolean },
  asyncFunc: { default: getPage, type: Function },
  params: {
    default: () => {
      return {
        number: ''
      }
    },
    type: Object
  }
})
const emits = defineEmits(['selectChange'])

const searchList = reactive([])
const columns = [
  { title: '单据编号', dataIndex: 'number', width: 150, fixed: true },
  { title: '客户名称', dataIndex: 'customer', width: 150 },
  { title: '收付款公司', dataIndex: 'manageCompany_dictText', width: 200 },
  {
    title: '单据类型',
    dataIndex: 'landNature',
    width: 120,
    customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  },
  { title: '收付金额', dataIndex: 'actualReceiveAmt', minWidth: 80 },
  { title: '手续费', dataIndex: 'serviceCharge', minWidth: 80 },
  { title: '合计金额', dataIndex: 'sumAmt', minWidth: 80 },
  {
    title: '数据状态',
    dataIndex: 'status',
    minWidth: 80,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '核销情况',
    dataIndex: 'bizStatus',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BizStatus')
  },
  {
    title: '来源',
    dataIndex: 'billSource',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BillSource')
  },
  {
    title: '经办人',
    dataIndex: 'operator_dictText',
    minWidth: 80
  },
  { title: '收款日期', dataIndex: 'receiveDate', minWidth: 80 }
]
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(asyncFunc)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams.value })
}
const selectedCount = computed(() => selectedRows.value.length)
const searchParams = ref({})
/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  handleCancel()
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 清空所有选择
 */
const handleClearAll = () => {
  clearSelection()
}

/**
 * 搜索处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 筛选搜索处理
 */
const handleFilterSearch = () => {
  pagination.value.current = 1
  onTableChange()
}
</script>
