<template>
  <a-modal v-model:open="visible" title="功能测试" width="600px" :mask-closable="false" wrap-class-name="common-modal">
    <a-form :model="form" :rules="rules" ref="formRef" autocomplete="off" layout="vertical">
      <a-form-item label="需要测试的值" name="value">
        <a-input v-model:value="form.value" placeholder="请输入需要测试的值"></a-input>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button type="primary" @click="handleTest">测试</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { test } from '../apis'

const visible = ref(false)

const open = (ruleCode) => {
  form.ruleCode = ruleCode
  visible.value = true
}

const form = reactive({
  ruleCode: '',
  value: ''
})

const validateValue = async (_, value) => {
  if (!value) return Promise.resolve()
  try {
    await test({ ruleCode: form.ruleCode, value: encodeURIComponent(value) })
    message.success('测试通过')
  } catch (err) {
    return Promise.reject((err && err.message) || '校验不通过')
  }
}
const rules = {
  value: [{ required: false, validator: validateValue, trigger: 'none' }]
}

const formRef = ref()
const handleTest = () => {
  formRef.value.validate()
}

defineExpose({ open })
</script>
