<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="70%"
    title="编辑公式"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <formula ref="formulaRef" :class-path="classPath" v-model="formulaZn"></formula>
  </a-modal>
</template>
<script setup>
const emits = defineEmits(['change'])
const classPath = ref('')

const formulaZn = ref('')
const visible = ref(false)

const open = (value, key = '') => {
  formulaZn.value = value
  classPath.value = key
  visible.value = true
}
defineExpose({ open })

const handleOk = () => {
  emits('change', formulaZn.value)
  visible.value = false
}
/**
 * 取消选择
 */
const handleCancel = () => {
  formulaZn.value = ''
  visible.value = false
}
</script>
