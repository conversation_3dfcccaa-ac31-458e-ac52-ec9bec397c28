<template>
  <a-modal
    v-model:open="visible"
    class="common-modal"
    width="1200px"
    title="编辑头像"
    :mask-closable="false"
    @ok="handleConfirm"
  >
    <div class="img-cropper-content">
      <div>
        <input
          class="cursor-pointer border border-tertiary rounded-[8px] p-[10px]"
          type="file"
          id="upload"
          accept="image/png, image/jpeg, image/gif, image/jpg"
          @change="uploadingImg($event, 1)"
        />
      </div>
      <div ref="cropperWrapperRef" class="cropper-row-wrapper">
        <div class="cut">
          <vue-cropper
            ref="cropperRef"
            mode="contain"
            :img="customaryUrl"
            :output-size="cropperObj.size"
            :output-type="cropperObj.outputType"
            :info="true"
            :full="cropperObj.full"
            :fixed="cropperObj.fixed"
            :fixed-number="cropperObj.fixedNumber"
            :can-move="cropperObj.canMove"
            :can-move-box="cropperObj.canMoveBox"
            :fixed-box="cropperObj.fixedBox"
            :original="cropperObj.original"
            :auto-crop="cropperObj.autoCrop"
            :auto-crop-width="cropperObj.autoCropWidth"
            :auto-crop-height="cropperObj.autoCropHeight"
            :center-box="cropperObj.centerBox"
            :high="cropperObj.high"
            :max-img-size="cropperObj.max"
            @realTime="realTime"
            @cropMoving="cropMoving"
          ></vue-cropper>
        </div>
        <!--预览 -->
        <div v-if="previewImg.url" :style="previewStyle">
          <div :style="previewImg.div">
            <img :src="previewImg.url" :style="previewImg.img" />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { uploadFile } from '@/apis/common'
const emit = defineEmits(['success'])
const { img, cropWidth, cropHeight } = defineProps({
  // 默认单选
  imgUrl: { type: String, default: '' },
  cropWidth: {
    type: Number,
    default: 1
  },
  cropHeight: {
    type: Number,
    default: 1
  },
  cropType: {
    type: Number,
    default: 0
  },
  img: {
    type: String,
    default: ''
  }
})
// 弹窗可见性
const visible = ref(false)
/**
 * 打开弹窗
 */
const open = () => {
  customaryUrl.value = img
  visible.value = true
}
defineExpose({ open })
const customaryUrl = ref('')
const loading = ref(false)
const previewImg = ref({})

const cropperObj = ref({
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: [cropWidth, cropHeight], // 截图框的宽高比例, 开启fixed生效
  size: 1,
  full: false, // 是否输出原图比例的截图
  outputType: 'png',
  canMove: true, // 上传图片是否可以移动
  fixedBox: false, // 固定截图框大小 不允许改变 false
  original: false,
  canMoveBox: true, // 截图框能否拖动 true
  autoCrop: true, // 是否默认生成截图框 false
  autoCropWidth: 400, // 默认生成截图框宽度 容器的80% 0~max
  autoCropHeight: 400, // 默认生成截图框高度 容器的80% 0~max
  centerBox: true, // 截图框是否被限制在图片里面 false
  high: true,
  max: 99999
  // infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
})
const previewStyle = ref({})

const uploadingImg = (e) => {
  const file = e.target.files[0]
  const reg = /.(gif|jpg|jpeg|png|bmp|GIF|PNG)$/
  if (!reg.test(e.target.value)) {
    this.$message.error('图片必须是.gif,jpeg,jpg,png,bmp中的一种')
    return false
  }
  const reader = new FileReader()
  reader.readAsDataURL(file)
  reader.onload = (e) => {
    const data = e.target.result
    customaryUrl.value = data
  }
}

// 裁剪的图片的实时预览
const cropperWrapperRef = ref()
const realTime = (data) => {
  previewImg.value = data
  // 固定为 this.$refs['cropper-wrapper'].scrollHeight 高度
  previewStyle.value = {
    width: `${data.w}px`,
    height: `${data.h}px`,
    overflow: 'hidden',
    margin: '0',
    zoom: cropperWrapperRef.value.scrollHeight / data.h
  }
}
const cropMoving = () => {}
// 选择确定
const cropperRef = ref()
const handleConfirm = () => {
  const formData = new FormData()
  const fileName = `cut-${new Date().getTime()}.jpg`
  cropperRef.value.getCropBlob(async (blob) => {
    // res 是裁剪后图片的bolb对象
    const files = new File([blob], fileName, { type: 'image/jpg' })
    formData.append('file', files)
    loading.value = true
    try {
      const { message } = await uploadFile(formData)
      emit('success', message)
    } finally {
      loading.value = false
    }
  })
  visible.value = false
}
</script>

<style lang="less" scoped>
.img-cropper-content {
  width: 100%;
  height: 60%;
  .cropper-row-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr; /* 并排显示裁剪区和预览 */
    gap: 20px;
    width: 100%;
    height: 40vh;
    margin: 20px 0;
    .cut {
      width: 40vh;
      height: 40vh;
    }
  }
}
</style>
