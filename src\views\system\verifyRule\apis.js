import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/sys/checkRule/list',
    params
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/sys/checkRule/deleteBatch',
    params
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/sys/checkRule/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/sys/checkRule/edit',
    data
  })
}

export const test = (params) => {
  return request({
    method: 'get',
    url: '/sys/checkRule/checkByCode',
    params,
    showErrorMsg: false
  })
}

export const checkRuleCode = (params) => {
  return request({
    method: 'get',
    url: '/sys/duplicate/check',
    params,
    showErrorMsg: false
  })
}
