<template>
  <a-drawer
    v-model:open="visible"
    title="款项类型详情"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleEdit">编辑</span>
        <span class="primary-btn" @click="handleDelete">删除</span>
        <span class="primary-btn" @click="handleUpdateStatus">
          {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
        </span>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <status-tag :dict-value="detail.status" dict-code="CT_BASE_ENUM_BaseStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h4 class="text-[16px] font-bold mb-[12px]">基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">名称: {{ detail.name }}</span>
        <span class="w-[50%]">款项类型: {{ detail.paymentProperties_dictText }}</span>
        <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-[50%]">合同协议类型: {{ detail.contractType }}</span>
        <span class="w-[50%]">
          是否周期性缴交: {{ detail.periodPayContract ? '是' : detail.periodPayContract === false ? '否' : '' }}
        </span>
        <span class="w-[50%]">是否不开票: {{ detail.noInvoice ? '是' : detail.noInvoice === false ? '否' : '' }}</span>
        <span class="w-[50%]">
          是否纳入计提印花税: {{ detail.includeAccrualStamp ? '是' : detail.includeAccrualStamp === false ? '否' : '' }}
        </span>
        <span class="w-[50%]">是否押金: {{ detail.isDeposit ? '是' : detail.isDeposit === false ? '否' : '' }}</span>
        <span class="w-full">备注: {{ detail.remark }}</span>
      </div>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[12px]">单据必填字段校验</h4>
      <div class="mb-[12px] text-secondary">应收单: {{ bill.join('、') || '无' }}</div>
      <div class="text-secondary">收付款记录: {{ record.join('、') || '无' }}</div>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import {
  detail as getDetail,
  queryPaymentTypeReceiveBill,
  queryPaymentTypePayExplainBook,
  deleteBatch,
  updateStatus
} from '../apis.js'
import { Modal, message } from 'ant-design-vue'

const emit = defineEmits(['editRentScheme', 'refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const optionObj = {
  leaseUnit: '租赁单元',
  waterEleTableNum: '水电表号',
  contractNum: '合同编号',
  carportNum: '车位号',
  serviceCenter: '服务处',
  receiveBeginDate: '应收开始日期',
  receiveEndDate: '应收结束日期',
  incomeBelongYm: '收入归属年月'
}

const bill = ref([]) // 应收单
const record = ref([]) // 收付款记录

const loading = ref(false)
const detail = reactive({})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  bill.value = []
  record.value = []
  const data = await Promise.all([queryPaymentTypeReceiveBill({ id }), queryPaymentTypePayExplainBook({ id })])
  const billObj = data[0].result.length ? data[0].result[0] : {}
  const recordObj = data[1].result.length ? data[1].result[0] : {}
  for (const key in optionObj) {
    if (billObj[key]) {
      bill.value.push(optionObj[key])
    }
    if (recordObj[key]) {
      record.value.push(optionObj[key])
    }
  }
  loading.value = false
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleUpdateStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该楼栋？`,
    content: detail.status === 'ENABLE' ? '楼栋禁用后将无法再被使用，但不影响已创建的数据。' : '',
    centered: true,
    onOk: async () => {
      await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(detail.status === 'ENABLE' ? '已禁用' : '启用成功')
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该款项类型？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
