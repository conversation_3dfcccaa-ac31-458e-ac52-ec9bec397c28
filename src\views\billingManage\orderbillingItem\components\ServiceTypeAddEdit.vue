<template>
  <a-modal
    v-model:open="visible"
    :title="title"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '120px' } }"
      autocomplete="off"
    >
      <a-form-item label="服务类型名称" name="itemText">
        <a-input v-model:value="ruleForm.itemText" placeholder="请输入服务类型名称" :maxlength="10" />
      </a-form-item>
      <a-form-item label="服务类型数据值" name="itemValue" v-if="!ruleForm.id">
        <a-input v-model:value="ruleForm.itemValue" placeholder="请输入服务类型数据值" :maxlength="10" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { serviceTypeAdd, serviceTypeEdit, serviceTypeDictItemCheck } from '../apis'
import { message } from 'ant-design-vue'
const emits = defineEmits('loadData')
// 弹窗可见性
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    Object.assign(ruleForm, data)
  }
}
defineExpose({ open })

const title = computed(() => {
  return ruleForm.id ? '编辑' : '新增'
})
const ruleForm = reactive({
  id: '',
  dictId: '',
  itemText: '',
  itemValue: ''
})
const validateItemValue = async (rule, value) => {
  if (!value) return Promise.reject('请输入数据值')
  try {
    await serviceTypeDictItemCheck({
      dictId: ruleForm.dictId,
      id: ruleForm.id || undefined,
      itemValue: value
    })
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const rules = {
  itemText: [{ required: true, message: '请输入服务类型名称', trigger: ['blur'] }],
  itemValue: [{ required: true, validator: validateItemValue, trigger: ['blur'] }]
}

// 提交
const confirmLoading = ref(false)
const formRef = ref()
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const data = await (ruleForm.id ? serviceTypeEdit(ruleForm) : serviceTypeAdd(ruleForm))
    emits('loadData')
    handleCancel()
    message.success(data.message)
  } finally {
    confirmLoading.value = false
  }
}
// 取消
const handleCancel = () => {
  Object.assign(ruleForm, {
    id: '',
    dictId: '',
    itemText: '',
    itemValue: ''
  })
  visible.value = false
}
</script>
