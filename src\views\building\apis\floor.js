import request from '@/apis/http'

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/wyFloor/exportXls',
    responseType: 'blob',
    params,
    fileName
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyFloor/importExcel',
    data
  })
}

export const addFloor = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyFloor/add',
    data
  })
}

export const editFloor = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyFloor/edit',
    data
  })
}

export const deleteFloor = (params) => {
  return request({
    method: 'delete',
    url: '/bas/wyFloor/deleteBatch',
    params
  })
}
