设置水电分摊信息弹窗
<template>
  <a-modal
    v-model:open="visible"
    title="设置楼层水电分摊信息"
    width="1200px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center justify-between mb-[16px]">
      <h2 class="text-[16px] font-bold">楼层水电分摊信息</h2>
      <div class="flex items-center">
        <span>自动出账时间: 每月</span>
        <a-input
          v-model:value="floorInfo.everyMonthAutoBillDay"
          class="!w-[60px] !mx-[10px]"
          size="medium"
          :maxlength="2"
        ></a-input>
        <span>日</span>
      </div>
    </div>
    <set-info
      :list="floorInfo.waterShareFormulas"
      :main-id="floorInfo.id"
      mitt-id="floor-water-electricity"
      relation-type="WyFloor"
      ref="setInfoRef"
    ></set-info>
  </a-modal>
</template>

<script setup>
import SetInfo from './SetInfo.vue'
import { message } from 'ant-design-vue'
import { positionIntRegexp } from '@/utils/validate'

const emit = defineEmits(['updateFloorWaterShareFormulas'])

const visible = ref(false)

const floorInfo = reactive({
  id: '',
  everyMonthAutoBillDay: '',
  waterShareFormulas: []
})
const open = (info) => {
  floorInfo.id = info.id
  floorInfo.everyMonthAutoBillDay = info.everyMonthAutoBillDay
  floorInfo.waterShareFormulas = [...info.waterShareFormulas]
  visible.value = true
}

const setInfoRef = ref()
const handleConfirm = () => {
  if (!setInfoRef.value.validate()) return
  if (floorInfo.waterShareFormulas.length) {
    if (!floorInfo.everyMonthAutoBillDay) {
      message.warning('请输入水电分摊信息的自动出账时间')
      return
    }
    if (!positionIntRegexp.test(floorInfo.everyMonthAutoBillDay)) {
      message.warning('自动出账时间填写不正确')
      return
    }
    const day = Number(floorInfo.everyMonthAutoBillDay)
    if (!(day >= 1 && day <= 28)) {
      message.warning('自动出账时间必须在每月1 - 28日之间')
      return
    }
  }
  emit('updateFloorWaterShareFormulas', floorInfo)
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>
