import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 获取自用公式管理列表
export const getWaterShareFormulaList = (params) => {
  return request({
    method: 'get',
    url: '/biz/watershare/waterShareFormulaBaseInfo/list',
    params
  })
}

// 租赁单元-f7分页查询
export const getF7List = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnit/f7List',
    params
  })
}

export const getLeaseUnitList = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnit/list',
    params
  })
}

// 保存
export const submitLeaseUnit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/submit',
    data
  })
}

// 暂存
export const addLeaseUnit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/add',
    data
  })
}

// 编辑
export const editLeaseUnit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/edit',
    data
  })
}

// 通过 id 删除
export const deleteLeaseUnit = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseUnit/delete',
    params
  })
}

// 批量删除
export const batchDeleteLeaseUnit = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseUnit/deleteBatch',
    params
  })
}

// 导出 Excel
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入 Excel
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/leaseUnit/importExcel', data, controller)
}

// 审核
export const audit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/audit',
    data
  })
}

// 反审核
export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/unAudit',
    data
  })
}

// 撤回
export const back = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/back',
    data
  })
}

// 获取租赁单元详情
export const getLeaseUnitDetail = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnit/queryById',
    params
  })
}

// 批量更新片区管理员
export const batchChangeAreaManager = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/batchChangeAreaManager',
    data
  })
}

// 批量更新发票地址
export const batchChangeInvoiceAddress = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/batchChangeInvoiceAddress',
    data
  })
}

// 批量维护项目
export const batchChangeProject = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnit/batchChangeWyProject',
    data
  })
}
