<template>
  <div>
    <a-form layout="inline" class="!mb-[16px]">
      <a-form-item>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
      </a-form-item>
      <a-form-item label="用户账号" class="!ml-[40px]">
        <s-input v-model="params.username" placeholder="搜索用户账号" @input="handleInput"></s-input>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'avatar'">
          <a-avatar :src="record.avatar"></a-avatar>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-popconfirm title="强制退出用户？" @confirm="handleLogout(record)">
            <span class="primary-btn">强退</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { page, forceLogout } from './apis'
import { message } from 'ant-design-vue'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  username: ''
})

const columns = [
  { title: '用户账号', dataIndex: 'username', width: 140, fixed: 'left' },
  { title: '用户姓名', dataIndex: 'realname' },
  { title: '头像', dataIndex: 'avatar' },
  { title: '生日', dataIndex: 'birthday' },
  { title: '性别', dataIndex: 'sex_dictText' },
  { title: '手机号', dataIndex: 'phone' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const handleLogout = async (data) => {
  await forceLogout({ token: data.token })
  message.success(`强制退出用户“${data.realname}”成功！`)
  let pageNo = pagination.value.current
  if (pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

onMounted(() => {
  onTableChange()
})
</script>
