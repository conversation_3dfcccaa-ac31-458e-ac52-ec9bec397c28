<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑车位号' : '新增车位号'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '136px' } }" autocomplete="off">
      <a-form-item label="名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入名称" :maxlength="64"></a-input>
      </a-form-item>
      <a-form-item label="停车场" name="park">
        <dict-select v-model="form.park" code="CT_BAS_Park" placeholder="请选择停车场"></dict-select>
      </a-form-item>
      <a-form-item label="物业管理公司" name="manageCompany">
        <company-select v-model="form.manageCompany" type="all" placeholder="请选择物业管理公司"></company-select>
      </a-form-item>
      <a-form-item label="是否引用" name="isRefer">
        <a-radio-group v-model:value="form.isRefer">
          <a-radio :value="true">是</a-radio>
          <a-radio :value="false">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="form.remark"
          placeholder="请输入备注(选填)"
          show-count
          :maxlength="255"
          :auto-size="{ minRows: 4, maxRows: 4 }"
          allow-clear
        ></a-textarea>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { add, edit } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
}

const form = reactive({
  id: '',
  name: '',
  number: '',
  manageCompany: '',
  park: '',
  status: '',
  remark: '',
  easId: '',
  isRefer: true
})

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  park: [{ required: true, message: '请选择停车场', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.name = ''
  form.number = ''
  form.manageCompany = ''
  form.park = ''
  form.status = ''
  form.remark = ''
  form.easId = ''
  form.isRefer = true
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
