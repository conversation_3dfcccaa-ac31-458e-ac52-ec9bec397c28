<template>
  <header class="h-[64px] bg-white flex items-center justify-between border-b border-solid border-[#d7dae0] px-[24px]">
    <div class="flex items-center">
      <router-link class="mr-[64px]" to="/home">
        <img src="@/assets/imgs/logo-row.png" class="h-[40px]" />
      </router-link>
      <router-link class="entry-btn" :class="{ active: route.path === '/home' }" to="/home">工作台</router-link>
      <router-link class="entry-btn" :class="{ active: route.path === '/cockpit' }" to="/cockpit">驾驶舱</router-link>
      <template v-for="item in menus" :key="item.id">
        <a-popover
          :arrow="false"
          overlay-class-name="nav-header-popover"
          placement="bottomLeft"
          v-if="item.children && item.children.length"
        >
          <template #content>
            <div
              v-for="link in item.children"
              :key="link.id"
              class="popover-link"
              @click="toModulePage(item, link.path)"
            >
              {{ link.meta.title }}
            </div>
          </template>
          <div class="entry-btn">{{ item.meta.title }}</div>
        </a-popover>
        <div
          class="entry-btn"
          :class="{ active: route.path === item.path }"
          :to="item.path"
          @click="toModulePage(null, item.path)"
          v-else
        >
          {{ item.meta.title }}
        </div>
      </template>
    </div>
    <a-dropdown>
      <div class="flex items-center cursor-pointer" @click.prevent>
        <img src="@/assets/imgs/avatar.png" class="w-[32px] h-[32px] rounded-[50%]" />
        <span class="ml-[12px] mr-[4px]">你好，{{ userInfo.realname }}</span>
        <i class="a-icon-arrow-down text-[14px] text-[#cecece]"></i>
      </div>
      <template #overlay>
        <a-menu>
          <a-menu-item><div class="primary-btn" @click="toPage('/personalCenter')">基础信息</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="handleSwitchDepart">切换公司</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="handleUpdatePassword">密码修改</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="handleLogout">退出登录</div></a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <change-password ref="changePasswordRef"></change-password>
    <switch-depart ref="switchDepartRef" :depart-list="departList"></switch-depart>
  </header>
</template>

<script setup>
import ChangePassword from '@/views/personalCenter/components/ChangePassword.vue'
import { useUserStore } from '@/store/modules/user'
import { message, Modal } from 'ant-design-vue'
import SwitchDepart from './SwitchDepart.vue'
import { getCurrentDeparts } from '@/views/system/user/apis'

const store = useUserStore()
const route = useRoute()
const router = useRouter()
const userInfo = computed(() => store.userInfo)
const menus = computed(() => {
  if (!(store.permission.menu && store.permission.menu.length)) return []
  const list = JSON.parse(
    JSON.stringify(
      store.permission.menu.filter((item) => !['杂乱菜单放置', '代码生成临时菜单'].includes(item.meta.title))
    )
  )
  return list.map((item) => {
    if (item.children && item.children.length) {
      item.children.forEach((child) => {
        if (child.children && child.children.length) {
          child.path = child.children[0].path
        }
      })
    }
    return item
  })
})

const toModulePage = (item, path) => {
  if (item) {
    const data = store.permission.menu.find((i) => i.id === item.id)
    store.setCurrentMenus({
      title: item.meta.title,
      list: data.children
    })
  }
  router.push({ path })
}

const toPage = (path) => {
  router.push({ path })
}

// 修改密码
const changePasswordRef = ref()
const handleUpdatePassword = () => {
  changePasswordRef?.value.open()
}

const departList = ref([])
const currentDepart = ref('')
const loadUserDepartInfo = async () => {
  const { result } = await getCurrentDeparts()
  currentDepart.value = result.currentDepart
  departList.value = result.departList
}

const switchDepartRef = ref()
const handleSwitchDepart = () => {
  if (departList.value.length <= 1) {
    message.warning('您当前只隶属于一个部门，没有其他可切换的部门')
    return
  }
  switchDepartRef.value.open(currentDepart.value)
}

// 退出登录
const handleLogout = () => {
  Modal.confirm({
    title: '系统提示',
    content: '是否确认退出登录？',
    centered: true,
    onOk: () => {
      store.logout()
    }
  })
}

onMounted(() => {
  loadUserDepartInfo()
})
</script>

<style lang="less" scoped>
.entry-btn {
  padding: 0 24px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition:
    color 0.2s,
    background-color 0.2s;
  margin-right: 16px;
  &:hover {
    color: var(--color-primary);
  }
  &.active {
    color: var(--color-primary);
    background-color: rgba(var(--color-primary-rgb), 0.1);
  }
}
</style>

<style lang="less">
.nav-header-popover {
  .ant-popover-inner {
    padding: 0;
    overflow: hidden;
  }
  .popover-link {
    width: 200px;
    height: 40px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--color-main);
    transition:
      color 0.2s,
      background-color 0.2s;
    &:hover {
      color: var(--color-primary);
      background-color: rgba(var(--color-primary-rgb), 0.1);
    }
  }
}
</style>
