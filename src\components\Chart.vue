<template>
  <div :id="id" class="w-full h-full"></div>
</template>

<script setup>
import * as echarts from 'echarts'

const emits = defineEmits(['click'])

const props = defineProps({
  id: { type: String, default: 'chart' },
  option: { type: Object, default: () => ({}) }
})

let chartInstance = null

watch(
  () => props.option,
  () => {
    updateChart()
    chartInstance.resize()
  },
  { deep: true }
)

const updateChart = () => {
  if (!props.option?.series) {
    return
  }
  chartInstance.showLoading()
  chartInstance.setOption(props.option)
  chartInstance.hideLoading() // 隐藏加载动画
  chartInstance.resize()
}
const initEcharts = () => {
  chartInstance = echarts.init(document.getElementById(props.id))
  updateChart()
  chartInstance.on('click', chartClick)
  // 监听窗口大小变化，自动调整图表大小
  window.addEventListener('resize', () => chartInstance.resize())
}

const chartClick = (data) => {
  emits('click', data)
}

onMounted(() => {
  nextTick(() => {
    initEcharts()
  })
})

onUnmounted(() => {
  // 销毁 ECharts 实例
  chartInstance?.dispose()
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', () => chartInstance.resize())
})
</script>
