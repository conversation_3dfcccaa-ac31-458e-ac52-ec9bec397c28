import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
// 分页数据
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/consume/consumedRecord/list',
    params
  })
}
// 核销记录-核销
export const consumed = (data) => {
  return request({
    method: 'post',
    url: '/biz/consume/consumedRecord/consumed',
    data
  })
}
// 核销记录-反核销
export const undoConsumed = (data) => {
  return request({
    method: 'post',
    url: '/biz/consume/consumedRecord/undo',
    data
  })
}
// 核销记录- 结果预览
export const preview = (data) => {
  return request({
    method: 'post',
    url: '/biz/consume/consumedRecord/preview',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/consume/consumedRecord/edit',
    data
  })
}
export const del = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/consume/consumedRecord/deleteBatch?ids=${ids}`
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/consume/consumedRecord/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/consume/consumedRecord/importExcel', data, controller)
}
