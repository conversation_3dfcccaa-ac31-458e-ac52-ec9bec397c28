import { f7List } from '@/views/paymentType/apis'
import { renderDictTag } from '@/utils/render'
export default {
  modalTitle: '选择款项类型',
  request: f7List,
  params: {
    column: 'number',
    order: 'desc',
    id: undefined,
    status: undefined,
    number: undefined,
    name: undefined,
    paymentProperties: undefined,
    manageCompany: undefined,
    contractType: undefined,
    periodPayContract: undefined,
    noInvoice: undefined,
    includeAccrualStamp: undefined,
    isDeposit: undefined,
    remark: undefined
  },
  rowKey: 'id',
  displayKey: 'name',
  keywordKey: 'name',
  keywordPlaceholder: '搜索名称',
  scrollX: 1500,
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  columns: [
    { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
    { title: '名称', dataIndex: 'name', width: 120 },
    { title: '款项类型', dataIndex: 'paymentProperties_dictText', width: 120 },
    {
      title: '单据状态',
      dataIndex: 'status',
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_BaseStatus', 'dot'),
      width: 120
    },
    { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true, width: 120 },
    { title: '不开票', dataIndex: 'noInvoice', width: 120, customRender: ({ text }) => (text ? '是' : '否') },
    {
      title: '周期性缴交',
      dataIndex: 'periodPayContract',
      width: 120,
      customRender: ({ text }) => (text ? '是' : '否')
    },
    { title: '押金', dataIndex: 'isDeposit', width: 120, customRender: ({ text }) => (text ? '是' : '否') },
    {
      title: '纳入计提印花税',
      dataIndex: 'includeAccrualStamp',
      width: 140,
      customRender: ({ text }) => (text ? '是' : '否')
    },
    { title: '合同协议类型', dataIndex: 'contractType_dictText', ellipsis: true, width: 120 },
    { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true }
  ],
  searchList: [
    { label: '单据编号', name: 'number', type: 's-input' },
    {
      label: '款项类型',
      name: 'paymentProperties',
      type: 'dict-select',
      code: 'CT_BASE_ENUM_PaymentType_PaymentProperties'
    },
    { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
    { label: '创建时间', name: 'createTime', type: 'date' },
    { label: '备注', name: 'remark', type: 's-input' }
  ]
}
