<template>
  <a-modal v-model:open="visible" title="更多筛选" width="600px" wrap-class-name="common-modal" :mask-closable="false">
    <a-form autocomplete="off" label-align="left" :label-col="{ style: { width: '120px' } }">
      <a-form-item label="物业管理公司">
        <company-select v-model="params.manageCompanyList" type="all" mode="multiple"></company-select>
      </a-form-item>
      <a-form-item label="权属单位">
        <company-select v-model="params.ownerCompanyList" type="all" mode="multiple"></company-select>
      </a-form-item>
      <a-form-item label="租赁用途">
        <!-- <dict-select v-model="params.leaseUseList" mode="multiple" code="CT_BAS_LeaseUse"></dict-select> -->
        <a-checkbox-group v-model:value="params.leaseUseList" :options="useList"></a-checkbox-group>
      </a-form-item>
      <a-form-item label="租赁单元业务状态">
        <a-checkbox-group v-model:value="params.bizStatusList" :options="statusList"></a-checkbox-group>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleReset">重置</a-button>
      <a-button type="primary" @click="handleConfirm">确认</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { useDictStore } from '@/store/modules/dict'

const emit = defineEmits(['refresh'])

const dictStore = useDictStore()

const statusList = computed(() => dictStore.getDictItems('CT_BASE_ENUM_LeaseUnit_BizStatus'))
const useList = computed(() => dictStore.getDictItems('CT_BAS_LeaseUse'))

const visible = ref(false)

const params = reactive({
  ownerCompanyList: [], // 权属公司列表
  manageCompanyList: [], // 管理公司列表
  leaseUseList: [], // 租赁类型列表
  bizStatusList: [], // 业务状态列表
  endTime: '' // 截止日期
})

const open = (data) => {
  Object.assign(params, data)
  visible.value = true
}

const handleConfirm = () => {
  visible.value = false
  emit('refresh', params)
}
const handleReset = () => {
  for (const key in params) {
    if (typeof params[key] !== 'string') {
      params[key] = []
    }
  }
}

defineExpose({ open })
</script>
