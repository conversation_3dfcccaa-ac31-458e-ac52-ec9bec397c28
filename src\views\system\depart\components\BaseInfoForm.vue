部门基本信息表单
<template>
  <a-form
    :model="form"
    :rules="rules"
    ref="formRef"
    label-align="left"
    :label-col="{ style: { width: '70px' } }"
    autocomplete="off"
  >
    <a-form-item label="机构名称" name="departName">
      <a-input v-model:value="form.departName" placeholder="请输入机构名称" :maxlength="20"></a-input>
    </a-form-item>
    <a-form-item label="上级部门" name="parentId" v-if="form.parentId">
      <a-tree-select
        v-model:value="form.parentId"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        :tree-data="deptTree"
        :field-names="{
          children: 'children',
          label: 'title',
          value: 'id'
        }"
        disabled
      ></a-tree-select>
    </a-form-item>
    <a-form-item label="机构编码" v-if="form.orgCode">
      <a-input v-model:value="form.orgCode" disabled></a-input>
    </a-form-item>
    <a-form-item label="机构类型" name="orgCategory">
      <a-radio-group v-model:value="form.orgCategory" name="radioGroup">
        <a-radio value="0">集团</a-radio>
        <a-radio value="1">公司</a-radio>
        <a-radio value="2">部门</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="排序" name="departOrder">
      <a-input v-model:value="form.departOrder" placeholder="请输入排序" :maxlength="4"></a-input>
    </a-form-item>
    <a-form-item label="电话" name="mobile">
      <a-input v-model:value="form.mobile" placeholder="请输入电话" :maxlength="4"></a-input>
    </a-form-item>
    <a-form-item label="传真" name="fax">
      <a-input v-model:value="form.fax" placeholder="请输入传真" :maxlength="4"></a-input>
    </a-form-item>
    <a-form-item label="地址" name="address">
      <a-input v-model:value="form.address" placeholder="请输入地址" :maxlength="4"></a-input>
    </a-form-item>
    <a-form-item label="备注" name="memo">
      <a-textarea v-model:value="form.memo" placeholder="请输入备注" :maxlength="200" show-count :rows="4"></a-textarea>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { addDept, editDept } from '../apis'

defineProps({
  deptTree: { type: Array, default: () => [] }
})

const form = reactive({
  departName: '',
  parentId: '',
  orgCode: '',
  orgCategory: '', // 一级部门类型
  orgType: '', // 子级部门类型
  departOrder: '',
  mobile: '',
  fax: '',
  address: '',
  memo: '',
  id: ''
})

const rules = {
  departName: [{ required: true, message: '请输入部门名称', trigger: 'blur' }]
}

const formRef = ref()
const save = async () => {
  await formRef.value.validate()
  form.id ? await editDept(form) : await addDept(form)
}

const setFormData = (data) => {
  Object.assign(form, data)
}

defineExpose({ save, setFormData })
</script>
