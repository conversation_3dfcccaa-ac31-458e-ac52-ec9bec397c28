<template>
  <a-modal
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}客户跟进记录`"
    @ok="handleSave"
    @cancel="handleCancel"
    :confirm-loading="confirmLoading"
    width="800px"
    class="common-modal"
    :mask-closable="false"
  >
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="跟进客户" name="followCustomer">
        <a-form-item-rest>
          <f7-select v-model="formData.followCustomer" placeholder="请选择客户" f7-type="customer" />
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="跟进人" name="followPerson">
        <a-form-item-rest>
          <f7-select v-model="formData.followPerson" placeholder="请选择跟进人" f7-type="user" />
        </a-form-item-rest>
      </a-form-item>

      <a-form-item label="跟进方式" name="followMethod">
        <dict-select
          v-model="formData.followMethod"
          placeholder="选择跟进方式"
          code="CT_BASE_ENUM_FollowRecord_FollowMethod"
        ></dict-select>
      </a-form-item>
      <a-form-item label="参观租赁单元" name="visitLeaseUnit">
        <a-form-item-rest>
          <f7-select v-model="formData.visitLeaseUnit" placeholder="请选择租赁单元" f7-type="leaseUnit" />
        </a-form-item-rest>
      </a-form-item>

      <a-form-item label="意向面积" name="intendArea">
        <a-input-number v-model:value="formData.intendArea" :precision="4" style="width: 100%" addon-after="m²" />
      </a-form-item>

      <a-form-item label="登记时间" name="followTime">
        <a-date-picker v-model:value="formData.followTime" style="width: 100%" value-format="YYYY-MM-DD" />
      </a-form-item>

      <a-form-item label="意向入住时间" name="moveIntendTime">
        <a-date-picker v-model:value="formData.moveIntendTime" style="width: 100%" value-format="YYYY-MM-DD" />
      </a-form-item>

      <a-form-item label="布局偏好" name="layoutPreference">
        <a-textarea
          v-model:value="formData.layoutPreference"
          :maxlength="200"
          show-count
          placeholder="请输入布局偏好"
        />
      </a-form-item>

      <a-form-item label="配套设施需求" name="supportFacilityRequire">
        <a-textarea
          v-model:value="formData.supportFacilityRequire"
          :maxlength="200"
          show-count
          placeholder="请输入配套设施需求"
        />
      </a-form-item>

      <a-form-item label="其他内容" name="otherContent">
        <a-textarea v-model:value="formData.otherContent" :maxlength="200" show-count placeholder="请输入其他内容" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addFollowRecord, editFollowRecord } from '../apis'
import dayjs from 'dayjs'
import userStore from '@/store/modules/user.js'

const { userInfo } = userStore()

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  followCustomer: undefined,
  followPerson: undefined,
  followMethod: undefined,
  visitLeaseUnit: undefined,
  intendArea: undefined,
  followTime: undefined,
  moveIntendTime: undefined,
  layoutPreference: undefined,
  supportFacilityRequire: undefined,
  otherContent: undefined
}

const formData = reactive({ ...formDataDefault })

const rules = {
  followCustomer: [{ required: true, message: '请选择跟进客户', trigger: 'change' }],
  followPerson: [{ required: true, message: '请选择跟进人', trigger: 'change' }],
  followMethod: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
  visitLeaseUnit: [{ required: true, message: '请选择参观租赁单元', trigger: 'change' }],
  followTime: [{ required: true, message: '请选择登记时间', trigger: 'change' }],
  moveIntendTime: [{ required: true, message: '请选择意向入住时间', trigger: 'change' }]
}

/**
 * 保存跟进记录
 */
const handleSave = async () => {
  if (confirmLoading.value) return

  await formRef.value.validateFields()

  try {
    confirmLoading.value = true
    if (formData.id) {
      const data = await editFollowRecord(formData)
      message.success(data.message)
    } else {
      const data = await addFollowRecord(formData)
      message.success(data.message)
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 打开编辑对话框
 */
const open = (record) => {
  visible.value = true
  formData.followPerson = userInfo.value.id
  formData.followTime = dayjs(Date.now()).format('YYYY-MM-DD')
  if (record && record.id) {
    Object.assign(formData, record)
  }
  // 从客户详情新建
  if (record && record.followCustomer) {
    formData.followCustomer = record.followCustomer
  }
}

/**
 * 取消编辑并重置表单
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)

  visible.value = false
  emits('refresh')
}

defineExpose({
  open
})
</script>
