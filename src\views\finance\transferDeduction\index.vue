<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div>
        <a-button type="primary" @click="handleAdd" v-auth="'biz.funds:ct_fun_transfer_deduction_req:add'">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'biz.funds:ct_fun_transfer_deduction_req:importExcel'">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport" v-auth="'biz.funds:ct_fun_transfer_deduction_req:exportXls'">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.number"
          placeholder="搜索编号"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <!-- 表格区域 -->
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1200, y: tableHeight }"
      row-key="id"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_AuditStatus" type="dot"></status-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <span
            class="primary-btn"
            @click="handleDetail(record)"
            v-auth="'biz.funds:ct_fun_transfer_deduction_req:view'"
          >
            查看
          </span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item key="submit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleSubmit(record)">提交</div>
                </a-menu-item>
                <a-menu-item key="back" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleBack(record)">撤回</div>
                </a-menu-item>
                <a-menu-item key="audit" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleAudit(record)">审核</div>
                </a-menu-item>
                <a-menu-item key="unAudit" v-if="record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleUnAudit(record)">反审核</div>
                </a-menu-item>
                <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
                <a-menu-item key="viewReceivePayRecords" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleViewReceivePayRecords(record)">查看收款单</div>
                </a-menu-item>
                <a-menu-item key="viewReceiveCertificate" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleViewReceiveCertificate(record)">查看应收单</div>
                </a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <edit-transfer-deduction ref="editDrawerRef" @refresh="onTableChange" />
    <transfer-deduction-detail ref="detailDrawerRef" @refresh="onTableChange" />
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('转款抵扣导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { renderMoney } from '@/utils/render'
import { hasPermission } from '@/utils/permission'
import {
  getTransferDeductionList,
  submitTransferDeduction,
  deleteTransferDeduction,
  batchDeleteTransferDeduction,
  auditTransferDeductionReq,
  unAuditTransferDeduction,
  backTransferDeduction,
  importExcel,
  exportExcel,
  getTransferDeductionDetail,
  getDeductionDetail
} from './apis'
import EditTransferDeduction from './components/EditTransferDeduction.vue'
import TransferDeductionDetail from './components/TransferDeductionDetail.vue'

const router = useRouter()

const exportLoading = ref(false)
const commonImportRef = ref()
const editDrawerRef = ref()
const detailDrawerRef = ref()
const columnSetRef = ref()

const searchParams = reactive({
  column: 'number',
  order: 'desc',
  id: undefined,
  number: undefined,
  sourceContract: undefined,
  transferDeductionContract: undefined,
  transferDeductionAmount: undefined,
  status: undefined,
  createTime: undefined,
  bizDate: undefined,
  operator: undefined,
  remark: undefined
})

const searchList = [
  { label: '款项来源合同', name: 'sourceContract', type: 'contract-select', placeholder: '请输入款项来源合同' },
  {
    label: '款项转款抵扣合同',
    name: 'transferDeductionContract',
    type: 'contract-select',
    placeholder: '请输入款项转款抵扣合同'
  },
  { label: '转款抵扣金额', name: 'transferDeductionAmount', type: 'input', placeholder: '请输入转款抵扣金额' },
  {
    label: '状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择状态'
  },
  { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
  { label: '业务日期', name: 'bizDate', type: 'date', placeholder: '请选择业务日期' },
  { label: '提交人', name: 'operator', type: 'user-select', placeholder: '请选择提交人' },
  { label: '备注', name: 'remark', type: 's-input', placeholder: '请输入备注' },
  { label: '单据id', name: 'id', type: 'input' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '款项来源合同', dataIndex: 'sourceContract_dictText', width: 160, ellipsis: true },
  { title: '款项转款抵扣合同', dataIndex: 'transferDeductionContract_dictText', width: 160, ellipsis: true },
  {
    title: '转款抵扣金额',
    dataIndex: 'transferDeductionAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '状态', dataIndex: 'status', width: 120 },
  { title: '创建时间', dataIndex: 'createTime', width: 120 },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '提交人', dataIndex: 'operator_dictText', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getTransferDeductionList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

/**
 * 打开新建转款抵扣记录的抽屉组件
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 打开编辑转款抵扣记录的抽屉组件
 * @param {Object} record - 转款抵扣记录数据
 */
const handleEdit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:edit')) return
  editDrawerRef.value.open(record)
}

/**
 * 打开查看转款抵扣记录详情的抽屉组件
 * @param {Object} record - 转款抵扣记录数据
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 打开导入数据的模态框
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出转款抵扣记录数据到Excel文件
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('转款抵扣记录.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 提交转款抵扣记录进行审核
 * @param {Object} record - 转款抵扣记录数据
 */
const handleSubmit = async (record) => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:submit')) return
  const transferList = await getTransferDeductionDetail({ id: record.id })
  const deductionList = await getDeductionDetail({ id: record.id })
  record.transferDeductionReqTransferDetailList = transferList.result
  record.transferDeductionReqDeductionDetailList = deductionList.result
  Modal.confirm({
    title: '确认提交',
    content: '确认提交该转款抵扣记录？',
    onOk: async () => {
      await submitTransferDeduction({ ...record })
      message.success('提交成功')
      onTableChange()
    }
  })
}

/**
 * 审核转款抵扣记录
 * @param {Object} record - 转款抵扣记录数据
 */
const handleAudit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: '确认审核该转款抵扣记录？',
    onOk: async () => {
      await auditTransferDeductionReq({ id: record.id })
      message.success('审核成功')
      onTableChange()
    }
  })
}

/**
 * 反审核转款抵扣记录
 * @param {Object} record - 转款抵扣记录数据
 */
const handleUnAudit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: '确认反审核该转款抵扣记录？',
    onOk: async () => {
      await unAuditTransferDeduction({ id: record.id })
      message.success('反审核成功')
      onTableChange()
    }
  })
}

/**
 * 撤回转款抵扣记录
 * @param {Object} record - 转款抵扣记录数据
 */
const handleBack = (record) => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: '确认撤回该转款抵扣记录？',
    onOk: async () => {
      await backTransferDeduction({ id: record.id })
      message.success('撤回成功')
      onTableChange()
    }
  })
}

/**
 * 删除单条转款抵扣记录
 * @param {Object} record - 转款抵扣记录数据
 */
const handleDelete = (record) => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: '确认删除该转款抵扣记录？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await deleteTransferDeduction({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除选中的转款抵扣记录
 */
const handleBatchDelete = () => {
  if (!hasPermission('biz.funds:ct_fun_transfer_deduction_req:deleteBatch')) return

  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确认删除选中的 ${selectedRowKeys.value.length} 条记录？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteTransferDeduction({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 查看缴款单
 * @param {Object} record - 转款抵扣记录
 */
const handleViewReceivePayRecords = (record) => {
  router.push({
    path: '/receivePayRecords',
    query: { sourceBillId: record.id }
  })
}

/**
 * 查看应收单
 * @param {Object} record - 转款抵扣记录
 */
const handleViewReceiveCertificate = (record) => {
  router.push({
    path: '/statement/receiveCertificate',
    query: { sourceBillId: record.id }
  })
}

let timer
/**
 * 搜索输入防抖处理，延迟600ms执行搜索
 */
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

onMounted(() => {
  const id = sessionStorage.getItem('idFromContractClearing')
  if (id) {
    searchParams.id = id
    sessionStorage.removeItem('idFromContractClearing')
  }
  onTableChange()
})
</script>
