<template>
  <div class="overflow-hidden">
    <div class="flowable-edit">
      <my-process-designer
        :key="`designer-${reloadIndex}`"
        :options="{
          taskResizingEnabled: true,
          eventResizingEnabled: true,
          minimap: { open: true }
        }"
        v-model="xmlString"
        v-bind="controlForm"
        keyboard
        ref="processDesigner"
        @element-click="elementClick"
        @init-finished="initModeler"
        @save="handleSave"
      />
      <my-properties-panel
        :key="`penal-${reloadIndex}`"
        :bpmn-modeler="modeler"
        :prefix="controlForm.prefix"
        class="process-panel"
      />
    </div>
  </div>
</template>

<script>
import { readXml, saveXml } from './apis'

// 自定义元素选中时的弹出菜单（修改 默认任务 为 用户任务）
import MyProcessDesigner from '../components/package/designer'
import MyPropertiesPanel from '../components/package/penal'
import CustomContentPadProvider from '../components/package/designer/plugins/content-pad'
// 自定义左侧菜单（修改 默认任务 为 用户任务）
import CustomPaletteProvider from '../components/package/designer/plugins/palette'
import Log from '../components/package/Log'
// 小地图
import minimapModule from 'diagram-js-minimap'
import { message } from 'ant-design-vue'
import { useTagStore } from '@/store/modules/tags'

import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css'

export default {
  name: 'App',
  components: {
    MyProcessDesigner,
    MyPropertiesPanel
  },
  data() {
    return {
      deployId: '',
      xmlString: '',
      modeler: null,
      reloadIndex: 0,
      controlDrawerVisible: false,
      infoTipVisible: false,
      pageMode: false,
      controlForm: {
        processId: '',
        processName: '',
        simulation: true,
        labelEditing: false,
        labelVisible: false,
        prefix: 'flowable',
        headerButtonSize: 'small',
        events: ['element.click', 'element.contextmenu'],
        // additionalModel: []
        additionalModel: [CustomContentPadProvider, CustomPaletteProvider, minimapModule]
      },
      addis: {
        CustomContentPadProvider,
        CustomPaletteProvider
      }
    }
  },
  mounted() {
    const deployId = this.$route.params.deployId
    if ([-1, '-1'].includes(deployId)) {
      return
    }
    if (deployId) {
      this.deployId = deployId
      this.loadXml()
    }
  },
  methods: {
    async loadXml() {
      const { result } = await readXml(this.deployId)
      this.xmlString = result
      this.$nextTick(() => {
        this.$refs.processDesigner.init()
      })
    },
    async handleSave({ processKey, name, category, classNameKey, xml }) {
      if (!processKey) {
        message.warning('流程标识key不能为空!')
        return
      }
      if (!name) {
        message.warning('流程名称不能为空!')
        return
      }
      if (!category) {
        message.warning('流程使用公司不能为空!')
        return
      }
      if (!classNameKey) {
        message.warning('流程业务单据不能为空!')
        return
      }
      if (!xml) {
        message.warning('流程设计图不能为空!')
        return
      }
      await saveXml({ processKey, name, category, company: category, classNameKey, xml })
      useTagStore().closeCurTab()
    },
    initModeler(modeler) {
      setTimeout(() => {
        this.modeler = modeler
        const canvas = modeler.get('canvas')
        const rootElement = canvas.getRootElement()
        Log.prettyPrimary('Process Id:', rootElement.id)
        Log.prettyPrimary('Process Name:', rootElement.businessObject.name)
      }, 10)
    },
    reloadProcessDesigner(notDeep) {
      this.controlForm.additionalModel = []
      for (const key in this.addis) {
        if (this.addis[key]) {
          this.controlForm.additionalModel.push(this.addis[key])
        }
      }
      !notDeep && (this.xmlString = undefined)
      this.reloadIndex += 1
      this.modeler = null // 避免 panel 异常
    },
    changeLabelEditingStatus(status) {
      // eslint-disable-next-line vue/no-undef-properties
      this.addis.labelEditing = status ? { labelEditingProvider: ['value', ''] } : false
      this.reloadProcessDesigner()
    },
    elementClick(element) {
      // eslint-disable-next-line vue/no-undef-properties
      this.element = element
    },
    changePageMode(mode) {
      const theme = mode ? { stroke: '#ffffff', fill: '#333333' } : { stroke: '#000000', fill: '#ffffff' }
      const elements = this.modeler.get('elementRegistry').getAll()
      this.modeler.get('modeling').setColor(elements, theme)
    }
  }
}
</script>

<style lang="less">
@import 'diagram-js-minimap/assets/diagram-js-minimap.css';
@import 'bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css';

.flowable-edit {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: inline-grid;
  grid-template-columns: auto max-content;

  .toggle-mode {
    display: none;
  }
}
</style>
