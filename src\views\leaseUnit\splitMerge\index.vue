<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd" v-auth="'biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:add'">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:importExcel'">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button
          :loading="exportLoading"
          @click="handleExport"
          v-auth="'biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:exportXls'"
        >
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="submit"><div class="primary-btn" @click="handleBatchSubmit">批量提交</div></a-menu-item>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.number"
          placeholder="搜索编号"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_AuditStatus" type="dot"></status-tag>
        </template>
        <template v-if="column.dataIndex === 'remark'">
          <div class="line-clamp-2" :title="record.remark">{{ record.remark }}</div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span
            class="primary-btn"
            @click="handleDetail(record)"
            v-auth="'biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:view'"
          >
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item key="submit" v-if="['BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleSubmit(record)">提交</div>
                </a-menu-item>
                <a-menu-item key="back" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleBack(record)">撤回</div>
                </a-menu-item>
                <a-menu-item key="audit" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleAudit(record)">审核</div>
                </a-menu-item>
                <a-menu-item key="unAudit" v-if="record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleUnAudit(record)">反审核</div>
                </a-menu-item>
                <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <edit-lease-unit-split-merge ref="editDrawerRef" @refresh="onTableChange"></edit-lease-unit-split-merge>
    <lease-unit-split-merge-detail ref="detailDrawerRef" @refresh="onTableChange"></lease-unit-split-merge-detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('租赁单元拆合单导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import EditLeaseUnitSplitMerge from './components/EditLeaseUnitSplitMerge.vue'
import LeaseUnitSplitMergeDetail from './components/LeaseUnitSplitMergeDetail.vue'
import {
  getLeaseUnitSplitMergeBillList,
  exportExcel,
  importExcel,
  deleteLeaseUnitSplitMergeBill,
  deleteBatchLeaseUnitSplitMergeBill,
  submitLeaseUnitSplitMergeBill,
  queryLeaseUnitSplitMergeBillEntryOriByMainId,
  queryLeaseUnitSplitMergeBillEntryDestByMainId,
  auditLeaseUnitSplitMergeBill,
  unAuditLeaseUnitSplitMergeBill,
  backLeaseUnitSplitMergeBill
} from './apis'

const editDrawerRef = ref()
const detailDrawerRef = ref()
const commonImportRef = ref()
const columnSetRef = ref()

const exportLoading = ref(false)

const searchParams = reactive({
  column: 'number',
  order: 'desc',
  number: undefined,
  manageCompany: undefined,
  splitMergeType: undefined,
  status: undefined,
  bizDate: undefined,
  createBy: undefined,
  operator: undefined,
  operatorDepart: undefined,
  remark: undefined
})

const searchList = reactive([
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择物业管理公司' },
  {
    label: '拆合类型',
    name: 'splitMergeType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_LeaseUnitSplitMergeBill_SplitMergeType',
    placeholder: '请选择拆合类型'
  },
  { label: '状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus', placeholder: '请选择状态' },
  { label: '业务日期', name: 'bizDate', type: 'date', placeholder: '请选择业务日期' },
  { label: '提交人', name: 'createBy', type: 'user-select', placeholder: '请选择提交人' },
  { label: '经办人', name: 'operator', type: 'user-select', placeholder: '请选择经办人' },
  { label: '经办部门', name: 'operatorDepart', type: 'depart-select', placeholder: '请选择经办部门' },
  { label: '备注', name: 'remark', type: 's-input', placeholder: '请输入备注' }
])

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getLeaseUnitSplitMergeBillList)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '拆合类型', dataIndex: 'splitMergeType_dictText', width: 120 },
  { title: '状态', dataIndex: 'status', width: 120 },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '提交人', dataIndex: 'createBy_dictText', width: 120 },
  { title: '经办人', dataIndex: 'operator_dictText', width: 120 },
  { title: '经办部门', dataIndex: 'operatorDepart_dictText', width: 160, ellipsis: true },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 新建拆合单
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

const handleEdit = (record) => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:edit')) return
  editDrawerRef.value.open(record)
}

/**
 * 查看详情
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 查看审批
 */
const handleAudit = (record) => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: `确定要对租赁单元拆合单"${record.number}"执行审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await auditLeaseUnitSplitMergeBill({ id: record.id })
      message.success('审核成功')
      onTableChange()
    }
  })
}

/**
 * 执行反审核操作
 */
const handleUnAudit = (record) => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: `确定要对租赁单元拆合单"${record.number}"执行反审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await unAuditLeaseUnitSplitMergeBill({ id: record.id })
      message.success('反审核成功')
      onTableChange()
    }
  })
}

/**
 * 撤回租赁单元拆合单
 */
const handleBack = (record) => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回租赁单元拆合单"${record.number}"审核吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await backLeaseUnitSplitMergeBill({ id: record.id })
      message.success('撤回成功')
      onTableChange()
    }
  })
}

/**
 * 删除单条记录
 */
const handleDelete = (record) => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnitSplitMergeBill({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除
 */
const handleBatchDelete = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:deleteBatch')) return
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await deleteBatchLeaseUnitSplitMergeBill({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 提交单个拆合单
 */
const handleSubmit = (record) => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:submit')) return
  Modal.confirm({
    title: '确认提交',
    content: '确定要提交该条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      const oriRes = await queryLeaseUnitSplitMergeBillEntryOriByMainId({ id: record.id })
      const destRes = await queryLeaseUnitSplitMergeBillEntryDestByMainId({ id: record.id })

      if (!oriRes.result?.length || !destRes.result?.length) {
        message.error('源租赁单元或目标租赁单元信息不完整，无法提交！')
        return
      }

      // 获取源单元和目标单元数据
      const sourceUnits = oriRes.result.map((entry) => {
        return entry.leaseUnitObject || entry
      })
      const targetUnits = destRes.result.map((entry) => {
        return entry.leaseUnitObject || entry
      })

      // 验证租赁面积
      const sourceLeaseAreaSum = sourceUnits.reduce((sum, unit) => {
        return sum + (parseFloat(unit.leaseArea) || 0)
      }, 0)

      const targetLeaseAreaSum = targetUnits.reduce((sum, unit) => {
        return sum + (parseFloat(unit.leaseArea) || 0)
      }, 0)

      if (targetLeaseAreaSum < sourceLeaseAreaSum) {
        message.error(
          `目标租赁单元的租赁面积总和(${targetLeaseAreaSum}㎡)不能小于源租赁单元的租赁面积总和(${sourceLeaseAreaSum}㎡)`
        )
        return
      }

      const submitData = {
        ...record,
        leaseUnitSplitMergeBillEntryOriList: sourceUnits.map((unit) => ({
          leaseUnit: unit.id,
          leaseUnitObject: unit,
          houseOwnerObject: unit.houseOwnerObject,
          parent: record.id
        })),
        leaseUnitSplitMergeBillEntryDestList: targetUnits.map((unit) => ({
          leaseUnit: unit.id,
          leaseUnitObject: unit,
          houseOwnerObject: unit.houseOwnerObject,
          parent: record.id
        }))
      }

      await submitLeaseUnitSplitMergeBill(submitData)
      message.success('提交成功')
      onTableChange()
    }
  })
}

/**
 * 批量提交拆合单
 */
const handleBatchSubmit = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:submit')) return
  if (!selectedRowKeys.value?.length) {
    message.warning('请至少选择一条记录')
    return
  }
  Modal.confirm({
    title: '确认批量提交',
    content: `确定要提交选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      let successCount = 0
      let failCount = 0

      // 并行处理每条记录
      const promises = selectedRows.value.map((record) => {
        return (async () => {
          const [oriRes, destRes] = await Promise.all([
            queryLeaseUnitSplitMergeBillEntryOriByMainId({ id: record.id }),
            queryLeaseUnitSplitMergeBillEntryDestByMainId({ id: record.id })
          ])

          if (!oriRes.result?.length || !destRes.result?.length) {
            return { success: false, error: '源租赁单元或目标租赁单元信息不完整' }
          }

          // 获取源单元和目标单元数据
          const sourceUnits = oriRes.result.map((entry) => {
            return entry.leaseUnitObject || entry
          })
          const targetUnits = destRes.result.map((entry) => {
            return entry.leaseUnitObject || entry
          })

          // 验证租赁面积
          const sourceLeaseAreaSum = sourceUnits.reduce((sum, unit) => {
            return sum + (parseFloat(unit.leaseArea) || 0)
          }, 0)

          const targetLeaseAreaSum = targetUnits.reduce((sum, unit) => {
            return sum + (parseFloat(unit.leaseArea) || 0)
          }, 0)

          if (targetLeaseAreaSum < sourceLeaseAreaSum) {
            return {
              success: false,
              error: `租赁面积不匹配：目标(${targetLeaseAreaSum}㎡) < 源(${sourceLeaseAreaSum}㎡)`
            }
          }

          const submitData = {
            ...record,
            leaseUnitSplitMergeBillEntryOriList: sourceUnits.map((unit) => ({
              leaseUnit: unit.id,
              leaseUnitObject: unit,
              houseOwnerObject: unit.houseOwnerObject,
              parent: record.id
            })),
            leaseUnitSplitMergeBillEntryDestList: targetUnits.map((unit) => ({
              leaseUnit: unit.id,
              leaseUnitObject: unit,
              houseOwnerObject: unit.houseOwnerObject,
              parent: record.id
            }))
          }

          await submitLeaseUnitSplitMergeBill(submitData)
          return { success: true }
        })()
      })

      const results = await Promise.allSettled(promises)

      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            successCount++
          } else {
            failCount++
            if (result.value.error) {
              message.error(result.value.error)
            }
          }
        } else {
          failCount++
        }
      })

      // 显示提交结果
      if (successCount > 0) {
        message.success(`成功提交 ${successCount} 条记录`)
      }
      if (failCount > 0) {
        message.error(`${failCount} 条记录提交失败`)
      }

      // 清空选择并刷新表格
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 导入
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('租赁单元拆合单清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 搜索输入防抖处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
/**
 * 表格变化事件处理
 */
const onTableChange = ({ current = pagination.value.current, pageNo, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: pageNo ?? current, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})
</script>
