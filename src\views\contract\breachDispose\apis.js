import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/liquidatedDamages/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/liquidatedDamages/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/importExcel',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/unAudit',
    data
  })
}

export const save = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/edit',
    data
  })
}

export const withdraw = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/liquidatedDamages/back',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/contractmanage/liquidatedDamages/deleteBatch',
    params
  })
}

// 违约金处置单-通过合同查询违约租赁单元
export const queryBreachLeaseUnit = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/liquidatedDamages/queryContractLeaseUnitsByContract',
    params
  })
}
