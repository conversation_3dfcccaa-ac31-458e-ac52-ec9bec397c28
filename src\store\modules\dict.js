import { queryAllDictItems } from '@/views/system/dict/apis'

export const useDictStore = defineStore('dictStore', {
  state: () => ({
    dict: {},
    // 系统管理-用户管理-用户状态
    userStatus: {
      NORMAL: 1, // 正常
      FROZEN: 2 // 已冻结
    },
    // 系统管理-菜单管理-菜单类型
    menuType: {
      PARENT_MENU: 0, // 一级菜单
      CHILD_MENU: 1, // 子菜单
      PERMISSION: 2 // 按钮/权限
    }
  }),
  actions: {
    async getAllDict() {
      const { result } = await queryAllDictItems()
      Object.assign(this.dict, result)
    },
    getDictItems(code) {
      if (!this.dict) return []
      return this.dict[code] || []
    }
  }
})

export const isOrNotDic = [
  { label: '是', value: true },
  { label: '否', value: false }
]
export const getDicName = (key, dic = []) => {
  const obj = dic.find((item) => item.dictKey === key)
  if (obj) {
    return obj.dictValue
  }
  return ''
}
