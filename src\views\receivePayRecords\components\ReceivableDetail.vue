<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="60%"
    title="选择应收明细"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form autocomplete="off" layout="inline" class="!mb-[16px]">
      <a-form-item label="客户名称">
        <s-input
          class="!w-[280px]"
          v-model="search.name"
          placeholder="搜索客户名称"
          @input="handleInput"
          allow-clear
        ></s-input>
      </a-form-item>
      <a-form-item>
        <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ y: '50vh', x: 2000 }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>
<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getPage } from '@/views/statement/receiveCertificate/apis.js'
const { multiple } = defineProps({
  multiple: { type: Boolean, default: false }
})
const emits = defineEmits(['selectChange'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = () => {
  visible.value = true
  onTableChange()
}
defineExpose({ open })
const columns = [
  { title: '单据编号', dataIndex: 'username', width: 120, fixed: 'left' },
  { title: '客户名称', dataIndex: 'realname', width: 100 },
  { title: '合同', dataIndex: 'sex', width: 80 },
  { title: '租赁单元', dataIndex: 'phone', width: 120 },
  { title: '款项类型', dataIndex: 'orgCodeTxt' },
  { title: '结算状态', dataIndex: 'orgCodeTxt' },
  { title: '期数/总期数', dataIndex: 'orgCodeTxt' },
  { title: '款项金额', dataIndex: 'orgCodeTxt' },
  { title: '调整金额', dataIndex: 'orgCodeTxt' },

  { title: '应收金额', dataIndex: 'orgCodeTxt' },
  { title: '已收金额', dataIndex: 'orgCodeTxt' },
  { title: '剩余金额', dataIndex: 'orgCodeTxt' },
  { title: '应收日期', dataIndex: 'orgCodeTxt' },
  { title: '结清日期', dataIndex: 'orgCodeTxt' }
]
const { list, pagination, tableLoading, onTableFetch } = usePageTable(getPage)
const { selectedRows, selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
const search = ref({
  column: 'createTime',
  order: 'desc',
  name: ''
})
const searchList = []
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}
const handleConfirm = () => {
  emits('selectChange', selectedRows)
  handleCancel()
}

const handleCancel = () => {
  visible.value = false
}
</script>
