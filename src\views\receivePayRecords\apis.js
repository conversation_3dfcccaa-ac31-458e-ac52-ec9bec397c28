import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
// 分页数据
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/payExplainBook/list',
    params
  })
}
// 收付款记录-未核销明细列表
export const getUnConsumedPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/payExplainBook/unConsumedPage',
    params
  })
}
// 收付款记录-未核销明细f7列表
export const getUnConsumedF7List = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/payExplainBook/unConsumedF7List',
    params
  })
}
export const add = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/payExplainBook/add',
    data
  })
}
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/payExplainBook/edit',
    data
  })
}
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/payExplainBook/submit',
    data
  })
}
// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/payExplainBook/queryById?id=${id}`
  })
}
// 收付款记录-分录主表ID查询
export const queryPayExplainBookEntryByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/payExplainBook/queryPayExplainBookEntryByMainId?id=${id}`
  })
}
// 审核
export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/payExplainBook/audit',
    data
  })
}
// 反审核
export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/payExplainBook/unAudit',
    data
  })
}
// 撤回
export const back = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/payExplainBook/back',
    data
  })
}
// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/payExplainBook/delete?id=${id}`
  })
}
// 通过id删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/payExplainBook/deleteBatch?ids=${ids}`
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/payExplainBook/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/tripartsettle/payExplainBook/importExcel', data, controller)
}
