<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="ruleForm.id ? '编辑自定义常用功能' : '新增自定义常用功能'"
    placement="right"
    width="1072px"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="ruleForm" ref="formRef" :rules="rules" :label-col="{ style: { width: '80px' } }" autocomplete="off">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="名称" name="name">
            <a-input v-model:value="ruleForm.name" placeholder="请输入名称" :maxlength="200" show-count></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="所属模块" name="module">
            <dict-select
              v-model="ruleForm.module"
              placeholder="请选择所属模块"
              code="CT_BASE_ENUM_CustomCommonFunction_Module"
            ></dict-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="路径" name="url">
            <a-input v-model:value="ruleForm.url" placeholder="请输入路径" :maxlength="200" show-count></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="图标" name="icon">
            <choose-icon v-model="ruleForm.icon" placeholder="请选择图标"></choose-icon>
            <!-- <a-input v-model:value="ruleForm.icon" placeholder="请输入图标" :maxlength="200" show-count></a-input> -->
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <template #footer>
      <a-button type="primary" @click="handleConfirm(0)" :loading="submitLoading">提交</a-button>
      <a-button type="primary" ghost @click="handleStash" :loading="stashLoading">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>
<script setup>
import ChooseIcon from '@/views/system/menu/components/ChooseIcon.vue'
import { message } from 'ant-design-vue'
import { detailById, stash, submit, edit } from '../apis'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (id) => {
  visible.value = true
  if (id) {
    getDetailById(id)
  }
}
defineExpose({ open })
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  Object.assign(ruleForm, result)
}
const ruleForm = reactive({
  id: '',
  name: '',
  icon: '',
  url: ''
})
const rules = computed(() => ({
  name: [{ required: true, message: '请输入名称', trigger: ['blur'] }],
  module: [{ required: true, message: '请选择所属模块', trigger: ['change'] }],
  url: [{ required: true, message: '请输入路径', trigger: ['blur'] }],
  icon: [{ required: true, message: '请上传图标', trigger: ['blur'] }]
}))

const formRef = ref()
const submitLoading = ref(false)
// 提交
const handleConfirm = async (type) => {
  await formRef.value.validate()
  submitLoading.value = true
  try {
    await submit(ruleForm)
    message.success('提交成功')
    emits('loadData')
    if (type) {
      return clearForm()
    }
    handleCancel()
  } finally {
    submitLoading.value = false
  }
}

// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  await formRef.value.validate()
  stashLoading.value = true
  try {
    const { message: msg } = await (ruleForm.id ? edit(ruleForm) : stash(ruleForm))
    message.success(msg)
    emits('loadData')
    stashLoading.value = false
  } finally {
    stashLoading.value = false
  }
}

const clearForm = () => {
  formRef.value.clearValidate()
}
// 取消
const handleCancel = () => {
  clearForm()
  ruleForm.id = ''
  ruleForm.bizDate = ''
  ruleForm.customer = ''
  ruleForm.operator = ''
  ruleForm.operatorDepart = ''
  ruleForm.remark = ''
  ruleForm.manageCompany = ''
  ruleForm.orderBillChargeDetailList = []
  visible.value = false
}
</script>

<style scoped lang="less">
// 自定义表头文本必填
.table-header-col {
  &::after {
    display: inline-block;
    content: '*';
    color: var(--color-red-600);
  }
}
// 移除按钮hover样式
.remove-btn {
  font-size: 16px;
  &:hover {
    color: var(--color-red-600);
  }
}
</style>
