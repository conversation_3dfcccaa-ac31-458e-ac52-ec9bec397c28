<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitch(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer mr-[16px]"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitch(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span v-if="detail.status === 'AUDITOK'" class="primary-btn" @click="handleUnAudit">取消通过</span>
          <template v-else>
            <span class="primary-btn" @click="handleEdit">编辑</span>
            <a-dropdown>
              <span class="primary-btn">
                更多
                <i class="a-icon-arrow-down"></i>
              </span>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <div class="primary-btn" @click="handleDelete">删除</div>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">房产税计提单</h2>
        <status-tag :dict-value="detail.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.updateBy_dictText }} 提交于{{ detail.updateTime }}</span>
      </div>
      <h4 class="text-[16px] font-bold mb-[12px]">基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">计提公司: {{ detail.jtCompany_dictText }}</span>
        <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
        <span class="w-[50%]">所属年月: {{ detail.belongYm }}</span>
        <span class="w-[50%]">含未审核合同: {{ detail.isIncludeNoAuditContract ? '是' : '否' }}</span>
        <span class="w-full">备注: {{ detail.remark }}</span>
      </div>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[12px]">计提明细</h4>
      <a-table
        :data-source="buildingTaxJTBillEntryList"
        :columns="columns"
        row-key="id"
        :scroll="{ x: 1500 }"
        :pagination="false"
      ></a-table>
    </a-spin>

    <template v-if="detail.status === 'AUDITING'" #footer>
      <a-button type="primary" @click="handleAudit">确认通过</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, audit, unAudit, deleteBatch, queryBuildingUseTaxJTBill } from '../apis.js'
import { Modal, message } from 'ant-design-vue'
import { formatCurrency, formatArea } from '@/utils/number'

const { dataList } = defineProps({
  dataList: { required: true, type: Array }
})

const emit = defineEmits(['edit', 'audit', 'refresh'])

const visible = ref(false)

const columns = [
  { title: '产权名称', dataIndex: 'houseOwner_dictText', width: 160, fixed: 'left' },
  { title: '土地面积(㎡)', dataIndex: 'structureArea', width: 120, customRender: ({ text }) => formatArea(text) },
  { title: '房产类型', dataIndex: 'houseType_dictText', width: 160 },
  {
    title: '产权计税原值',
    dataIndex: 'houseTaxOrgValue',
    width: 150,
    customRender: ({ text }) => formatCurrency(text)
  },
  { title: '视同销售计提税', dataIndex: 'stSaleJtRate', width: 150 },
  { title: '从价月税率', dataIndex: 'leaseMonthRate', width: 150, customRender: ({ text }) => text && `${text}%` },
  { title: '从价折扣率', dataIndex: 'priceDiscRate', width: 150, customRender: ({ text }) => text && `${text}%` },
  { title: '租赁单元', dataIndex: 'leaseUnit', width: 130 },
  {
    title: '租赁单元面积',
    dataIndex: 'leaseUnitStructureArea',
    width: 130,
    customRender: ({ text }) => formatArea(text)
  },
  { title: '单元计费原值', dataIndex: 'leaseUnitTaxOrgValue', width: 130 },
  { title: '从价计税开始年月', dataIndex: 'effectDate', width: 150 },
  { title: '从价计税结束年月', dataIndex: 'taxExpireDate', width: 150 },
  { title: '合同', dataIndex: 'contract', width: 130 },
  { title: '合同状态', dataIndex: 'contractBizStatus', width: 130 },
  { title: '账单明细', dataIndex: 'detailBill', width: 130 },
  { title: '账单状态', dataIndex: 'detailBillBizStatus', width: 130 },
  { title: '增值税率', dataIndex: 'addTaxRate', width: 130 },
  { title: '合同开始时间', dataIndex: 'startDate', width: 130 },
  { title: '合同结束时间', dataIndex: 'endDate', width: 130 },
  { title: '租金收入', dataIndex: 'rentAmount', width: 130, customRender: ({ text }) => formatCurrency(text) },
  {
    title: '含税租金收入',
    dataIndex: 'containTaxRentAmount',
    width: 130,
    customRender: ({ text }) => formatCurrency(text)
  },
  { title: '客户', dataIndex: 'customer', width: 130 },
  {
    title: '税金',
    dataIndex: 'taxAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => formatCurrency(text)
  },
  {
    title: '调整额',
    dataIndex: 'adjustAmount',
    width: 180,
    fixed: 'right',
    customRender: ({ text }) => formatCurrency(text)
  },
  {
    title: '调整后税金',
    dataIndex: 'adjustAfterAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => formatCurrency(text)
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

const open = (id) => {
  visible.value = true
  loadDetail(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const currentIndex = ref(0)
const handleSwitch = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  loadDetail(dataList[index].id)
}

const loading = ref(false)
const detail = reactive({})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  loading.value = false
  loadBillEntryList()
}

const buildingTaxJTBillEntryList = ref([])
const loadBillEntryList = async () => {
  const { result } = await queryBuildingUseTaxJTBill({ id: detail.id })
  buildingTaxJTBillEntryList.value = result
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleAudit = () => {
  Modal.confirm({
    title: '确认通过该房产税计提单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await audit(detail)
      message.success(msg)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleUnAudit = () => {
  Modal.confirm({
    title: '确认取消通过该房产税计提单？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await unAudit(detail)
      message.success(msg)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该房产税计提单？',
    content: '',
    centered: true,
    onOk: async () => {
      const { message: msg } = await deleteBatch({ ids: detail.id })
      message.success(msg)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
