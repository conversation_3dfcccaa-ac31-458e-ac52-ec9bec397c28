<template>
  <div class="flex flex-col h-full">
    <div class="flex-1 flex content overflow-hidden">
      <div class="left flex flex-col w-[300px]">
        <div class="text-[18px] font-bold mb-[10px]">
          <a-input-search v-model:value="name" placeholder="按名称搜索" enter-button @search="loadFlowBizList" />
        </div>
        <div class="flex-1 overflow-y-auto">
          <a-tree
            v-if="flowBizBillTree.length"
            default-expand-all
            :tree-data="flowBizBillTree"
            :field-names="{ children: 'children', title: 'name', key: 'classNameKey' }"
            @select="flowBizBillSelect"
          >
            <template #title="{ name, classNameKey }">
              <div class="py-[10px] px-[5px]">
                <span>{{ name }}</span>
                <span v-if="classNameKey">
                  <span class="mx-[5px]">--</span>
                  <a-tag color="blue">{{ className<PERSON>ey }}</a-tag>
                </span>
              </div>
            </template>
          </a-tree>
        </div>
      </div>
      <div class="right flex-1 p-[16px]">
        <div class="flex items-center justify-between mb-[16px]">
          <div class="flex">
            <a-button type="primary" @click="handleAdd">
              <i class="a-icon-plus"></i>
              新建
            </a-button>
            <a-button @click="handleImport">
              <i class="a-icon-import-right"></i>
              导入
            </a-button>
            <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
              <i class="a-icon-refresh"></i>
              刷新
            </a-button>
            <s-input
              v-model="params.name"
              placeholder="搜索名称"
              class="ml-[10px] !w-[280px]"
              @input="handleInput"
            ></s-input>
            <filter-more
              :params="params"
              :search-list="searchList"
              width="320px"
              label-width="100px"
              @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
            ></filter-more>
          </div>
          <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
        </div>
        <a-table
          :data-source="list"
          :columns="columns"
          :loading="tableLoading"
          :pagination="pagination"
          row-key="deploymentId"
          :scroll="{ y: tableHeight }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          @change="onTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'version'">
              <a-tag color="blue">V{{ record.version }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'suspensionState'">
              <a-tag v-if="record.suspensionState === 1" color="success">激活</a-tag>
              <a-tag v-if="record.suspensionState === 2" color="warning">挂起</a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <span class="primary-btn" @click="handleEdit(record)">编辑</span>
              <a-dropdown>
                <span class="primary-btn">
                  更多
                  <i class="a-icon-arrow-down"></i>
                </span>
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <div class="primary-btn" @click="handleUpdate(record)">
                        {{ record.suspensionState === 1 ? '挂起' : '激活' }}
                      </div>
                    </a-menu-item>
                    <a-menu-item>
                      <div class="primary-btn" @click="handleCopy(record)">复制</div>
                    </a-menu-item>
                    <a-menu-item>
                      <div class="primary-btn" @click="handleRemove(record)">删除</div>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <import-process ref="importRef" @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })" />
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, updateState, copy, del } from './apis.js'
import { bizBillTreeSelectList } from '@/views/flowable/flowBizBill/apis'
import { Modal, message } from 'ant-design-vue'
import ImportProcess from './components/ImportProcess.vue'

const params = reactive({
  name: undefined,
  processKey: undefined,
  classNameKey: undefined
})
const flowBizBillList = ref([])

const searchList = computed(() => [{ label: '流程定义key', name: 'processKey', type: 's-input' }])

const defaultColumns = [
  { title: '流程定义key', dataIndex: 'processKey', width: 180, fixed: 'left' },
  { title: '流程业务单据', dataIndex: 'classNameKey', width: 180 },
  { title: '流程使用公司', dataIndex: 'category_dictText', width: 230 },
  { title: '流程名称', dataIndex: 'name', width: 180 },
  { title: '流程版本', dataIndex: 'version', width: 100 },
  { title: '状态', dataIndex: 'suspensionState', width: 100 },
  { title: '部署时间', dataIndex: 'deploymentTime', width: 180 },
  { title: '操作', dataIndex: 'action', width: 130, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'deploymentId')

const router = useRouter()
const handleAdd = () => {
  router.push({
    path: '/flowable/definition/edit/-1'
  })
}
const handleEdit = (data) => {
  router.push({
    path: `/flowable/definition/edit/${data.deploymentId}`
  })
}

const handleUpdate = ({ deploymentId, suspensionState }) => {
  const title = `确定${suspensionState === 1 ? '挂起' : '激活'}流程建模设计？`
  Modal.confirm({
    title,
    content: '',
    centered: true,
    onOk: async () => {
      await updateState({ deployId: deploymentId, state: suspensionState === 1 ? 2 : 1 })
      clearSelection()
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}

const handleCopy = ({ deploymentId, processKey }) => {
  Modal.confirm({
    title: `确定复制${processKey}流程建模设计？`,
    content: '',
    centered: true,
    onOk: async () => {
      await copy({ deployId: deploymentId })
      refresh()
    }
  })
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除流程建模设计信息？',
    content: '',
    centered: true,
    onOk: async () => {
      await del({ deployId: data ? data.deploymentId : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const importRef = ref()
const handleImport = () => {
  importRef.value.open(flowBizBillList.value)
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const name = ref('')
const flowBizBillTree = ref([])
const loadFlowBizList = async () => {
  const { result } = await bizBillTreeSelectList({ name: name.value })
  flowBizBillList.value = result
  flowBizBillTree.value = [{ classNameKey: '', name: '业务单据', children: result }]
}

const flowBizBillSelect = ([classNameKey]) => {
  params.classNameKey = classNameKey
  onTableChange()
}

onMounted(() => {
  onTableChange()
  loadFlowBizList()
})
</script>

<style lang="less" scoped>
.content {
  border-radius: 10px;
  border: 1px solid #e6e9f0;
}

.left {
  padding: 16px;
  border-right: 1px solid #e6e9f0;
  flex-shrink: 0; /* 设置为 0 防止被挤压 */
}

.right {
  overflow: hidden;
}

:deep(.ant-tree-treenode) {
  width: 100%;
}

:deep(.ant-tree-node-content-wrapper) {
  flex: 1;
}
</style>
