<template>
  <a-drawer
    v-model:open="visible"
    class="edit-lease-unit-state-change-drawer common-drawer"
    title="新建租赁单元状态变更"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">变更基础信息</h4>
      <a-form ref="basicFormRef" :model="formData" :label-col="{ style: { width: '140px' } }" :rules="rules">
        <a-form-item label="物业管理公司" name="manageCompany">
          <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司"></company-select>
        </a-form-item>

        <a-form-item label="变更目标状态" name="destStatus">
          <dict-select
            v-model="formData.destStatus"
            placeholder="请选择目标状态"
            code="CT_BASE_ENUM_LeaseUnit_BizStatus"
          ></dict-select>
        </a-form-item>

        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>

        <a-form-item label="变更说明" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="变更说明" :maxlength="255" :rows="4" show-count />
        </a-form-item>
      </a-form>
      <div class="flex justify-between items-center mb-4">
        <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">租赁单元</h4>
      </div>
      <!-- 当有数据时显示表格 -->
      <a-table
        class="lease-unit-table"
        :columns="columns"
        :data-source="formData.leaseUnitStateChangeReqBillEntryList"
        :pagination="false"
        row-key="id"
        :scroll="{ x: 1400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'leaseUnit'">
            {{ record.leaseUnit }}
          </template>
          <template v-if="column.dataIndex === 'bizStatus'">
            <a-tag color="success" v-if="record.bizStatus === 'InLease'">{{ record.bizStatus_dictText }}</a-tag>
          </template>
          <template v-if="column.dataIndex === 'destStatusBeginDate'">
            <a-date-picker
              v-model:value="record.destStatusBeginDate"
              value-format="YYYY-MM-DD"
              placeholder="请选择日期"
              style="width: 100%"
            />
          </template>
        </template>
      </a-table>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { addLeaseUnitStateChangeReqBill } from '@/views/leaseUnit/stateChange/apis'
import { useUserStore } from '@/store/modules/user'

const emits = defineEmits(['refresh'])

const store = useUserStore()

const visible = ref(false)
const confirmLoading = ref(false)
const basicFormRef = ref()

const formDataDefault = {
  id: undefined,
  manageCompany: undefined,
  destStatus: '',
  bizDate: undefined,
  remark: undefined,
  leaseUnitStateChangeReqBillEntryList: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  destStatus: [{ required: true, message: '请选择变更目标状态', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  remark: [{ required: true, message: '请输入变更说明', trigger: 'blur' }]
}

const columns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'leaseUnitAddress', width: 160, ellipsis: true },
  { title: '租赁归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '原业务状态', dataIndex: 'bizStatus', width: 120 },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '业务日期', dataIndex: 'expireDate', width: 120 },
  { title: '目标状态开始日期', dataIndex: 'destStatusBeginDate', width: 180, fixed: 'right' }
]

/**
 * 打开编辑抽屉
 * @param {Array} leaseUnitList - 租赁单元列表
 */
const open = (leaseUnitList = []) => {
  Object.assign(formData, formDataDefault)
  formData.manageCompany = store.userInfo.currentCompany
  formData.bizDate = dayjs().format('YYYY-MM-DD')

  // 设置传入的租赁单元列表
  if (leaseUnitList && leaseUnitList.length > 0) {
    formData.leaseUnitStateChangeReqBillEntryList = leaseUnitList.map((unit) => ({
      ...unit,
      leaseUnit: unit.name,
      leaseUnitAddress: unit.detailAddress,
      destStatusBeginDate: undefined
    }))
  }

  visible.value = true
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  // 只有在正式保存时才进行表单验证
  if (!isTemporary) {
    await basicFormRef.value.validateFields()

    // 验证租赁单元的目标状态开始日期
    if (formData.leaseUnitStateChangeReqBillEntryList && formData.leaseUnitStateChangeReqBillEntryList.length > 0) {
      const unitsWithoutDate = formData.leaseUnitStateChangeReqBillEntryList.filter((unit) => !unit.destStatusBeginDate)
      if (unitsWithoutDate.length > 0) {
        message.error('请为所有租赁单元选择目标状态开始日期')
        return
      }
    }
  }

  confirmLoading.value = true

  try {
    await addLeaseUnitStateChangeReqBill(formData)
    const action = isTemporary ? '暂存' : '保存'
    message.success(`${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 保存表单
 */
const handleSave = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 取消编辑
 */
const handleCancel = () => {
  visible.value = false
  basicFormRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  emits('refresh')
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.edit-lease-unit-state-change-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-date-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>
