<template>
  <a-modal v-model:open="visible" title="脚本模拟" width="900px" :mask-closable="false">
    <div>
      <a-tabs size="large">
        <a-tab-pane v-for="(item, index) in entities" :key="index + 1" :tab="item.name">
          <FormulaCodemirror v-model="mockData[item.clazz]" disabled beautify />
        </a-tab-pane>
        <a-tab-pane :key="entities.length + 1" tab="脚本">
          <FormulaCodemirror v-model="script" disabled beautify />
        </a-tab-pane>
      </a-tabs>
      <a-row type="flex" gutter="10" class="my-[10px]">
        <a-col>执行结果：</a-col>
        <a-col flex="auto">
          <a-textarea v-model:value="result" :rows="2"></a-textarea>
        </a-col>
        <a-col>
          <a-button type="primary" :loading="iconLoading" @click="handleExecute">
            <template #icon>
              <i :class="[executeOk ? 'a-icon-pass-solid' : 'a-icon-loading']"></i>
            </template>
            <span>执行</span>
          </a-button>
        </a-col>
      </a-row>
    </div>
    <template #footer></template>
  </a-modal>
</template>

<script setup>
import { simulate } from '../apis'
import { message } from 'ant-design-vue'

const visible = ref(false)

const open = (clazz, formula) => {
  mock(clazz, formula)
  visible.value = true
}

const script = ref('')
const entities = ref([{ name: '测试数据', clazz: 'WaterShareBillEntry' }])

const iconLoading = ref(false)
const executeOk = ref(true)
const result = ref('')

const handleExecute = async () => {
  const data = toJson()
  if (!data) {
    iconLoading.value = false
    executeOk.value = false
    setTimeout(() => {
      executeOk.value = true
    }, 3000)
    return
  }
  try {
    const res = await simulate({ script: script.value, params: data })
    result.value = res.result
    executeOk.value = true
  } catch {
    iconLoading.value = false
  }
}

const mock = async (clazz, formula) => {
  entities.value = clazz
  script.value = formula
  for (const item of clazz) {
    // eslint-disable-next-line no-await-in-loop
    await getJSON(item.clazz)
  }
}

const test = async (clazz, formula) => {
  await mock(clazz, formula)
  const data = toJson()
  if (!data) {
    return
  }
  try {
    await simulate({ script: formula, params: data })
    return true
  } catch (error) {
    return error
  }
}

const mockData = reactive({})
const modules = import.meta.glob('./json/*Mock.json')
const getJSON = async (clazz) => {
  const filePath = `./json/${clazz}Mock.json`
  const loader = modules[filePath]
  if (!loader) {
    message.error(`找不到 ${filePath}`)
    return
  }
  try {
    const response = await loader()
    mockData[clazz] = JSON.stringify(response.default)
  } catch (error) {
    message.error(error.message || '提交失败')
  }
}

const toJson = () => {
  const data = {}
  for (const key in mockData) {
    // eslint-disable-next-line no-prototype-builtins
    if (mockData.hasOwnProperty(key)) {
      const element = mockData[key]
      try {
        data[key] = JSON.parse(element)
      } catch {
        // 通过key查找entities
        const finded = entities.value.find((item) => item.clazz === key)
        message.error(`${finded.name} 的值必须是 JSON 格式`)
        return false
      }
    }
  }
  return data
}

defineExpose({ open, test })
</script>
