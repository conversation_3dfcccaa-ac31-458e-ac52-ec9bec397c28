<template>
  <a-select
    v-bind="$attrs"
    show-search
    :value="value || undefined"
    :style="{ width }"
    :options="options"
    :filter-option="filterOption"
    :field-names="{ label: 'departName', value: 'id' }"
    allow-clear
    @change="onchange"
  ></a-select>
</template>

<script setup>
import { getCompanyTree } from '@/views/system/depart/apis'
import useUserStore from '@/store/modules/user'

const { modelValue, type } = defineProps({
  width: { type: String, default: '100%' },
  modelValue: { type: [Array, String, Number] },
  type: { type: String, default: 'user' } // 类型 user=根据当前登录用户获取所属公司列表 all=获取系统全部公司列表
})

const emit = defineEmits(['update:modelValue', 'change'])

const { companyList } = useUserStore()

const value = computed(() => {
  if (modelValue === undefined) return undefined
  if (modelValue === null) return null
  if (typeof modelValue === 'string') return modelValue
  return modelValue.map((item) => String(item))
})

const onchange = (val) => {
  emit('update:modelValue', val)
  emit('change', val)
}

const filterOption = (input, option) => {
  return option.departName.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const options = computed(() => {
  return type === 'all' ? allCompanyList.value : companyList.value
})

const allCompanyList = ref([])
const loadCompanyList = async () => {
  const { result } = await getCompanyTree()
  allCompanyList.value = result
}

onMounted(() => {
  if (type === 'all') {
    loadCompanyList()
  }
})
</script>
