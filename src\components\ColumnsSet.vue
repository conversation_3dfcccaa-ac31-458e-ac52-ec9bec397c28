表格列设置组件 使用说明： 1.在目标页面中，
<columns-set :default-columns="columns" ref="columnSetRef"></columns-set>
2.在script中，先声明默认columns const defaultColumns = [xxx] const columnSetRef = ref() const columns = computed(() =>
columnSetRef.value?.columns) 这样即可

<template>
  <a-popover
    placement="bottomRight"
    :arrow="false"
    trigger="click"
    overlay-class-name="columns-set-box"
    v-model:open="open"
  >
    <template #content>
      <div class="flex items-center mb-[12px] text-[14px] text-tertiary">
        <i class="a-icon-tips mr-[8px]"></i>
        <span>拖动可调整显示列顺序</span>
      </div>
      <section class="max-h-[50vh] overflow-y-auto no-scrollbar">
        <draggable v-model="columns" handle=".a-icon-move" class="hot-company-box" item-key="id" :move="onMove">
          <template #item="{ element }">
            <div class="flex items-center justify-between h-[40px]">
              <a-checkbox v-model:checked="element.checked">{{ element.title }}</a-checkbox>
              <i class="a-icon-move cursor-move text-secondary" v-if="showMoveBtn(element)"></i>
            </div>
          </template>
        </draggable>
      </section>
      <div class="flex items-center justify-between mt-[24px]">
        <span class="text-secondary cursor-pointer transition-colors hover:text-primary" @click="handleReset">
          <i class="a-icon-reset mr-[4px]"></i>
          恢复默认
        </span>
        <div>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSave">保存</a-button>
        </div>
      </div>
    </template>
    <a-button>
      <i class="a-icon-list"></i>
      列设置
    </a-button>
  </a-popover>
</template>

<script setup>
import draggable from 'vuedraggable'
import { useTableColumnsCacheStore } from '@/store/modules/tableColumnsCache'

const { defaultColumns } = defineProps({
  defaultColumns: { required: true, type: Array }
})

const store = useTableColumnsCacheStore()

const route = useRoute()

const columns = ref([])
const columnsResult = computed(() => {
  const list = columns.value.filter((item) => item.checked)
  list.forEach((item) => {
    const data = defaultColumns.find(
      (i) => i.title === item.title && (i.dataIndex === item.dataIndex || i.key === item.key)
    )
    if (data) {
      Object.assign(item, data)
    }
  })
  return list
})

const showMoveBtn = (element) => {
  return !(element.key === 'action' || element.dataIndex === 'action' || element.title === '操作' || element.fixed)
}

const open = ref(false)

const handleReset = () => {
  columns.value = defaultColumns.map((item, index) => {
    item.id = index
    item.checked = true
    return item
  })
}

const handleSave = () => {
  store.cache[route.path] = columns.value
  open.value = false
}

const handleCancel = () => {
  if (store.cache[route.path]) {
    columns.value = store.cache[route.path]
  } else {
    handleReset()
  }
  open.value = false
}

// 禁止操作栏，和已固定左右侧的列，进行拖拽排序
const onMove = (evt) => {
  const element = evt.relatedContext && evt.relatedContext.element // 拖拽到的目标元素(比如把第一个，拖拽到第五个，那么第五个元素就是这个element)
  return Boolean(element && showMoveBtn(element))
}

onMounted(() => {
  if (store.cache[route.path]) {
    columns.value = store.cache[route.path]
  } else {
    columns.value = defaultColumns.map((item, index) => {
      item.id = index
      item.checked = true
      return item
    })
  }
})

defineExpose({ columns: columnsResult })
</script>

<style lang="less">
.columns-set-box .ant-popover-inner {
  border: 1px solid #d7dae0;
  border-radius: 8px;
  box-shadow: 0px 4px 24px 0px rgba(29, 51, 92, 0.12);
  background-color: #fff;
  width: 340px;
  padding: 24px;
  // 元素正在被拖拽时的样式，这个类名由vuedraggable自带，勿改动
  .sortable-chosen {
    border: 1px solid var(--color-primary);
    padding: 10px;
    border-radius: 8px;
    background-color: #fff;
  }
}
</style>
