import { f7List } from '@/views/system/user/apis'
import { renderDict } from '@/utils/render'

export default {
  modalTitle: '选择用户',
  request: f7List,
  params: {
    column: 'createTime',
    order: 'desc',
    username: undefined,
    depart: undefined,
    sex: undefined,
    phone: undefined,
    status: 1
  },
  rowKey: 'id',
  displayKey: 'realname',
  keywordKey: 'realname',
  keywordPlaceholder: '搜索姓名',
  clearIgnoreKeys: ['status', 'depart'],
  statIgnoreKeys: ['status', 'depart'],
  columns: [
    { title: '账号', dataIndex: 'username', width: 120, fixed: 'left' },
    { title: '姓名', dataIndex: 'realname', width: 100 },
    { title: '性别', dataIndex: 'sex', width: 80, customRender: ({ text }) => renderDict(text, 'sex') },
    { title: '手机', dataIndex: 'phone', width: 120 },
    { title: '公司', dataIndex: 'currentCompany_dictText' },
    { title: '部门', dataIndex: 'currentDepart_dictText' }
  ],
  searchList: [
    {
      label: '性别',
      name: 'sex',
      type: 'a-select',
      options: [
        { name: '男', id: '1' },
        { name: '女', id: '2' }
      ]
    },
    { label: '手机号', name: 'phone', type: 's-input' }
  ]
}
