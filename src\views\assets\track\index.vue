<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-radio-group class="!m-0 !mb-[10px]" v-model:value="activeTab" style="margin: 8px" @change="activeTabChange">
          <a-radio-button v-for="item in tabs" :key="item.value" :value="item.value">
            {{ item.name }}
          </a-radio-button>
        </a-radio-group>
        <a-button
          v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:add'"
          class="ml-[16px] mb-[10px]"
          type="primary"
          @click="handleAdd"
        >
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button
          v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:importExcel'"
          class="mb-[10px]"
          @click="handleImport"
        >
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="pageTableSelectionData.selectedRowKeys.length">
          <a-button class="ml-[16px] mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div
                  v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:deleteBatch'"
                  class="primary-btn"
                  @click="handleRemove(false)"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button
          class="ml-[16px] mb-[10px]"
          @click="onTableChange({ pageNo: 1, pageSize: pageTableData.pagination.pageSize })"
        >
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]">
          <s-input v-model="search.number" placeholder="搜索单据编号" class="!w-[280px]" @input="handleInput"></s-input>
        </a-form-item>
        <a-form-item class="!mb-[10px]">
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="pageTableData.list"
      :columns="columns"
      :loading="pageTableData.tableLoading"
      :scroll="{ y: pageTableData.tableHeight, x: 1500 }"
      :pagination="pageTableData.pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys: pageTableSelectionData.selectedRowKeys,
        onChange: pageTableSelectionData.onSelectChange
      }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <!--
        暂存： 查看、编辑、删除
        审核中：有配置审批，且审批人不为空的才有：若全部审批人都还没完成审批 查看、撤回
                有配置审批，且审批人不为空的才有：已经有人完成审批，但审批流程还没结束 查看
        已撤回：查看、提交、编辑、删除
        审核通过：查看、反审核
        审核不通过：查看、编辑、提交
        -->
        <template v-if="column.dataIndex === 'action'">
          <span
            v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:view'"
            class="primary-btn"
            @click="rowView(record)"
          >
            查看
          </span>
          <!--  暂存 已撤回 审核不通过 才有 -->
          <span
            v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:edit'"
            v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)"
            class="primary-btn"
            @click="rowEdit(record)"
          >
            编辑
          </span>
          <!-- 待审批 -->
          <span
            v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:edit'"
            class="primary-btn"
            v-if="record.status === 'AUDITING'"
            @click="rowBack(record)"
          >
            撤回
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <!-- 除了暂存都要展示 -->
                <a-menu-item v-if="record.status !== 'TEMP'">
                  <div class="primary-btn" @click="rowViewApprove(record)">查看审批</div>
                </a-menu-item>
                <!-- 审核中才有审核操作（临时） -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:audit'"
                    class="primary-btn"
                    @click="rowVerify(record)"
                  >
                    审核通过(临时)
                  </div>
                </a-menu-item>
                <!-- 审核通过才能 反审核 -->
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div
                    v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:unAudit'"
                    class="primary-btn"
                    @click="rowReverse(record)"
                  >
                    反审核
                  </div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP'].includes(record.status)">
                  <div
                    v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:submit'"
                    class="primary-btn"
                    @click="rowResubmit(record)"
                  >
                    提交
                  </div>
                </a-menu-item>
                <!-- 暂存 已撤回 才有删除 -->
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div
                    v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:delete'"
                    class="primary-btn"
                    @click="handleRemove(record)"
                  >
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 查看 -->
    <detail ref="detailRef" @load-data="onTableChange"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产跟踪"
      :download-fn="() => requestFuncObj.exportExcel('资产跟踪数据导入模板.xls', { id: 0 })"
      :upload-fn="requestFuncObj.importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pageTableData.pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import Detail from './components/TrackDetail.vue'
import { renderDictTag } from '@/utils/render'
import usePageTable from '@/hooks/usePageTable'
import AddEdit from './components/AddEdit.vue'
import useTableSelection from '@/hooks/useTableSelection'
import {
  getAllList,
  getIdleAssetTrackingList,
  getOccupyAssetTrackingList,
  getBorrowAssetTrackingList,
  getSelfAssetTrackingList,
  allExportExcel,
  idleExportExcel,
  occupyExportExcel,
  borrowExportExcel,
  selfExportExcel,
  allImportExcel,
  idleImportExcel,
  occupyImportExcel,
  borrowImportExcel,
  selfImportExcel,
  allDeleteBatch,
  idleBatchDel,
  occupyBatchDel,
  borrowBatchDel,
  selfBatchDel,
  getIdleQueryById,
  getOccupyQueryById,
  getBorrowQueryById,
  getSelfQueryById,
  idleBack,
  occupyBack,
  borrowBack,
  selfBack,
  idleSubmit,
  occupySubmit,
  borrowSubmit,
  selfSubmit,
  idleUnAudit,
  occupyUnAudit,
  borrowUnAudit,
  selfUnAudit,
  idleAudit,
  occupyAudit,
  borrowAudit,
  selfAudit
} from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
  // 资产详情跳转过来新增
  if (route.query.adding) {
    addEditRef?.value.open()
  }
})

const route = useRoute()
const tabs = ref([
  { value: 0, name: '全部跟踪' },
  { value: 1, name: '闲置' },
  { value: 2, name: '占用' },
  { value: 3, name: '借用' },
  { value: 4, name: '自用' }
])
const activeTab = ref(0)
const columnSetRef = ref()
const search = ref({
  column: 'number',
  order: 'desc',
  number: undefined
})
const searchFilter = ref({})
const searchList = reactive([
  {
    label: '物业管理公司',
    name: 'manageCompany',
    type: 'companySelect',
    placeholder: '请选择物业管理公司'
  },
  {
    label: '跟踪类型',
    name: 'trackingType',
    type: 'dic',
    placeholder: '请选择跟踪类型',
    code: 'CT_BASE_ENUM_TrackingType'
  },
  {
    label: '业务状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择业务状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  {
    label: '业务日期',
    name: 'bizDate',
    type: 'date',
    placeholder: '请选择业务日期'
  }
  // {
  //   label: '跟踪资产',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入跟踪资产'
  // }
])
const getTypeRequestObj = (type) => {
  const funcObj = {
    back: null,
    submit: null,
    unAudit: null,
    audit: null
  }
  switch (type) {
    case 'Idle':
      funcObj.back = idleBack
      funcObj.submit = idleSubmit
      funcObj.unAudit = idleUnAudit
      funcObj.audit = idleAudit
      break
    case 'Occupy':
      funcObj.back = occupyBack
      funcObj.submit = occupySubmit
      funcObj.unAudit = occupyUnAudit
      funcObj.audit = occupyAudit
      break
    case 'Borrow':
      funcObj.back = borrowBack
      funcObj.submit = borrowSubmit
      funcObj.unAudit = borrowUnAudit
      funcObj.audit = borrowAudit
      break
    case 'Self':
      funcObj.back = selfBack
      funcObj.submit = selfSubmit
      funcObj.unAudit = selfUnAudit
      funcObj.audit = selfAudit
      break
  }
  return funcObj
}
const requestFuncObj = computed(() => {
  const funcObj = {
    exportExcel: null,
    importExcel: null,
    batchDel: null
  }
  switch (activeTab.value) {
    case 0:
      funcObj.exportExcel = allExportExcel
      funcObj.importExcel = allImportExcel
      funcObj.batchDel = allDeleteBatch
      break
    case 1:
      funcObj.exportExcel = idleExportExcel
      funcObj.importExcel = idleImportExcel
      funcObj.batchDel = idleBatchDel
      break
    case 2:
      funcObj.exportExcel = occupyExportExcel
      funcObj.importExcel = occupyImportExcel
      funcObj.batchDel = occupyBatchDel
      break
    case 3:
      funcObj.exportExcel = borrowExportExcel
      funcObj.importExcel = borrowImportExcel
      funcObj.batchDel = borrowBatchDel
      break
    case 4:
      funcObj.exportExcel = selfExportExcel
      funcObj.importExcel = selfImportExcel
      funcObj.batchDel = selfBatchDel
      break
  }
  return funcObj
})

const pageRequestFunc = computed(() => {
  let func = null
  switch (activeTab.value) {
    case 0:
      func = getAllList
      break
    case 1:
      func = getIdleAssetTrackingList
      break
    case 2:
      func = getOccupyAssetTrackingList
      break
    case 3:
      func = getBorrowAssetTrackingList
      break
    case 4:
      func = getSelfAssetTrackingList
      break
  }
  return func
})

const pageTableData = ref({
  tableLoading: false,
  pagination: {},
  list: [],
  onTableFetch() {},
  tableHeight: ''
})
const pageTableSelectionData = ref({
  selectedRowKeys: [],
  onSelectChange() {},
  clearSelection() {}
})
pageTableData.value = usePageTable(pageRequestFunc.value)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  pageTableData.value.onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
const activeTabChange = () => {
  pageTableData.value = usePageTable(pageRequestFunc.value)
  pageTableSelectionData.value = useTableSelection(
    computed(() => pageTableData.value.list),
    'id'
  )
  onTableChange()
}

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: 'left' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 150 },
  { title: '业务日期', dataIndex: 'bizDate' },
  { title: '资产', dataIndex: 'houseOwner_dictText' },
  { title: '跟踪类型', dataIndex: 'trackingType_dictText' },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  { title: '创建时间', dataIndex: 'createTime', ellipsis: true },
  { title: '提交人', dataIndex: 'createBy_dictText' },
  { title: '审核时间', dataIndex: 'auditTime' },
  { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns)
pageTableSelectionData.value = useTableSelection(
  computed(() => pageTableData.value.list),
  'id'
)

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pageTableData.value.pagination.pageSize })
  }, 600)
}
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = async (row) => {
  let requestFunc = null
  if (row.trackingType === 'Idle') {
    requestFunc = getIdleQueryById
  }
  if (row.trackingType === 'Occupy') {
    requestFunc = getOccupyQueryById
  }
  if (row.trackingType === 'Borrow') {
    requestFunc = getBorrowQueryById
  }
  if (row.trackingType === 'Self') {
    requestFunc = getSelfQueryById
  }
  if (requestFunc) {
    const { result } = await requestFunc(row.id)
    addEditRef?.value.open({ ...result })
  }
}
// 撤回操作
const rowBack = (row) => {
  Modal.confirm({
    title: '确定撤回？',
    content: '',
    centered: true,
    onOk: async () => {
      const { back } = getTypeRequestObj(row.trackingType)
      const data = await back({ id: row.id })
      message.success(data.message)
      const pageNo = pageTableData.value.current
      onTableChange({ pageNo })
    }
  })
}
// 查看审批
const rowViewApprove = () => {}

// 审核操作（临时）
const rowVerify = (row) => {
  Modal.confirm({
    title: '确认审核通过？',
    content: '',
    async onOk() {
      const { audit } = getTypeRequestObj(row.trackingType)
      const data = await audit({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 反审核
const rowReverse = (row) => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const { unAudit } = getTypeRequestObj(row.trackingType)
      const data = await unAudit({ id: row.id })
      message.success(data.message)
      onTableChange({
        pageNo: pageTableData.value.pagination.current,
        pageSize: pageTableData.value.pagination.pageSize
      })
    }
  })
}

// 提交（重新提交）
const rowResubmit = (row) => {
  Modal.confirm({
    title: '确认提交？',
    content: '',
    centered: true,
    onOk: async () => {
      const { submit } = getTypeRequestObj(row.trackingType)
      const data = await submit({ id: row.id })
      message.success(data.message)
      const pageNo = pageTableData.value.current
      onTableChange({ pageNo })
    }
  })
}
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除当前资产跟踪？',
    content: '',
    centered: true,
    onOk: async () => {
      const { message: msg } = await requestFuncObj.value.batchDel(
        data ? data.id : pageTableSelectionData.value.selectedRowKeys.join(',')
      )
      message.success(msg)
      let pageNo = pageTableData.value.pagination.current
      if (
        pageNo > 1 &&
        ((data && pageTableData.value.list.length === 1) ||
          (!data && pageTableSelectionData.value.selectedRowKeys.length === pageTableData.value.list.length))
      ) {
        pageNo--
      }
      pageTableSelectionData.value.clearSelection()
      onTableChange({ pageNo, pageSize: pageTableData.value.pagination.pageSize })
    }
  })
}

// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await requestFuncObj.value.exportExcel('资产跟踪数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: pageTableSelectionData.value.selectedRowKeys.join(',')
    })
    message.success('导出成功')
    pageTableSelectionData.value.clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
