<template>
  <a-modal
    v-model:open="visible"
    title="变更合同"
    width="600px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    centered
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      autocomplete="off"
      label-align="left"
      :label-col="{ style: { width: 74 } }"
    >
      <a-form-item label="变更原因" name="changeReason">
        <a-textarea
          v-model:value="form.changeReason"
          show-count
          :maxlength="200"
          :auto-size="{ minRows: 4, maxRows: 4 }"
        ></a-textarea>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['confirmChange'])

const visible = ref(false)

const open = (data) => {
  form.contractVersion = data.contractVersion
  form.initContract = data.initContract
  form.originalContract = data.id
  visible.value = true
}

const form = reactive({
  contractVersion: '', // 合同版本号
  initContract: '',
  originalContract: '', // 原合同id
  changeReason: ''
})
const rules = {
  changeReason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }]
}

const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  emit('confirmChange', { ...form })
  handleCancel()
}

const handleCancel = () => {
  form.originalContract = ''
  form.changeReason = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
