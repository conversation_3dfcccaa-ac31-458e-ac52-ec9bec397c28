import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/carportNum/list',
    params
  })
}

export const f7List = (params) => {
  return request({
    method: 'get',
    url: '/bas/carportNum/f7list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/carportNum/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/carportNum/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/carportNum/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/carportNum/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/carportNum/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/carportNum/deleteBatch',
    params
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/carportNum/updateEnableDisableStatus',
    data
  })
}
