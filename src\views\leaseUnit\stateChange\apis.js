import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 获取列表数据
export const getLeaseUnitStateChangeReqBillList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/list',
    params
  })
}

// 获取详情
export const getLeaseUnitStateChangeReqBillDetail = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/queryById',
    params
  })
}

// 根据变更单ID获取租赁单元
export const getLeaseUnitStateChangeReqBillById = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/queryById',
    params
  })
}

// 根据主表ID获取租赁单元信息
export const getLeaseUnitStateChangeReqBillEntryByMainId = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/queryLeaseUnitStateChangeReqBillEntryByMainId',
    params
  })
}

// 暂存
export const addLeaseUnitStateChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/add',
    data
  })
}

// 编辑
export const editLeaseUnitStateChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/edit',
    data
  })
}

// 提交
export const submitLeaseUnitStateChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/submit',
    data
  })
}

// 反审核
export const unAuditLeaseUnitStateChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/unAudit',
    data
  })
}

// 撤回
export const backLeaseUnitStateChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/back',
    data
  })
}

// 审核
export const auditLeaseUnitStateChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/audit',
    data
  })
}

// 通过 id 删除
export const deleteLeaseUnitStateChangeReqBill = (params) => {
  return request({
    method: 'delete',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/delete',
    params
  })
}

// 导出 Excel
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitStateChangeReqBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入 Excel
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/leaseUnitStateChangeReqBill/importExcel', data, controller)
}
