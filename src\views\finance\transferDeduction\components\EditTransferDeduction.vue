<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}转款抵扣申请`"
    class="edit-transfer-deduction-drawer common-drawer"
    placement="right"
    width="1072px"
    :confirm-loading="confirmLoading"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-align="left"
        :label-col="{ style: { width: '100px' } }"
      >
        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="客户" name="customer">
          <a-form-item-rest>
            <f7-select v-model="formData.customer" f7-type="customer" placeholder="请选择客户" />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="物业管理公司" name="manageCompany">
          <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司" disabled></company-select>
        </a-form-item>

        <a-form-item label="业务部门" name="operatorDepart">
          <depart-select v-model="formData.operatorDepart" @change="onDepartChange" />
        </a-form-item>

        <a-form-item label="经办人" name="operator">
          <a-form-item-rest>
            <f7-select
              v-model="formData.operator"
              f7-type="user"
              :depart-id="formData.operatorDepart"
              placeholder="请选择经办人"
            />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :maxlength="255"
            :rows="4"
            show-count
          />
        </a-form-item>
      </a-form>

      <h4 class="text-[16px] font-bold mt-[24px] mb-[12px] text-[#1d335c] flex justify-between">
        <span>
          转款明细
          <span class="text-[12px] font-normal text-[#8992a3]">
            即：可用于抵扣的款项，转出用于抵扣其他未收取的应收款
          </span>
        </span>
        <a-button type="primary" size="medium" @click="handleAddTransferItem">添加明细</a-button>
      </h4>

      <a-table
        v-if="formData.transferDeductionReqTransferDetailList.length"
        :columns="transferColumns"
        :data-source="formData.transferDeductionReqTransferDetailList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'detailBill'">
            <i class="a-icon-remove cursor-pointer mr-2 text-red-500" @click="handleRemoveTransferItem(index)"></i>
            {{ record.detailBill_dictText }}
          </template>
          <template v-else-if="column.dataIndex === 'thisTransferOutAmount'">
            <a-input-number
              v-model:value="record.thisTransferOutAmount"
              :min="0"
              :precision="2"
              placeholder="请输入转款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
        </template>
      </a-table>
      <div v-else class="flex flex-col items-center">
        <img src="@/assets/imgs/no-data.png" />
        <span class="text-tertiary">暂无数据</span>
      </div>

      <h4 class="text-[16px] font-bold mt-[24px] mb-[12px] text-[#1d335c] flex justify-between">
        <span>
          抵扣欠款明细
          <span class="text-[12px] font-normal text-[#8992a3]">即：上述转出明细的总转出金额要抵扣的未收取营收明细</span>
        </span>
        <a-button type="primary" size="medium" @click="handleAddDeductionItem">添加明细</a-button>
      </h4>
      <a-table
        v-if="formData.transferDeductionReqDeductionDetailList.length"
        :columns="debtColumns"
        :data-source="formData.transferDeductionReqDeductionDetailList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'detailBill'">
            <i class="a-icon-remove cursor-pointer mr-2 text-red-500" @click="handleRemoveDebtItem(index)"></i>
            {{ record.detailBill_dictText }}
          </template>
          <template v-else-if="column.dataIndex === 'thisTransferIntoAmount'">
            <a-input-number
              v-model:value="record.thisTransferIntoAmount"
              :min="0"
              :precision="2"
              placeholder="请输入转款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
        </template>
      </a-table>
      <div v-else class="flex flex-col items-center">
        <img src="@/assets/imgs/no-data.png" />
        <span class="text-tertiary">暂无数据</span>
      </div>
      <div class="p-[16px] bg-[#f7f8fa] rounded-[8px] mt-[40px] border border-[#e6e9f0]">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-[24px] text-[#1d335c]">
              转出合计：
              <span class="text-[#f03a1d] font-bold">{{ renderMoney(transferOutTotal, 2, '元') }}</span>
              转入合计：
              <span class="text-[#f03a1d] font-bold">{{ renderMoney(transferInTotal, 2, '元') }}</span>
            </p>
            <p class="mt-[8px] text-[14px] text-[#495a7a]">{{ statusInfo }}</p>
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- 转款明细选择器 -->
  <transfer-detail-modal ref="transferDetailSelectorRef" @update-list="handleTransferDetailConfirm" />

  <!-- 抵扣欠款明细选择器 -->
  <deduction-detail-modal ref="deductionDetailSelectorRef" @update-list="handleDeductionDetailConfirm" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/modules/user'
import { renderMoney } from '@/utils/render'
import {
  editTransferDeduction,
  addTransferDeduction,
  submitTransferDeduction,
  getTransferDeductionDetail,
  getDeductionDetail
} from '../apis'
import TransferDetailModal from './TransferDetailModal.vue'
import DeductionDetailModal from './DeductionDetailModal.vue'

const emits = defineEmits(['refresh'])

const store = useUserStore()

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const transferDetailSelectorRef = ref()
const deductionDetailSelectorRef = ref()

/**
 * 计算转出合计
 */
const transferOutTotal = computed(() => {
  return formData.transferDeductionReqTransferDetailList.reduce((total, item) => {
    return total + (item.thisTransferOutAmount || 0)
  }, 0)
})

/**
 * 计算转入合计
 */
const transferInTotal = computed(() => {
  return formData.transferDeductionReqDeductionDetailList.reduce((total, item) => {
    return total + (item.thisTransferIntoAmount || 0)
  }, 0)
})

/**
 * 计算状态文本
 */
const statusInfo = computed(() => {
  const outTotal = transferOutTotal.value
  const inTotal = transferInTotal.value
  const difference = outTotal - inTotal
  if (difference > 0) {
    return `已多收取：${renderMoney(difference, 2, '元')}`
  }
  if (difference < 0) {
    return `合计欠款：${renderMoney(Math.abs(difference), 2, '元')}`
  }
  return '已结清'
})

const formDataDefault = {
  bizDate: undefined,
  operatorDepart: undefined,
  manageCompany: undefined,
  operator: undefined,
  remark: undefined,
  customer: undefined,
  transferDeductionReqTransferDetailList: [],
  transferDeductionReqDeductionDetailList: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期' }],
  customer: [{ required: true, message: '请选择客户' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }],
  operator: [{ required: true, message: '请选择经办人' }]
}

/**
 * 转款明细表格列配置
 */
const transferColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 240, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 200, ellipsis: true },
  { title: '合同', dataIndex: 'contract_dictText', width: 200, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '减免金额', dataIndex: 'remission', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '实际应收',
    dataIndex: 'actualReceiveAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已收金额', dataIndex: 'paid', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '未收金额', dataIndex: 'residual', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已转款抵扣',
    dataIndex: 'transferDeduction',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已退金额', dataIndex: 'refunded', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已处理尾差',
    dataIndex: 'offDifference',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '剩余可转',
    dataIndex: 'residueTransferAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '本次转款金额',
    dataIndex: 'thisTransferOutAmount',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisTransferOutAmount || 0
    }
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

/**
 * 抵扣欠款明细表格列配置
 */
const debtColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 240, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 200, ellipsis: true },
  { title: '合同', dataIndex: 'contract_dictText', width: 200, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '减免金额', dataIndex: 'remission', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '实际应收',
    dataIndex: 'actualReceiveAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已收金额', dataIndex: 'paid', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '未收金额', dataIndex: 'residual', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已转款抵扣',
    dataIndex: 'transferDeduction',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已退金额', dataIndex: 'refunded', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已处理尾差',
    dataIndex: 'offDifference',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '剩余欠款',
    dataIndex: 'residual',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '本次转入金额',
    dataIndex: 'thisTransferIntoAmount',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisTransferIntoAmount || 0
    }
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

/**
 * 打开编辑弹窗
 * @param {Object} data - 编辑的数据对象，新建时为空
 */
const open = async (data) => {
  visible.value = true
  // 重置数据
  Object.assign(formData, {
    ...formDataDefault,
    transferDeductionReqTransferDetailList: [],
    transferDeductionReqDeductionDetailList: []
  })
  formRef.value?.resetFields()

  if (data && data.id) {
    // 编辑模式，加载数据
    Object.assign(formData, data)

    try {
      confirmLoading.value = true

      // 查询转款明细
      const transferDetailRes = await getTransferDeductionDetail({ id: data.id })
      if (transferDetailRes.success && transferDetailRes.result) {
        formData.transferDeductionReqTransferDetailList = transferDetailRes.result
      }

      // 查询抵扣欠款明细
      const deductionDetailRes = await getDeductionDetail({ id: data.id })
      if (deductionDetailRes.success && deductionDetailRes.result) {
        formData.transferDeductionReqDeductionDetailList = deductionDetailRes.result
      }
    } catch {
      message.error('加载明细数据失败')
    } finally {
      confirmLoading.value = false
    }
  } else {
    formData.bizDate = dayjs().format('YYYY-MM-DD')
    formData.operator = store.userInfo.id
    formData.operatorDepart = store.userInfo.currentDepart
    formData.manageCompany = store.userInfo.currentCompany
  }
}

/**
 * 关闭抽屉并重置表单
 */
const handleCancel = () => {
  Object.assign(formData, {
    ...formDataDefault,
    transferDeductionReqTransferDetailList: [],
    transferDeductionReqDeductionDetailList: []
  })
  formRef.value?.resetFields()
  emits('refresh')
  visible.value = false
}

const onDepartChange = () => {
  formData.operator = ''
}

/**
 * 打开转款明细选择器
 */
const handleAddTransferItem = () => {
  if (!formData.customer) {
    message.warning('请先选择客户')
    return
  }
  // 获取已选择的明细
  const selectedTransferDetails = formData.transferDeductionReqTransferDetailList.map((item) => ({
    ...item,
    parent: item.detailBill,
    id: item.detailBillEntry
  }))

  // 传递查询参数
  const params = {
    customer: formData.customer
  }

  transferDetailSelectorRef.value?.open(selectedTransferDetails, params)
}

/**
 * 转款明细选择确认回调
 * @param {Array} selectedRows - 选中的明细数据
 */
const handleTransferDetailConfirm = (selectedRows) => {
  // 处理数据格式，添加必要的字段映射
  selectedRows.map((item) => {
    const data = formData.transferDeductionReqTransferDetailList.find((i) => i.detailBillEntry === item.id)
    if (data) return
    formData.transferDeductionReqTransferDetailList.push({
      ...item,
      id: undefined,
      detailBill_dictText: item.number,
      detailBill: item.parent,
      detailBillEntry: item.id,
      residueTransferAmount: item.balance,
      thisTransferOutAmount: 0,
      remark: ''
    })
  })

  if (selectedRows && selectedRows.length > 0) {
    message.success(`已选择 ${selectedRows.length} 条转款明细`)
  } else {
    message.info('已清空转款明细')
  }
}

/**
 * 移除转款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveTransferItem = (index) => {
  formData.transferDeductionReqTransferDetailList.splice(index, 1)
}

/**
 * 打开抵扣欠款明细选择器
 */
const handleAddDeductionItem = () => {
  if (!formData.customer) {
    message.warning('请先选择客户')
    return
  }

  // 获取已选择的明细
  const selectedDeductionDetails = formData.transferDeductionReqDeductionDetailList.map((item) => ({
    ...item,
    parent: item.detailBill,
    id: item.detailBillEntry
  }))

  // 传递查询参数
  const params = {
    customer: formData.customer
  }

  deductionDetailSelectorRef.value?.open(selectedDeductionDetails, params)
}

/**
 * 抵扣欠款明细选择确认回调
 * @param {Array} selectedRows - 选中的明细数据
 */
const handleDeductionDetailConfirm = (selectedRows) => {
  // 处理数据格式，添加必要的字段映射
  selectedRows.map((item) => {
    const data = formData.transferDeductionReqDeductionDetailList.find((i) => i.detailBillEntry === item.id)
    if (data) return
    formData.transferDeductionReqDeductionDetailList.push({
      ...item,
      id: undefined,
      detailBill_dictText: item.number,
      detailBill: item.parent,
      detailBillEntry: item.id,
      residueDebtAmount: item.residual,
      thisTransferIntoAmount: 0,
      remark: ''
    })
  })

  if (selectedRows && selectedRows.length > 0) {
    message.success(`已选择 ${selectedRows.length} 条抵扣欠款明细`)
  } else {
    message.info('已清空抵扣欠款明细')
  }
}

/**
 * 移除抵扣欠款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveDebtItem = (index) => {
  formData.transferDeductionReqDeductionDetailList.splice(index, 1)
}

/**
 * 验证明细数据
 */
const validateDetails = () => {
  // 验证转款明细
  if (formData.transferDeductionReqTransferDetailList.length === 0) {
    message.error('请至少添加一条转款明细')
    return false
  }

  // 验证抵扣欠款明细
  if (formData.transferDeductionReqDeductionDetailList.length === 0) {
    message.error('请至少添加一条抵扣欠款明细')
    return false
  }

  return true
}

/**
 * 保存表单数据
 * @param {boolean} isTemporary - 是否为暂存，true为暂存，false为提交
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    await formRef.value?.validate()

    // 验证明细数据
    if (!validateDetails()) {
      return
    }

    // 根据操作类型选择对应的API
    const api = !isTemporary ? submitTransferDeduction : formData.id ? editTransferDeduction : addTransferDeduction

    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '新建'
    message.success(`转款抵扣申请${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 提交表单
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

onMounted(() => {
  // 从合同-退租清算，点击“申请转款抵扣”跳转过来
  const infoStr = sessionStorage.getItem('transferDeductionFromClearing')
  if (!infoStr) return
  const info = JSON.parse(infoStr)
  open()
  formData.customer = info.customer
  // 转款明细
  formData.transferDeductionReqTransferDetailList = info.transferList.map((item) => ({
    ...item,
    detailBill_dictText: item.number,
    detailBill: item.parent,
    detailBillEntry: item.id,
    residueTransferAmount: item.balance,
    thisTransferOutAmount: 0,
    remark: ''
  }))
  // 抵扣欠款明细
  formData.transferDeductionReqDeductionDetailList = info.debtList.map((item) => ({
    ...item,
    detailBill_dictText: item.number,
    detailBill: item.parent,
    detailBillEntry: item.id,
    residueDebtAmount: item.residual,
    thisTransferIntoAmount: 0,
    remark: ''
  }))
  sessionStorage.removeItem('transferDeductionFromClearing')
})

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.edit-transfer-deduction-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>
