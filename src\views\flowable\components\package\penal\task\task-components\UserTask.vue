<template>
  <div style="margin-top: 16px">
    <a-form-item label="人员类型" name="userType">
      <a-select v-model:value="userTaskForm.userType" @change="updateElementTask('userType')">
        <a-select-option value="assignee">指定人员</a-select-option>
        <a-select-option value="candidateUsers">候选人员</a-select-option>
        <a-select-option value="candidateGroups">候选组</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="指定方式" name="dataType">
      <a-radio-group v-model:value="userTaskForm.dataType" name="radioGroup" @change="updateElementTask('dataType')">
        <a-radio value="fixed">固定</a-radio>
        <a-radio value="dynamic">动态</a-radio>
      </a-radio-group>
    </a-form-item>

    <a-form-item v-if="userTaskForm.dataType === 'fixed' && userTaskForm.userType === 'assignee'" label="指定人员">
      <f7-select
        v-model="userTaskForm.assignee"
        f7-type="user"
        @change="() => updateElementTask('assignee')"
      ></f7-select>
    </a-form-item>
    <a-form-item v-if="userTaskForm.dataType === 'dynamic' && userTaskForm.userType === 'assignee'" label="指定人员">
      <a-select v-model:value="userTaskForm.assignee" @change="updateElementTask('assignee')">
        <a-select-option value="submitter">流程发起人</a-select-option>
        <a-select-option value="manager">部门负责人</a-select-option>
        <a-select-option value="divisionManager">分管领导</a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item
      v-if="userTaskForm.dataType === 'fixed' && userTaskForm.userType === 'candidateUsers'"
      label="候选人员"
    >
      <f7-select
        v-model="userTaskForm.candidateUsers"
        multiple
        f7-type="user"
        @change="() => updateElementTask('candidateUsers')"
      ></f7-select>
    </a-form-item>
    <a-form-item
      v-if="userTaskForm.dataType === 'dynamic' && userTaskForm.userType === 'candidateUsers'"
      label="候选人员"
    >
      <a-select
        v-model:value="userTaskForm.candidateUsers"
        mode="multiple"
        @change="updateElementTask('candidateUsers')"
      >
        <a-select-option value="submitter">流程发起人</a-select-option>
        <a-select-option value="manager">部门负责人</a-select-option>
        <a-select-option value="divisionManager">分管领导</a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item
      v-if="userTaskForm.dataType === 'fixed' && userTaskForm.userType === 'candidateGroups'"
      label="候选组"
      name="candidateGroups"
    >
      <a-select
        v-model:value="userTaskForm.candidateGroups"
        mode="multiple"
        placeholder="请选择角色"
        @change="updateElementTask('candidateGroups')"
      >
        <a-select-option v-for="item in roleList" :key="item.id" :value="item.id">
          {{ item.roleName }}
        </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="userTaskForm.dataType === 'dynamic' && userTaskForm.userType === 'candidateGroups'"
      label="候选组"
      @change="updateElementTask('candidateGroups')"
    >
      <a-select
        v-model:value="userTaskForm.candidateGroups"
        mode="multiple"
        @change="updateElementTask('candidateGroups')"
      ></a-select>
    </a-form-item>

    <a-form-item label="到期时间">
      <a-input v-model:value="userTaskForm.dueDate" clearable @change="updateElementTask('dueDate')" />
    </a-form-item>
    <a-form-item label="跟踪时间">
      <a-input v-model:value="userTaskForm.followUpDate" clearable @change="updateElementTask('followUpDate')" />
    </a-form-item>
    <a-form-item label="优先级">
      <a-input v-model:value="userTaskForm.priority" clearable @change="updateElementTask('priority')" />
    </a-form-item>
  </div>
</template>

<script>
import { queryAllNoByTenant } from '@/views/system/role/apis'

export default {
  name: 'UserTask',
  props: {
    id: String,
    type: String
  },
  data() {
    return {
      roleList: [],
      defaultTaskForm: {
        userType: 'assignee',
        dataType: 'fixed',
        assignee: '',
        candidateUsers: [],
        candidateGroups: [],
        dueDate: '',
        followUpDate: '',
        priority: ''
      },
      userTaskForm: {
        userType: 'assignee',
        dataType: 'fixed',
        assignee: '',
        candidateUsers: [],
        candidateGroups: [],
        dueDate: '',
        followUpDate: '',
        priority: ''
      }
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        // eslint-disable-next-line vue/no-undef-properties
        this.bpmnElement = window.bpmnInstances.bpmnElement
        this.$nextTick(() => this.resetTaskForm())
      }
    }
  },
  mounted() {
    this.loadRoleList()
  },
  methods: {
    async loadRoleList() {
      const { result } = await queryAllNoByTenant()
      this.roleList = result
    },
    resetTaskForm() {
      for (const key in this.defaultTaskForm) {
        let value
        if (key === 'candidateUsers' || key === 'candidateGroups') {
          value = this.bpmnElement?.businessObject[key] ? this.bpmnElement.businessObject[key].split(',') : []
        } else {
          value = this.bpmnElement?.businessObject[key] || this.defaultTaskForm[key]
        }
        this.userTaskForm[key] = value
      }
    },
    updateElementTask(key) {
      console.log(this.userTaskForm, '-----')
      if (key === 'dataType') {
        this.userTaskForm.assignee = ''
        this.userTaskForm.candidateUsers = []
        this.userTaskForm.candidateGroups = []
      }
      const taskAttr = Object.create(null)
      if (key === 'candidateUsers' || key === 'candidateGroups') {
        taskAttr[key] = this.userTaskForm[key] && this.userTaskForm[key].length ? this.userTaskForm[key].join() : null
      } else {
        taskAttr[key] = this.userTaskForm[key] || null
      }
      console.log(taskAttr[key])
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, taskAttr)
    }
  },
  beforeUnmount() {
    this.bpmnElement = null
  }
}
</script>

<style lang="less" scoped>
:deep(.f7-select-input) {
  height: auto;
}
</style>
