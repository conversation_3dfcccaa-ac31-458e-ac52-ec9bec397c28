<template>
  <div class="page-wrapper">
    <div class="w-[1072px] center-wrapper">
      <div class="text-[#1D335C] mb-[12px]">
        <span class="a-icon-zhanghushezhi text-[18px] mr-[8px]"></span>
        <span class="text-[18px] font-bold">账号设置</span>
      </div>
      <div class="mb-[40px] border border-[#E6E9F0] rounded-[8px]">
        <div class="flex items-center p-[25px] rounded-t-[8px] info-header">
          <img
            class="rounded-[50%] cursor-pointer"
            :src="userInfo.avatar"
            :width="78"
            :height="78"
            @click="uploadAvatar"
          />
          <!-- <img-upload v-model="userInfo.avatar" round size="78px"></img-upload> -->

          <div class="ml-[25px]">
            <div class="text-secondary text-[24px] font-bold mb-[10px]">{{ userInfo.realname }}</div>
            <div>
              <span class="text-secondary text-[16px] mr-[4px]">{{ renderDict(userInfo.sex, 'sex') }}</span>
              <span v-if="userInfo.sex === 1" class="a-icon-male-solid text-primary"></span>
              <span v-else class="a-icon-female-solid text-[#FAB700]"></span>
            </div>
          </div>
        </div>
        <div class="m-[24px] text-secondary">
          <span class="w-[80px] mr-[40px] inline-block">登录账号</span>
          <span>{{ userInfo.username }}</span>
        </div>
        <div class="flex justify-between items-center m-[24px]">
          <div class="text-secondary">
            <span class="w-[80px] mr-[40px] inline-block">登录密码</span>
            <span>**************</span>
          </div>
          <div>
            <span class="primary-btn" @click="changePw">修改密码</span>
          </div>
        </div>
        <div class="flex justify-between items-center m-[24px]">
          <div class="text-secondary">
            <span class="w-[80px] mr-[40px] inline-block">绑定手机号</span>
            <span v-if="userInfo.phone">{{ userInfo.phone }}</span>
            <span v-else class="a-icon-tips text-[14px] text-[#FAB700]">未完善</span>
          </div>
          <div>
            <span class="text-tertiary">（用于接收通知及找回密码等）</span>
            <template v-if="userInfo.phone">
              <span class="primary-btn" @click="unbind(1)">解绑</span>
              <span class="primary-btn" @click="changePhone(1)">修改手机号</span>
            </template>
            <span v-else class="primary-btn" @click="changePhone">立即绑定</span>
          </div>
        </div>
        <div class="flex justify-between items-center m-[24px]">
          <div class="text-secondary">
            <span class="w-[80px] mr-[40px] inline-block">绑定邮箱</span>
            <span>{{ userInfo.email }}</span>
          </div>
          <div>
            <span class="text-tertiary">（用于接收通知及找回密码等）</span>
            <template v-if="userInfo.email">
              <span class="primary-btn" @click="unbind">解绑</span>
              <span class="primary-btn" @click="changeEmail(1)">修改邮箱</span>
            </template>
            <span v-else class="primary-btn" @click="changeEmail">立即绑定</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 修改密码 -->
    <change-password ref="changePasswordRef"></change-password>
    <!-- 修改手机号 -->
    <bind-phone ref="changePhoneRef"></bind-phone>
    <!-- 修改邮箱 -->
    <bind-email ref="changeEmailRef"></bind-email>
    <!-- 上传头像 -->
    <img-cropper ref="imgCropperRef" @success="uploadSuccess"></img-cropper>
  </div>
</template>

<script setup>
import ImgCropper from '@/components/ImgCropper.vue'
import { message, Modal } from 'ant-design-vue'
import { unbindPhone, unbindEmail } from '@/apis/common'
import ChangePassword from './components/ChangePassword.vue'
import BindPhone from './components/ChangePhone.vue'
import BindEmail from './components/ChangeEmail.vue'
import { useUserStore } from '@/store/modules/user'
import { renderDict } from '@/utils/render'
const store = useUserStore()
const userInfo = ref({})
userInfo.value = JSON.parse(JSON.stringify(store.userInfo))

// 修改密码
const changePasswordRef = ref()
const changePw = () => {
  changePasswordRef?.value.open()
}
// 修改手机号
const changePhoneRef = ref()
const changePhone = (type) => {
  const params = type ? { phone: userInfo.value.phone } : {}
  changePhoneRef?.value.open(params)
}
// 解绑
const unbind = (type) => {
  const requestFunc = type ? unbindPhone : unbindEmail
  Modal.confirm({
    title: '提示',
    content: type ? '确定解绑手机号？' : '确定解绑邮箱？',
    centered: true,
    onOk: async () => {
      const data = await requestFunc()
      message.success(data.message)
    }
  })
}
// 修改邮箱
const changeEmailRef = ref()
const changeEmail = (type) => {
  const params = type ? { email: userInfo.value.email } : {}
  changeEmailRef?.value.open(params)
}
// 点击上传头像
const imgCropperRef = ref()
const uploadAvatar = () => {
  imgCropperRef?.value.open()
}
// 上传头像的回调
const uploadSuccess = () => {}
</script>

<style lang="less" scoped>
.page-wrapper {
  padding: 40px;
  width: 100vw;
  height: calc(100vh - 64px);
  background: url('@/assets/imgs/pCenter/p-center-bg.png') center/100% 100% no-repeat;
  .center-wrapper {
    margin: 0 auto;
    .info-header {
      background: url('@/assets/imgs/pCenter/p-center-name-bg.png') center/100% 100% no-repeat;
    }
  }
  .cropper-row-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr; /* 并排显示裁剪区和预览 */
    gap: 20px;
    margin: 20px 0;
    .cut {
      width: 300px;
      height: 300px;
    }
  }
}
</style>
