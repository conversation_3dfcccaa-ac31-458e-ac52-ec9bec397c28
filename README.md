# 技术栈说明

### 1.构建工具vite6

[官网文档](https://cn.vitejs.dev/guide/)

### 2.vue3.5

[官网文档](https://cn.vuejs.org/guide/introduction)

### 3.UI框架，ant-design-vue4.x

[官网文档](https://www.antdv.com/components/overview-cn/)

### 4.路由系统，vue-router4.x

[官网文档](https://router.vuejs.org/zh/guide/)

### 5.状态管理，pinia@2.x + 持久化存储pinia-plugin-persistedstate@3.x

[pinia官方文档](https://pinia.vuejs.org/zh/introduction.html)
[pinia-plugin-persistedstate官方文档](https://prazdevs.github.io/pinia-plugin-persistedstate/zh/guide/why.html)

### 6.CSS相关 预处理器less，以及tailwindcss4

[tailwindcss官方文档](https://tailwind.nodejs.cn/docs/installation)

# 项目运行说明

### 1.node版本

必须20以上的版本，建议20.15.0

### 2.依赖安装

先全局安装pnpm，npm install -g pnpm
统一使用pnpm安装依赖，pnpm i

### 3.运行命令

npm run dev

### 4.插件安装

.vscode/extensions.json 配置了vscode插件，一般在vscode打开项目时，右下角会弹出是否安装插件，请点击安装，以便后续开发
