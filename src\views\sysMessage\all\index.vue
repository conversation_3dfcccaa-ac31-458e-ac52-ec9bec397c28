<template>
  <div>
    <div class="flex justify-between items-center">
      <a-tabs v-model:active-key="activeTabKey" class="flex-1">
        <a-tab-pane key="all" tab="全部类型"></a-tab-pane>
        <a-tab-pane key="Intention" tab="逾期通知"></a-tab-pane>
        <a-tab-pane key="Official" tab="平台通知"></a-tab-pane>
      </a-tabs>
    </div>
    <div class="flex justify-between my-[24px]">
      <div>
        <a-button type="primary" @click="handleRead">
          <i class="a-icon-plus mr-1"></i>
          已读
        </a-button>
        <a-form layout="inline" ref="formRef" class="mb-[10px]" autocomplete="off"></a-form>
      </div>
    </div>
    <a-table :data-source="list" :columns="columns" :loading="tableLoading" :pagination="pagination">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
const activeTabKey = ref('all')

const handleRead = () => {}

const handleDetail = () => {}

const columns = [
  { title: '标题', dataIndex: 'title', fixed: 'left' },
  { title: '内容', dataIndex: 'content' },
  { title: '状态', dataIndex: 'status' },
  { title: '发送时间', dataIndex: 'time' },
  { title: '发布人', dataIndex: 'person' },
  { title: '重要性', dataIndex: 'important' },
  { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
]

const { list, pagination, tableLoading } = {}
</script>
