import { login, queryPermissions, logout, getCurrentDeparts, getCurrentUserCompanies } from '@/apis/user'
import router, { createRouters } from '@/router'
import { useDictStore } from './dict'

const getKeepAliveList = (list) => {
  const keepAliveList = []
  function getKeepAlive(list) {
    list.forEach((item) => {
      if (item.meta.keepAlive) {
        keepAliveList.push(item.path)
      }
      if (item.children && item.children.length) {
        getKeepAlive(item.children)
      }
    })
  }
  getKeepAlive(list)
  return keepAliveList
}

export const useUserStore = defineStore('user', {
  persist: {
    key: `${import.meta.env.VITE_STORE_KEY}:user`
  },
  state: () => ({
    departs: [],
    token: '',
    userInfo: {},
    permission: {
      menu: []
    },
    currentMenu: {
      title: '',
      list: []
    },
    keepAliveList: [],
    companyList: [],
    departList: []
  }),
  actions: {
    async loginByUsername(params) {
      const { result } = await login(params)
      this.$patch((state) => {
        state.departs = result.departs || []
        state.token = result.token
        state.userInfo = result.userInfo
      })
      const dictStore = useDictStore()
      await Promise.all([
        this.getPermissions(),
        dictStore.getAllDict(),
        this.getCompanyListByUser(),
        this.getDepartListByUser()
      ])
    },
    async getPermissions() {
      const { result } = await queryPermissions()
      this.permission = result
      createRouters(result.menu)
      this.keepAliveList = getKeepAliveList(result.menu)
    },
    // 根据当前登录用户，获取用户所属公司列表
    async getCompanyListByUser() {
      const { result } = await getCurrentUserCompanies()
      this.companyList = result.companyList
    },
    // 根据当前登录用户，获取用户所属公司之下的部门列表
    async getDepartListByUser() {
      const { result } = await getCurrentDeparts()
      this.departList = result.departList
    },
    setCurrentMenus(data) {
      this.currentMenu.title = data.title
      this.currentMenu.list = data.list
    },
    async logout() {
      await logout()
      this.$patch((state) => {
        state.departs = []
        state.token = ''
        state.userInfo = {}
        state.permission = { menu: [] }
      })
      router.replace('/login')
    }
  }
})

export default function userStore() {
  const store = useUserStore()
  return storeToRefs(store)
}
