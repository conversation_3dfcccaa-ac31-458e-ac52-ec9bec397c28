<template>
  <div>
    <div class="flex mb-[16px]">
      <a-button type="primary" @click="handleAdd" v-auth="'bas:ct_bas_lease_unit_change_record:add'">
        <i class="a-icon-plus mr-1"></i>
        新建
      </a-button>
      <a-button @click="handleImport" v-auth="'bas:ct_bas_lease_unit_change_record:importExcel'">
        <i class="a-icon-import-right mr-1"></i>
        导入
      </a-button>
      <a-button :loading="exportLoading" @click="handleExport" v-auth="'bas:ct_bas_lease_unit_change_record:exportXls'">
        <i class="a-icon-export-right mr-1"></i>
        导出
      </a-button>
      <a-dropdown v-if="selectedRowKeys.length">
        <a-button>
          批量操作
          <i class="a-icon-arrow-down ml-[8px]"></i>
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item key="batchDelete">
              <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <s-input
        v-model="searchParams.number"
        placeholder="搜索编号"
        class="ml-[40px] !w-[280px]"
        @input="handleSearch"
      ></s-input>
      <filter-more
        :params="searchParams"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag
            :dict-value="record.status"
            dict-code="CT_BASE_ENUM_HouseOwner_AuditStatus"
            type="dot"
          ></status-tag>
        </template>
        <template v-if="column.dataIndex === 'remark'">
          <div class="line-clamp-2" :title="record.remark">{{ record.remark }}</div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)" v-auth="'bas:ct_bas_lease_unit_change_record:view'">
            查看
          </span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item key="submit" v-if="['BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleSubmit(record)">提交</div>
                </a-menu-item>
                <a-menu-item key="back" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleBack(record)">撤回</div>
                </a-menu-item>
                <a-menu-item key="audit" v-if="record.status === 'AUDITING'">
                  <div class="primary-btn" @click="handleAudit(record)">审核</div>
                </a-menu-item>
                <a-menu-item key="unAudit" v-if="record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleUnAudit(record)">反审核</div>
                </a-menu-item>
                <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('租赁单元变更记录导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>

    <edit-lease-unit-info-change ref="editDrawerRef" @refresh="onTableChange"></edit-lease-unit-info-change>
    <lease-unit-info-change-detail ref="detailDrawerRef" @refresh="onTableChange"></lease-unit-info-change-detail>
  </div>
</template>

<script setup>
import { Modal, message } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import EditLeaseUnitInfoChange from './components/EditLeaseUnitChangeRecord.vue'
import LeaseUnitInfoChangeDetail from './components/LeaseUnitChangeRecordDetail.vue'
import {
  queryLeaseUnitChangeRecordEntryByMainId,
  getLeaseUnitChangeRecordList,
  deleteLeaseUnitChangeRecord,
  batchDeleteLeaseUnitChangeRecord,
  submitLeaseUnitChangeRecord,
  backLeaseUnitChangeRecord,
  auditLeaseUnitChangeRecord,
  unAuditLeaseUnitChangeRecord,
  importExcel,
  exportExcel
} from './apis'

const editDrawerRef = ref()
const detailDrawerRef = ref()
const commonImportRef = ref()

const exportLoading = ref(false)
let timer

const searchParams = reactive({
  column: 'number',
  order: 'desc',
  number: undefined,
  leaseUnit: undefined,
  manageCompany: undefined,
  status: undefined,
  bizDate: undefined,
  createBy: undefined,
  remark: undefined
})

const searchList = reactive([
  { label: '租赁单元', name: 'leaseUnit', type: 'lease-unit-select', placeholder: '请选择租赁单元' },
  { label: '管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择管理公司' },
  {
    label: '单据状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_HouseOwner_AuditStatus',
    placeholder: '请选择单据状态'
  },
  { label: '业务日期', name: 'bizDate', type: 'date', placeholder: '请选择业务日期' },
  { label: '提交人', name: 'createBy', type: 'user-select', placeholder: '请选择提交人' },
  { label: '变更说明', name: 'remark', type: 'input', placeholder: '请输入变更说明' }
])

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 220, fixed: 'left' },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', width: 160, ellipsis: true },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '状态', dataIndex: 'status', width: 120 },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '提交人', dataIndex: 'createBy_dictText', width: 120 },
  { title: '变更说明', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getLeaseUnitChangeRecordList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

/**
 * 新建信息变更单
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 查看详情
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 编辑信息变更单
 */
const handleEdit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:edit')) return
  editDrawerRef.value.open(record)
}

/**
 * 提交信息变更单
 */
const handleSubmit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:submit')) return
  Modal.confirm({
    title: '确认提交',
    content: `确定要提交单据编号为 "${record.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      // 先获取最新的租赁单元列表
      const { result } = await queryLeaseUnitChangeRecordEntryByMainId({ id: record.id })
      if (result && result.length > 0) {
        record.leaseUnitChangeRecordEntryList = result
      }
      // 提交变更申请
      await submitLeaseUnitChangeRecord(record)
      message.success('提交成功')
      onTableChange()
    }
  })
}

/**
 * 撤回信息变更单
 */
const handleBack = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回单据编号为 "${record.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await backLeaseUnitChangeRecord({ id: record.id })
      message.success('撤回成功')
      onTableChange()
    }
  })
}

/**
 * 审核信息变更单
 */
const handleAudit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: `确定要审核通过单据编号为 "${record.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await auditLeaseUnitChangeRecord({ id: record.id })
      message.success('审核成功')
      onTableChange()
    }
  })
}

/**
 * 反审核信息变更单
 */
const handleUnAudit = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: `确定要反审核单据编号为 "${record.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await unAuditLeaseUnitChangeRecord({ id: record.id })
      message.success('反审核成功')
      onTableChange()
    }
  })
}

/**
 * 删除信息变更单
 */
const handleDelete = (record) => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除单据编号为 "${record.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnitChangeRecord({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除信息变更单
 */
const handleBatchDelete = () => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:deleteBatch')) return
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的记录')
    return
  }
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteLeaseUnitChangeRecord({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      onTableChange()
    }
  })
}

/**
 * 导入
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('租赁单元变更记录清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 搜索输入防抖处理
 */
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格变化事件处理
 */
const onTableChange = ({ current = pagination.value.current, pageNo, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: pageNo ?? current, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})
</script>
