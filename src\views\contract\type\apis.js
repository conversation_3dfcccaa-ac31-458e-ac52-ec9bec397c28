import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/contractType/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/contractType/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/contractType/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/contractType/importExcel',
    data
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/contractType/updateEnableDisableStatus',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/contractType/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/contractType/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/contractType/deleteBatch',
    params
  })
}
