根据公司id，查询该公司下的所有部门数据
<template>
  <a-tree-select
    v-bind="$attrs"
    :value="modelValue || undefined"
    :style="{ width }"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-data="deptTree"
    show-search
    allow-clear
    :tree-default-expanded-keys="expandedKeys"
    :field-names="{ label: 'title', value: 'id' }"
    tree-node-filter-prop="title"
    @change="onchange"
  ></a-tree-select>
</template>

<script setup>
import { queryDepartTreeByCompanyId } from '@/views/system/depart/apis'
import useUserStore from '@/store/modules/user'

const { modelValue, companyId } = defineProps({
  width: { type: String, default: '100%' },
  companyId: { type: String }, // 如果有传，就根据该公司id查询部门，如果没传，默认就是查询当前登录用户所属公司
  modelValue: { type: String }
})

const emit = defineEmits(['update:modelValue', 'change'])

const { userInfo } = useUserStore()

const onchange = (val) => {
  emit('update:modelValue', val)
  emit('change', val)
}

const deptTree = ref([])
const loadDeptList = async () => {
  const { result } = await queryDepartTreeByCompanyId({ companyId: companyId || userInfo.value.currentCompany })
  deptTree.value = result && result.length ? result[0].children || [] : []
  expandedKeys.value = []
  if (deptTree.value.length) {
    loadExpandedKeys(deptTree.value)
  }
}

const expandedKeys = ref([]) // 让树形数据全部展开
const loadExpandedKeys = (list) => {
  list.forEach((item) => {
    expandedKeys.value.push(item.id)
    if (item.children && item.children.length) {
      loadExpandedKeys(item.children)
    }
  })
}

watch(
  () => companyId,
  (val) => {
    if (val) {
      loadDeptList()
    } else {
      deptTree.value = []
      expandedKeys.value = []
    }
  }
)

onMounted(() => {
  loadDeptList()
})
</script>
