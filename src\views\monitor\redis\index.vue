<template>
  <div>
    <!-- Redis 信息实时监控 -->
    <a-row :gutter="16">
      <a-col :sm="24" :xl="12">
        <com-chart
          title="Redis 内存实时占用情况（KB）"
          pie-title="内存占用量"
          chart-id="real-time-memory-usage"
          default-chart-type="line"
          :height="400"
          :chart-type-toggle="false"
          :data="realTimeMemoryUsageData"
        />
      </a-col>
      <a-col :sm="24" :xl="12">
        <com-chart
          title="Redis Key实时数量（个）"
          pie-title="实时数量"
          chart-id="real-time-quantity"
          default-chart-type="line"
          :height="400"
          :chart-type-toggle="false"
          :data="realTimeQuantityData"
        />
      </a-col>
    </a-row>

    <a-table
      class="mt-[16px]"
      :data-source="dataSource"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: 'calc(100vh - 710px)' }"
      :pagination="false"
      @change="getInfoList"
    ></a-table>
  </div>
</template>
<script setup>
import ComChart from '@/components/ComChart.vue'
import { getInfo, getRedisInfo, getMetricsHistory } from './apis'
import dayjs from 'dayjs'
onMounted(() => {
  loadHistoryData()
  openTimer()
  getInfoList()
})
onUnmounted(() => {
  closeTimer()
})
const realTimeMemoryUsageData = ref([])
const realTimeQuantityData = ref([])
let timer = null
/** 开启定时器 */
const openTimer = () => {
  closeTimer()
  timer = setInterval(() => {
    loadData()
  }, 15000)
}
/** 关闭定时器 */
const closeTimer = () => {
  if (timer) clearInterval(timer)
}

// 获取当前实时数据
const loadData = async () => {
  const data = await getRedisInfo()
  realTimeMemoryUsageData.value.shift()
  realTimeMemoryUsageData.value.push({
    name: dayjs(data[1].create_time).format('HH:MM:ss'),
    value: Number(data[1].used_memory)
  })
  realTimeQuantityData.value.shift()
  realTimeQuantityData.value.push({
    name: dayjs(data[0].create_time).format('HH:MM:ss'),
    value: data[0].dbSize
  })
}
// 获取历史数据
const loadHistoryData = async () => {
  const {
    result: { memory, dbSize }
  } = await getMetricsHistory()

  realTimeMemoryUsageData.value = memory.map((item) => {
    return {
      name: dayjs(item.create_time).format('HH:MM:ss'),
      value: Number(item.used_memory)
    }
  })
  realTimeQuantityData.value = dbSize.map((item) => {
    return {
      name: dayjs(item.create_time).format('HH:MM:ss'),
      value: item.dbSize
    }
  })
}

const tableLoading = ref(false)
const dataSource = ref([])
const columns = [
  {
    title: 'Key',
    dataIndex: 'key'
  },
  {
    title: 'Description',
    dataIndex: 'description'
  },
  {
    title: 'Value',
    dataIndex: 'value'
  }
]
// 表格数据
const getInfoList = async () => {
  tableLoading.value = true
  try {
    const { result } = await getInfo()
    dataSource.value = result
  } finally {
    tableLoading.value = false
  }
}
</script>
