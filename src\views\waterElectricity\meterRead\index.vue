<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd" v-auth="'bas:ct_bas_water_electricity_meter_read:add'">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'bas:ct_bas_water_electricity_meter_read:importExcel'">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button
          :loading="exportLoading"
          @click="handleExport"
          v-auth="'bas:ct_bas_water_electricity_meter_read:exportXls'"
        >
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.number"
          placeholder="搜索编码(表号)"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'thisMonthTableNumber'">
          {{ record.thisMonthTableNumber != null ? formatDecimals(record.thisMonthTableNumber, 4) : '-' }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleDelete(record)">删除</span>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('抄表数导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
    <edit-meter-read ref="editModalRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getMeterReadList, deleteMeterRead, batchDeleteMeterRead, exportExcel, importExcel } from './apis'
import { formatDecimals } from '@/utils/number'
import EditMeterRead from './components/EditMeterRead.vue'
import { f7List } from '@/views/waterElectricity/manage/apis/waterElectricity'
import useUserStore from '@/store/modules/user'

const { userInfo } = useUserStore()

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getMeterReadList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list)

const columnSetRef = ref()
const editModalRef = ref()
const commonImportRef = ref()

const exportLoading = ref(false)

const searchParams = reactive({
  column: 'number',
  order: 'desc',
  number: undefined,
  meterDate: undefined,
  type: undefined,
  property: undefined,
  thisMonthTableNumber: undefined,
  address: undefined,
  manageCompany: undefined,
  treeId: undefined,
  dataSource: undefined
})

const getWaterElectricityList = () =>
  f7List({ pageNo: 1, pageSize: 5000, manageCompany: userInfo.value.currentCompany })
const searchList = reactive([
  { label: '抄表时间', name: 'meterDate', type: 'date', placeholder: '请选择抄表时间' },
  {
    label: '类型',
    name: 'type',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Type',
    placeholder: '请选择类型'
  },
  {
    label: '属性',
    name: 'property',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Property',
    placeholder: '请选择属性'
  },
  { label: '表数', name: 'thisMonthTableNumber', type: 'input', placeholder: '请输入表数' },
  { label: '水电表', name: 'waterElectriCityTableNum', type: 'api-select', asyncFn: getWaterElectricityList },
  { label: '地址', name: 'address', type: 's-input', placeholder: '请输入地址' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择物业管理公司' },
  {
    label: '数据来源',
    name: 'dataSource',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterElectricityMeterRead_DataSource',
    placeholder: '请选择数据来源'
  }
])

const defaultColumns = [
  { title: '编码(表号)', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '抄表时间', dataIndex: 'meterDate', width: 120 },
  { title: '类型', dataIndex: 'type_dictText', width: 120 },
  { title: '属性', dataIndex: 'property_dictText', width: 120 },
  { title: '表数', dataIndex: 'thisMonthTableNumber', width: 120 },
  { title: '地址', dataIndex: 'address', width: 160, ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '分组', dataIndex: 'treeId_dictText', width: 160, ellipsis: true },
  { title: '数据来源', dataIndex: 'dataSource_dictText', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 打开新增抄表数弹窗
 */
const handleAdd = () => {
  editModalRef.value.open()
}

/**
 * 打开编辑抄表数弹窗
 * @param {Object} row 选中的行数据
 */
const handleEdit = (row) => {
  if (!hasPermission('bas:ct_bas_water_electricity_meter_read:edit')) return
  editModalRef.value.open(row)
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出抄表数据到Excel
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('抄表数清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 删除单条抄表数据
 * @param {Object} record 要删除的记录
 */
const handleDelete = (record) => {
  if (!hasPermission('bas:ct_bas_water_electricity_meter_read:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除编码为"${record.number}"的抄表数吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteMeterRead({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除选中的抄表数据
 */
const handleBatchDelete = () => {
  if (!hasPermission('bas:ct_bas_water_electricity_meter_read:deleteBatch')) return
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的${selectedRowKeys.value.length}条数据吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteMeterRead({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 搜索输入防抖处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格变化事件处理
 * @param {Object} params 分页参数
 */
const onTableChange = ({ current = pagination.value.current, pageNo, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: pageNo ?? current, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})
</script>
