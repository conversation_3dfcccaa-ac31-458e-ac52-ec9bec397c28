import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/orderBill/list',
    params
  })
}
// 提交
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/orderBill/submit',
    data
  })
}
// 添加
export const stash = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/orderBill/add',
    data
  })
}
// 编辑
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/orderBill/edit',
    data
  })
}
// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/orderBill/queryById?id=${id}`
  })
}

// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/orderBill/delete?id=${id}`
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/orderBill/deleteBatch?ids=${ids}`
  })
}
// 反审核
export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/orderBill/unAudit',
    data
  })
}
// 撤回
export const back = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/orderBill/back',
    data
  })
}
// 审核
export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/orderBill/audit',
    data
  })
}
// 收付款记录-根据订单ID查询
export const listByOrderBillId = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/payExplainBook/listByOrderBillId',
    params
  })
}
// 订单计费明细主表ID查询
export const queryOrderBillChargeDetailByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/orderBill/queryOrderBillChargeDetailByMainId?id=${id}`
  })
}

// 订单-生成应收单
export const createReceiveBill = (ids) => {
  return request({
    method: 'post',
    url: `/biz/tripartsettle/orderBill/generateReceiveBill?ids=${ids}`
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/orderBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/tripartsettle/orderBill/importExcel', data, controller)
}
