<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑预警单据类型信息' : '新建预警单据类型信息'"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '150px' } }" autocomplete="off">
        <a-form-item label="名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item label="类名key" name="classNameKey">
          <a-input v-model:value="form.classNameKey" placeholder="请输入类名key" />
        </a-form-item>
        <a-form-item label="主表类名key" name="classNameParentKey">
          <a-input v-model:value="form.classNameParentKey" placeholder="请输入主表类名key" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="form.remark" placeholder="请输入备注" :maxlength="255" :rows="4" show-count />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { add, edit, detail } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.name = result.name
  form.classNameKey = result.classNameKey
  form.status = result.status
  form.classNameParentKey = result.classNameParentKey
  form.remark = result.remark
  loading.value = false
}

const form = reactive({
  id: '',
  name: '',
  classNameKey: '',
  status: '',
  remark: '',
  classNameParentKey: ''
})

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  classNameKey: [{ required: true, message: '请输入类名key', trigger: 'blur' }],
  classNameParentKey: [{ required: true, message: '请输入主表类名key', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    form.id ? await edit(params) : await add(params)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.name = ''
  form.classNameKey = ''
  form.status = ''
  form.classNameParentKey = ''
  form.remark = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
