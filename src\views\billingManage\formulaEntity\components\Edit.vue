<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑公式实体类信息' : '新建公式实体类信息'"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '60px' } }" autocomplete="off">
        <a-form-item label="类路径" name="classPath">
          <a-input v-model:value="form.classPath" placeholder="请输入类路径" />
        </a-form-item>
        <a-form-item label="名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item label="类key" name="classKey">
          <a-input v-model:value="form.classKey" placeholder="请输入类key" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { add, edit, detail } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.classPath = result.classPath
  form.name = result.name
  form.classKey = result.classKey
  loading.value = false
}

const form = reactive({
  id: '',
  classPath: '',
  name: '',
  classKey: ''
})

const rules = {
  classPath: [{ required: true, message: '请输入类路径', trigger: 'blur' }],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  classKey: [{ required: true, message: '请输入类key', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    const data = await (form.id ? edit(params) : add(params))
    message.success(data.message)
    confirmLoading.value = false
    handleCancel()
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.classPath = ''
  form.name = ''
  form.classKey = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
