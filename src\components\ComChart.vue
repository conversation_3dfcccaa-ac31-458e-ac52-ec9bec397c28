<template>
  <a-card :style="{ height: `${Number(height) + 56}px` }">
    <div class="flex justify-between">
      <span class="text-[#1D335C] text-[16px]">{{ title }}</span>
      <div class="flex items-center">
        <slot></slot>
        <div v-if="chartTypeToggle" class="ml-[10px]">
          <a-select v-model:value="chartType" :options="chartTypeList" size="small" />
        </div>
      </div>
    </div>
    <div :style="{ height: `${height}px` }">
      <Chart v-if="chartType === 'pie'" :id="chartId" :option="option" />
      <Chart v-if="chartType === 'bar'" :id="chartId" :option="option" />
      <Chart v-if="chartType === 'line'" :id="chartId" :option="option" />
    </div>
  </a-card>
</template>

<script setup>
import * as echarts from 'echarts'
import { renderMoney } from '@/utils/render'
const props = defineProps({
  chartId: { type: String, default: '' },
  data: { type: Array, default: () => [] },
  title: { type: String, default: '' },
  defaultChartType: { type: String, default: 'pie' },
  pieTitle: { type: String, default: '' },
  colors: {
    type: Array,
    default: () => ['#397eff', '#28dcb9', '#7e7be6', '#ea609a', '#fa7373', '#FFA065', '#ffc501', '#8FD561', '#8CACCF']
  },
  chartTypeToggle: { type: Boolean, default: true },
  height: {
    type: [Number, String],
    default: 240
  }
})
const chartType = ref(props.defaultChartType)
const chartTypeList = [
  { label: '环形图', value: 'pie' },
  { label: '柱状图', value: 'bar' }
]

const total = computed(() => {
  return props.data.reduce((a, b) => {
    return a + Number(b.value)
  }, 0)
})

// 饼形图圆心文字大小
const circleCenterFontSize = (value) => {
  let symbolCount = 0 // 记录数值标点符号数量
  for (let i = 0; i < value.length; i++) {
    if (['.', ','].includes(value.charAt(i))) {
      symbolCount++
    }
  }
  // 小数点，和逗号，每3个，算作一个数字
  return Math.max(18, Math.ceil(120 / (value.length - Math.floor(symbolCount / 3))))
}

const option = computed(() => {
  if (chartType.value === 'pie') {
    return {
      title: [
        {
          text: `{name|${props.pieTitle}}\n{val|${renderMoney(total.value)}}`,
          top: 'center',
          left: '20%',
          textAlign: 'center',
          textStyle: {
            rich: {
              name: { fontSize: 14, fontWeight: 'normal', color: '#666666', padding: [10, 0] },
              val: { fontSize: circleCenterFontSize(renderMoney(total.value)), fontWeight: 'bold', color: '#333333' }
            }
          }
        }
      ],
      // 修改 tooltip 配置
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          return `${params.marker} ${params.name}: ${params.percent}% ${params.value}`
        }
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        icon: 'circle',
        right: 0,
        top: 'center',
        itemWidth: 12,
        itemHeight: 12,
        align: 'left',
        textStyle: {
          rich: {
            name: {
              fontSize: 14,
              width: 150, // 设置固定宽度确保对齐
              align: 'left'
            },
            unit: {
              width: 35
            },
            value: { fontSize: 14, align: 'right', width: 150 }
          }
        },
        formatter(name) {
          const item = props.data.find((v) => v.name === name) || {}
          const ratio = (item.ratio || 0).toFixed(2)
          return `{name|${name}} {unit|${ratio || 0}}% {value|${item.value}}`
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['50%', '80%'],
          center: ['20%', '50%'],
          data: props.data,
          hoverAnimation: false,
          itemStyle: { normal: { borderWidth: 2, borderColor: '#fff' } },
          labelLine: { normal: { length: 20, length2: 120, lineStyle: { color: '#e6e6e6' } } },
          label: {
            normal: {
              show: false,
              formatter: (params) => {
                return `{icon|●}{name|${params.name}}{value|${renderMoney(params.value)}}`
              },
              padding: [0, -100, 25, -100],
              rich: {
                icon: { fontSize: 16 },
                name: { fontSize: 14, padding: [0, 10, 0, 4], color: '#666666' },
                value: { fontSize: 18, fontWeight: 'bold', color: '#333333' }
              }
            }
          }
        }
      ]
    }
  }
  if (chartType.value === 'bar') {
    return {
      color: props.colors,
      grid: { top: '10%', left: '12%', right: '5%', bottom: '30%' },
      tooltip: {
        trigger: 'axis',
        formatter: '{b} : {c}',
        axisPointer: { type: 'cross', crossStyle: { color: '#999' } }
      },
      xAxis: {
        type: 'category',
        axisPointer: { type: 'shadow' },
        data: props.data.map((v) => v.name),
        axisLabel: {
          interval: 0,
          rotate: 0,
          formatter(value) {
            if (props.data.length > 12) {
              // 将文本拆分为单个字符数组
              const chars = value.split('')
              // 只取前4个字符
              const limitedChars = chars.slice(0, 4)
              // 将字符用换行符连接，实现每个字符一行
              const verticalText = limitedChars.join('\n')
              // 如果原文本超过4个字符，添加省略号
              return chars.length > 4 ? `${verticalText}\n...` : verticalText
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        splitLine: { show: true, lineStyle: { type: 'dashed' } },
        axisLabel: { formatter: '{value}', textStyle: { color: '#B4C0CC', fontSize: 12 } },
        name: '',
        nameTextStyle: { color: '#B3CFFF', fontSize: 12 }
      },
      series: [
        {
          type: 'bar',
          name: '',
          barWidth: 20,
          itemStyle: {
            normal: {
              borderRadius: '4 4 0 0',
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: 'rgba(57,126,255,1)', opacity: 1 },
                { offset: 0, color: 'rgba(0,216,254,1)', opacity: 0.6 }
              ])
            }
          },
          data: props.data.map((v) => v.value)
        }
      ],
      // 缩放组件配置
      dataZoom: [
        {
          type: 'slider', // 滑动条型数据区域缩放组件
          xAxisIndex: 0, // 控制哪个x轴
          backgroundColor: '#EFF1F6',
          height: 6,
          start: 0, // 初始起始位置(0-100)
          end: 100 // 初始结束位置(0-100)
        },
        {
          type: 'inside', // 内置型数据区域缩放组件
          xAxisIndex: 0, // 控制哪个x轴
          backgroundColor: '#BCC1CC',
          height: 6
        }
      ]
    }
  }
  if (chartType.value === 'line') {
    return {
      grid: { top: '10%', left: '70px', right: '2%', bottom: '10%' },
      tooltip: {
        trigger: 'axis',
        formatter: '{b} : {c}',
        axisPointer: { type: 'cross', crossStyle: { color: '#999' } }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: props.data.map((v) => v.name),
        axisLabel: {
          interval: 0,
          rotate: 0,
          formatter(value) {
            if (props.data.length > 12) {
              // 将文本拆分为单个字符数组
              const chars = value.split('')
              // 只取前4个字符
              const limitedChars = chars.slice(0, 4)
              // 将字符用换行符连接，实现每个字符一行
              const verticalText = limitedChars.join('\n')
              // 如果原文本超过4个字符，添加省略号
              return chars.length > 4 ? `${verticalText}\n...` : verticalText
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: props.data.map((v) => v.value),
          type: 'line',
          areaStyle: {
            color: '#74bcff'
          },
          lineStyle: {
            color: '#1890ff',
            width: 10,
            type: 'solid'
          }
        }
      ],
      // 缩放组件配置
      dataZoom: [
        {
          type: 'slider', // 滑动条型数据区域缩放组件
          xAxisIndex: 0, // 控制哪个x轴
          backgroundColor: '#EFF1F6',
          height: 6,
          start: 0, // 初始起始位置(0-100)
          end: 100 // 初始结束位置(0-100)
        },
        {
          type: 'inside', // 内置型数据区域缩放组件
          xAxisIndex: 0, // 控制哪个x轴
          backgroundColor: '#BCC1CC',
          height: 6
        }
      ]
    }
  }
  return {}
})
</script>
