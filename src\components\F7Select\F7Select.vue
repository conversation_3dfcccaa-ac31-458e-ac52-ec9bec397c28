<template>
  <div
    class="f7-select-input"
    :style="{ width }"
    @click="openModal"
    @mouseenter="onmouseenter"
    @mouseleave="onmouseleave"
  >
    <span class="f7-select-value line-clamp-1" :title="displayValue" :placeholder="placeholder">
      {{ displayValue }}
    </span>
    <i class="a-icon-close-solid text-[rgba(23,43,82,0.25)]" @click.stop="handleClear" v-if="showClearBtn"></i>
    <i class="a-icon-arrow-down text-[rgba(23,43,82,0.25)]" v-else></i>
  </div>
  <f7-modal
    :f7-type="f7Type"
    :select-value="modelValue"
    :multiple="multiple"
    :depart-id="departId"
    :extra-params="extraParams"
    ref="f7ModalRef"
    @update-value="updateValue"
  ></f7-modal>
</template>

<script setup>
import config from './config'
import F7Modal from './F7Modal.vue'
import { message } from 'ant-design-vue'

const { modelValue, multiple, f7Type, relationDepart, departId } = defineProps({
  modelValue: { type: [String, Array] },
  placeholder: { type: String, default: '' },
  multiple: { type: Boolean, default: false },
  width: { type: String, default: '100%' },
  // departId: 部门id，只有f7Type=user时会用到，用于筛选在某个部门下的用户
  departId: { type: String, default: '' },
  // relationDepart: 是否关联业务部门，只有“f7Type=user && 只能选择业务部门之下的人员”才会用到，为true时，如果没有departId，则不允许打开弹窗
  relationDepart: { type: Boolean, default: false },
  f7Type: {
    required: true,
    type: String,
    validator: (value) => {
      return [
        'user', // 系统用户
        'contract', // 合同
        'customer', // 客户
        'waterElectricity', // 水电表
        'leaseUnit', // 租赁单元
        'paymentType', // 款项类型
        'receiptPayment', // 收付款记录
        'receiptPaymentDetail', // 收付款记录明细
        'receiveBill', // 应收单
        'receiveBillDetail', // 应收单明细
        'refund', // 退款明细
        'transfer', // 转款明细
        'debt', // 抵扣欠款明细
        'project', // 项目
        'orderBillingItem', // 订单计费项
        'assets'
      ].includes(value)
    }
  },
  extraParams: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue', 'change'])

const displayValue = ref('')

const f7ModalRef = ref()
const openModal = () => {
  if (relationDepart && !departId) {
    message.warning('请先选择业务部门')
    return
  }
  f7ModalRef.value.open(selectedRows)
}

const showClear = ref(false)
const showClearBtn = computed(() => modelValue && modelValue.length > 0 && showClear.value)
const onmouseenter = () => {
  if (modelValue) {
    showClear.value = true
  }
}
const onmouseleave = () => {
  showClear.value = false
}

const handleClear = () => {
  emit('update:modelValue', multiple ? [] : '')
  emit('change', multiple ? [] : '')
  displayValue.value = ''
}

let selectedRows = []
const updateValue = (keys, values) => {
  if (!keys.length) {
    emit('update:modelValue', multiple ? [] : '')
    if (multiple) {
      emit('change', [], [])
    } else {
      emit('change', '', {})
    }
    displayValue.value = ''
    selectedRows = []
  } else {
    selectedRows = values
    emit('update:modelValue', multiple ? keys : keys[0])
    if (multiple) {
      emit('change', keys, values)
      displayValue.value = values.map((i) => i[config[f7Type].displayKey]).join(',')
    } else {
      emit('change', keys[0], values[0])
      displayValue.value = values[0][config[f7Type].displayKey]
    }
  }
}

const loadDataById = async (id) => {
  const { result } = await config[f7Type].request({ id })
  selectedRows = result.records
  displayValue.value = selectedRows.map((i) => i[config[f7Type].displayKey]).join(',')
}

watch(
  () => modelValue,
  // 监听modelValue的改变，以便回显displayValue和selectedRows
  (val) => {
    if (!(val && val.length)) {
      displayValue.value = ''
      selectedRows = []
      return
    }
    if (!selectedRows.length) {
      loadDataById(Array.isArray(val) ? val.join(',') : val)
      return
    }
    if (Array.isArray(val)) {
      if (selectedRows.length !== val.length) {
        loadDataById(val.join(','))
        return
      }
      const keys = selectedRows
        .map((i) => i[config[f7Type].rowKey])
        .sort()
        .join(',')
      const valKeys = val.sort().join(',')
      // 将两个字符串数组排序后转为逗号拼接字符串，比较是否相等，如果不等，则重新请求
      if (keys !== valKeys) {
        loadDataById(val.join(','))
      }
    } else {
      if (selectedRows.length !== 1) {
        loadDataById(val)
        return
      }
      if (selectedRows[0][config[f7Type].rowKey] !== val) {
        loadDataById(val)
      }
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.ant-form-item-has-error {
  .f7-select-input {
    border-color: var(--color-error) !important;
  }
}
.f7-select-input {
  width: 100%;
  cursor: pointer;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
  &:hover {
    border-color: var(--color-primary);
  }
}
.f7-select-value {
  &:empty::after {
    content: attr(placeholder);
    color: rgba(0, 0, 0, 0.25);
  }
}
</style>
