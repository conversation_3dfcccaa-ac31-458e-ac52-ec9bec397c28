<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="50%"
    title="编辑预警消息"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form :model="ruleForm" ref="formRef" :rules="rules" :label-col="{ style: { width: '70px' } }" autocomplete="off">
      <a-form-item label="预警消息" name="alterMessage">
        <a-textarea v-model:value="ruleForm.alterMessage" placeholder="请输入预警消息" :rows="20" show-count />
      </a-form-item>

      <a-form-item label=" ">
        <div class="mb-[10px]">
          <span class="text-error">注：</span>
          <span>
            sql主表别名命名为a,有排序需添加尾部需添加#{mergeCondition}，完整案例：select "alterMessage" alterMessage
            FROM ct_biz_receive_bill_entry a where a.id = '' #{mergeCondition} order by a.id
          </span>
        </div>
        <a-button type="primary" :loading="checkLoading" @click="check">校验预警消息</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { checkWarningMessage } from '../apis'
import { message } from 'ant-design-vue'
const emits = defineEmits(['change'])
const classPath = ref('')

const ruleForm = ref({
  alterMessage: ''
})
const rules = {
  alterMessage: [{ required: true, message: '请输入预警消息', trigger: ['blur'] }]
}
const visible = ref(false)
const open = (value, key = '') => {
  ruleForm.value.alterMessage = value
  classPath.value = key
  visible.value = true
}
defineExpose({ open })

const checkLoading = ref(false)
const check = async () => {
  checkLoading.value = true
  try {
    const data = await checkWarningMessage(ruleForm.value)
    message.success(data.message)
  } finally {
    checkLoading.value = false
  }
}
const handleOk = () => {
  emits('change', ruleForm.value.alterMessage)
  visible.value = false
}
/**
 * 取消选择
 */
const handleCancel = () => {
  ruleForm.value.alterMessage = ''
  visible.value = false
}
</script>
