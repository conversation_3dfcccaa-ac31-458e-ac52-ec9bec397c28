选择明细账单弹窗
<template>
  <a-modal
    v-model:open="visible"
    title="选择明细账单"
    width="1200px"
    class="common-modal bill-details-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center mb-[16px]">
      <s-input
        v-model:value="params.number"
        placeholder="搜索单据编号"
        @input="handleInput"
        class="!w-[280px] mr-[16px]"
      ></s-input>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: 2000 })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <filter-more
        :params="params"
        label-width="100px"
        :search-list="searchList"
        :stat-ignore-keys="['customer']"
        :clear-ignore-keys="['customer']"
        @query="onTableChange({ pageNo: 1, pageSize: 2000 })"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="false"
      children-column-name="detailBillEntryVOList"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        checkStrictly: false
      }"
      :scroll="{ x: 4500, y: 'calc(75vh - 120px)' }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'detailAddress'">
          {{ record.province }}{{ record.city }}{{ record.district }}{{ record.detailAddress }}
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
// import { getPage as billPage } from '@/views/statement/list/apis.js'
import { billPage } from '@/views/contract/billAdjust/apis'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message } from 'ant-design-vue'
import { renderDictTag, renderBoolean, renderMoney } from '@/utils/render'
import { f7List } from '@/views/paymentType/apis'

const emit = defineEmits(['updateList'])

const getPaymentType = () => f7List({ pageNo: 1, pageSize: 1000 })

const visible = ref(false)
const open = (list, paramsData) => {
  selectedRowKeys.value = list.map((item) => item.id)
  selectedRows.value = list
  if (paramsData) {
    for (const key in paramsData) {
      params[key] = paramsData[key]
    }
  }
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 2000 })
}

const { list, tableLoading, onTableFetch } = usePageTable(billPage, (list) => {
  list.forEach((item) => {
    // 因为要做成树形数据，所以要将外层的部分字段，设为跟子级一致的字段
    item.areaManager_dictText = item.mergeAreaManager_dictText // 片区管理员
    item.collectionCompany_dictText = item.mergeCollectionCompany_dictText // 租金归集公司
    item.periodTotalPeriod = item.mergePeriodSeq // 缴交周期
    item.receiveDate = item.mergeReceiveDate // 应收日期
    item.paymentAmount = item.sumPaymentAmount // 应收金额
    item.remission = item.sumRemission // 减免金额
    item.paid = item.sumPaid // 已收金额
    item.residual = item.sumResidual // 未收金额
    item.actualReceiveAmount = item.sumActualReceiveAmount // 实际应收金额
    item.transfered = item.sumTransfered // 已转应收金额
    item.transferdBalance = item.sumTransferdBalance // 未转应收金额
  })
  return list
})

const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id', false, true)

const params = reactive({
  number: undefined,
  customer: undefined,
  currentBillId: undefined,
  contract: undefined,
  paymentType: undefined,
  status: undefined,
  paymentAmount: undefined,
  actualReceiveAmount: undefined,
  paid: undefined,
  receiveDate: undefined
})
const searchList = [
  { label: '款项类型', name: 'paymentType', type: 'api-select', asyncFn: getPaymentType },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '款项金额', name: 'paymentAmount', type: 'input' },
  { label: '应收金额', name: 'actualReceiveAmount', type: 'input' },
  { label: '已收金额', name: 'paid', type: 'input' },
  { label: '应收日期', name: 'receiveDate', type: 'date' }
]

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 220, fixed: 'left' },
  { title: '合同编号', dataIndex: 'contractNumber', width: 180 },
  { title: '客户名称', dataIndex: 'customer_dictText' },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', customRender: ({ text }) => text || '-' },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', customRender: ({ text }) => text || '-' },
  {
    title: '账单/款项类型',
    dataIndex: 'paymentType_dictText',
    customRender: ({ record }) => record.billType_dictText || record.paymentType_dictText
  },
  { title: '缴交周期', dataIndex: 'periodTotalPeriod' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) || '-' },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text) },
  { title: '实际应收金额', dataIndex: 'actualReceiveAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '已转应收金额', dataIndex: 'transfered', customRender: ({ text }) => renderMoney(text) },
  { title: '未转应收金额', dataIndex: 'transferdBalance', customRender: ({ text }) => renderMoney(text) },
  { title: '已抵扣金额', dataIndex: 'transferDeduction', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '已退款金额', dataIndex: 'refunded', customRender: ({ text }) => renderMoney(text) || '-' },
  {
    title: '尾差已处理金额',
    dataIndex: 'offDifference',
    width: 140,
    customRender: ({ text }) => renderMoney(text) || '-'
  },
  { title: '可抵退转金额', dataIndex: 'balance', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '已提房产税', dataIndex: 'houseTax', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', customRender: ({ text }) => text || '-' },
  { title: '业务员', dataIndex: 'operator_dictText', customRender: ({ text }) => text || '-' },
  { title: '业务部门', dataIndex: 'operatorDepart_dictText', customRender: ({ text }) => text || '-' },
  { title: '业务日期', dataIndex: 'bizDate', customRender: ({ text }) => text || '-' },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot') || '-'
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_DetailBill_BizStatus', 'dot') || '-'
  }
]

const onTableChange = ({ current, pageNo = 1, pageSize = 2000 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: 2000 })
  }, 600)
}

const handleConfirm = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择明细账单')
    return
  }
  emit(
    'updateList',
    selectedRows.value.filter((item) => !item.detailBillEntryVOList)
  )
  handleCancel()
}

const handleCancel = () => {
  list.value = []
  clearSelection()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.bill-details-modal {
  top: 5vh;
  .ant-modal-body {
    max-height: 75vh !important;
  }
  .ant-table-row {
    .ant-table-cell:first-child {
      border-left: 4px solid transparent;
    }
  }
  .ant-table-row.ant-table-row-level-1 {
    .ant-table-cell:first-child {
      border-left: 4px solid var(--color-primary);
    }
  }
  .ant-table-row-selected > td {
    background-color: transparent !important;
  }
}
</style>
