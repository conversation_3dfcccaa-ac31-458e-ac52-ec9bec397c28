<!-- 资产选择 -->
<template>
  <a-select
    :multiple="multiple"
    v-model:value="localValue"
    :placeholder="placeholder"
    :open="false"
    :style="{ width: width }"
    :disabled="disabled"
    @click="handleHouseOwnerClick"
    :options="localOptions"
    allow-clear
    @change="localValueChange"
  />
  <!-- 选择资产弹窗 -->
  <assets-select-dialog
    ref="assetsSelectDialogRef"
    :multiple="multiple"
    :params="params"
    @selectChange="selectChange"
  ></assets-select-dialog>
</template>
<script setup>
import AssetsSelectDialog from './AssetsSelectDialog.vue'
onMounted(() => {
  localValue.value = modelValue || undefined
  localOptions.value = options || []
})

const emits = defineEmits(['update:modelValue', 'selectChange', 'change'])
const { modelValue, options, multiple, disabled } = defineProps({
  multiple: { default: false, type: Boolean },
  modelValue: { required: true, type: [String, Array] },
  options: { default: () => [], type: Array },
  placeholder: { default: '请选择', type: String },
  width: {
    default: '100%',
    type: String
  },
  params: {
    default: () => {
      return {}
    },
    type: Object
  },
  disabled: {
    default: false,
    type: Boolean
  }
})
const localValue = ref(undefined)
const localOptions = ref([])
// 监听 modelValue 变化并同步到 localValue
watch(
  () => modelValue,
  (newVal) => {
    localValue.value = newVal
  },
  { immediate: true, deep: true }
)
// 打开弹窗
const assetsSelectDialogRef = ref()
const handleHouseOwnerClick = () => {
  if (disabled) return
  assetsSelectDialogRef.value.open(localOptions.value.map((item) => item.value))
}
// 选择弹窗里数据选择后的回调
const selectChange = (list) => {
  const value = multiple ? list.map((item) => item.id) : list[0].id
  localValue.value = value
  emits('update:modelValue', value)
  emits('selectChange', list)
  localOptions.value = list.map((item) => {
    return {
      label: item.name,
      value: item.id
    }
  })
}
// 只改变的回调
const localValueChange = (val) => {
  if (!val) {
    localOptions.value = []
  }
}
</script>
