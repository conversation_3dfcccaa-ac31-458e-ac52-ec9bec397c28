<template>
  <a-drawer v-model:open="visible" title="预警方案详情" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          class="primary-btn"
          @click="handleEdit"
          v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)"
          v-auth="'biz.basicdatadeal:ct_gzb_idle_asset_tracking:edit'"
        >
          编辑
        </span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down text-[12px]"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item v-if="['AUDITING'].includes(detailData.status)">
                <div v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:edit'" class="primary-btn" @click="rowBack">
                  撤回
                </div>
              </a-menu-item>
              <a-menu-item v-if="['AUDITING'].includes(detailData.status)">
                <div v-auth="'bas:ct_bas_house_owner:audit'" class="primary-btn" @click="verify">审核通过(临时)</div>
              </a-menu-item>
              <a-menu-item v-if="['AUDITOK'].includes(detailData.status)">
                <div v-auth="'bas:ct_bas_house_owner:unAudit'" class="primary-btn" @click="reverse">反审核</div>
              </a-menu-item>
              <a-menu-item v-if="['TEMP', 'AUDITNO', 'BACK'].includes(detailData.status)">
                <div v-auth="'biz.basicdatadeal:ct_biz_house_deal_bill:delete'" class="primary-btn" @click="handleDel">
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">预警方案详情</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detailData.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>方案编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <h2 class="text-[#1D335C] text-[16px] font-bold mb-[12px]">预警方案基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">公司：{{ detailData.ctrlUnit_dictText || '-' }}</span>
        <span class="w-[50%]">方案编号：{{ detailData.number || '-' }}</span>
        <span class="w-[50%]">方案名称：{{ detailData.name || '-' }}</span>
        <span class="w-[50%]">预警实体：{{ detailData.alterBizBill_dictText || '-' }}</span>
        <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
        <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
        <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
      </div>

      <h2 class="text-[#1D335C] text-[16px] font-bold mb-[12px] mt-[40px]">预警方案分录</h2>
      <a-table
        :data-source="detailData.alterSchemeEntryList"
        :columns="columns"
        :pagination="false"
        :scroll="{ y: 300, x: 1500 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'alterMessage'">
            <a-tooltip :title="record.alterMessage" :overlay-style="{ maxWidth: '800px', whiteSpace: 'pre' }">
              <span class="primary-btn">查看</span>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'submitAlter'">
            <div>{{ record.submitAlter ? '预警' : '不预警' }}</div>
          </template>
          <template v-if="column.dataIndex === 'submitAlterType'">
            <div>
              {{ record.submitAlterType === 'Alter' ? '提醒' : record.submitAlterType === 'Forbidden' ? '禁止' : '' }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'auditAlter'">
            <div>{{ record.auditAlter ? '预警' : '不预警' }}</div>
          </template>
          <template v-if="column.dataIndex === 'auditAlterType'">
            <div>
              {{ record.auditAlterType === 'Alter' ? '提醒' : record.auditAlterType === 'Forbidden' ? '禁止' : '' }}
            </div>
          </template>
        </template>
      </a-table>
    </a-spin>
  </a-drawer>
  <add-edit ref="addEditRef" @loadData="getDetailById(detailData.id)"></add-edit>
</template>
<script setup>
import AddEdit from './AddEdit.vue'
import { detailById, queryAlterSchemeEntryByMainId, back, audit, unAudit, delById } from '../apis'
import { Modal, message } from 'ant-design-vue'
const emits = defineEmits(['loadData'])
const loading = ref(false)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} id 列表行id用于查看详情
 */
const open = (id) => {
  visible.value = true
  if (id) {
    getDetailById(id)
  }
}
defineExpose({ open })
const detailData = ref({
  alterSchemeEntryList: []
})
const columns = [
  // {
  //   title: '序号',
  //   dataIndex: 'index',
  //   width: 80,
  //   align: 'center',
  //   fixed: true,
  //   customRender: ({ index }) => index + 1
  // },
  // { title: '预警条件', dataIndex: 'ruleFormula', width: 300, fixed: true },
  { title: '预警消息', dataIndex: 'alterMessage', width: 200, fixed: true },
  {
    title: '提交预警',
    children: [
      {
        title: '预警时点',
        dataIndex: 'submitAlter'
      },
      {
        title: '预警方式',
        dataIndex: 'submitAlterType'
      }
      // {
      //   title: '预警对象',
      //   dataIndex: 'submitAlterWho'
      // }
    ]
  },
  {
    title: '审核预警',
    children: [
      {
        title: '预警时点',
        dataIndex: 'auditAlter'
      },
      {
        title: '预警方式',
        dataIndex: 'auditAlterType'
      }
      // {
      //   title: '预警对象',
      //   dataIndex: 'auditAlterWho'
      // }
    ]
  },
  {
    title: '周期预警',
    children: [
      {
        title: '定时表达式',
        dataIndex: 'clockingAlterCron',
        width: 200
      },
      { title: '消息接收人', dataIndex: 'clockingAlterWho_dictText' }
      // {
      //   title: '预警对象',
      //   children: [
      //     { title: '消息接收人', dataIndex: 'clockingAlterWho_dictText' },
      //     { title: '制单人领导', dataIndex: 'clockingAlterRole' }
      //   ]
      // }
    ]
  }
  // { title: '预警消息', dataIndex: 'alterMessage', width: 300 }
]
// 通过id获取详情
const getDetailById = async (id) => {
  loading.value = true
  try {
    const { result } = await detailById(id)
    const data = await queryAlterSchemeEntryByMainId(id)
    detailData.value = { ...result, alterSchemeEntryList: data.result }
  } finally {
    loading.value = false
  }
}
// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value.id)
}
// 撤回操作
const rowBack = () => {
  Modal.confirm({
    title: '确定撤回？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await back({ id: detailData.value.id })
      message.success(data.message)
      emits('loadData')
      getDetailById(detailData.value.id)
    }
  })
}
// 审核操作（临时）
const verify = () => {
  Modal.confirm({
    title: '确认审核通过？',
    content: '',
    async onOk() {
      const data = await audit({ id: detailData.value.id })
      message.success(data.message)
      emits('loadData')
      getDetailById(detailData.value.id)
    }
  })
}
// 反审核
const reverse = () => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await unAudit({ id: detailData.value.id })
      message.success(data.message)
      emits('loadData')
      getDetailById(detailData.value.id)
    }
  })
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前预警方案？',
    content: '',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      handleClose()
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>
