import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseRateScheme/queryLeaseRateSchemeEntryByMainId',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/exportEntryXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/importEntryExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/addEntry',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseRateScheme/editEntry',
    data
  })
}
export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseRateScheme/deleteEntryBatch',
    params
  })
}
