<template>
  <div class="!p-[0px] home">
    <img src="@/assets/imgs/home/<USER>" class="w-full" />
    <div class="content">
      <!-- 我的待办 -->
      <div class="flex items-center mb-[10px]">
        <img src="@/assets/svgs/todo.svg" class="w-[24px] h-[24px]" />
        <span class="text-[18px] font-bold text-[#1D335C] mx-[8px]">我的待办</span>
        <span class="ml-[4px] text-[#F03A1D] cursor-pointer">
          <i class="a-icon-warning-solid"></i>
          <span class="mx-[4px]">您有 0 条预警消息，请及时了解并关注</span>
          <i class="a-icon-right-arrow"></i>
        </span>
      </div>
      <a-row :gutter="[16, 16]">
        <a-col v-for="(t, index) in todoList" :key="t.id" :xs="2" :sm="4" :md="6">
          <div
            class="todo cursor-pointer"
            :style="{
              background: `url(${bg('todo', index)}) no-repeat`,
              backgroundSize: '100% 100%'
            }"
          >
            <div class="text-[#495A7A]">{{ t.name }}</div>
            <div class="text-[#1D335C] text-[40px] font-bold">{{ t.num }}</div>
            <div class="primary-btn mt-[10px]" @click="toPage(t.url)">
              <span>前去处理</span>
              <i class="a-icon-right-arrow"></i>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 我常用的 -->
      <div class="flex justify-between mt-[40px] mb-[10px]">
        <div class="flex items-center">
          <img src="@/assets/svgs/app.svg" class="w-[24px] h-[24px]" />
          <span class="text-[18px] font-bold text-[#1D335C] mx-[8px]">我常用的</span>
        </div>

        <div class="flex primary-btn custom" @click="setupCustomShortcuts">
          <i class="a-icon-setting"></i>
          <span>自定义</span>
        </div>
      </div>

      <a-row :gutter="[16, 16]">
        <a-col v-for="(u, index) in useList" :key="u.id" :xs="6" :sm="6" :md="4">
          <div
            class="use flex items-center cursor-pointer"
            :style="{ background: bgColor(index) }"
            @click="openPage(u)"
          >
            <div class="use-icon text-[20px] flex items-center justify-center" :style="{ background: iconBg(index) }">
              <i :class="u.customCommonFunctionData.icon"></i>
            </div>
            <span class="ml-[10px]">{{ u.customCommonFunctionData.name }}</span>
          </div>
        </a-col>
      </a-row>

      <!-- 数据看板 -->
      <div class="flex items-center justify-between mt-[40px] mb-[10px]">
        <div class="flex items-center">
          <img src="@/assets/svgs/chart.svg" class="w-[24px] h-[24px]" />
          <span class="text-[18px] font-bold text-[#1D335C] mx-[8px]">数据看板</span>
        </div>
        <company-select
          v-model="manageCompany"
          type="all"
          placeholder="全部公司"
          width="240px"
          allow-clear
          @change="loadData"
        ></company-select>
      </div>

      <stat-card :manage-company="manageCompany" />

      <a-row :gutter="[16, 16]" class="mt-[16px]">
        <a-col :md="12">
          <com-chart
            title="资产面积状态统计（m²）"
            pie-title="资产面积"
            chart-id="houseOwner-status-area"
            :data="leaseUnitStatusAreaData"
          />
        </a-col>
        <a-col :md="12">
          <com-chart
            title="资产数量状态分布（个）"
            pie-title="资产数量"
            chart-id="houseOwner-status-count"
            default-chart-type="bar"
            :data="leaseUnitStatusCountData"
          />
        </a-col>
        <a-col :md="12">
          <com-chart
            title="资产面积类型统计（m²）"
            pie-title="资产面积"
            chart-id="houseOwner-type-area"
            :data="houseOwnerTypeAreaData"
          />
        </a-col>
        <a-col :md="12">
          <com-chart
            title="资产数量类型分布（个）"
            pie-title="资产数量"
            default-chart-type="bar"
            chart-id="houseOwner-type-count"
            :data="houseOwnerTypeCountData"
          />
        </a-col>

        <a-col :md="12">
          <a-card class="h-[316px]">
            <div class="flex justify-between">
              <span class="text-[#1D335C] text-[16px]">新增客户数量统计 (个)</span>
              <a-radio-group v-model:value="dateType" size="small" button-style="solid" @change="loadCustomerCount">
                <a-radio-button value="month">本月</a-radio-button>
                <a-radio-button value="year">本年</a-radio-button>
              </a-radio-group>
            </div>
            <div class="h-[240px]">
              <Chart id="customer-chart" :option="customerOption" />
            </div>
          </a-card>
        </a-col>
        <a-col :md="12">
          <a-card class="h-[316px]">
            <div class="flex justify-between">
              <span class="text-[#1D335C] text-[16px]">财务应收统计 (万元)</span>
              <a-range-picker
                size="small"
                v-model:value="daterange"
                picker="month"
                value-format="YYYY-MM"
                format="YYYY-MM"
                :disabled-date="disabledDate"
                @calendarChange="handleCalendarChange"
                @change="loadAccountsReceivableStatistics"
              />
            </div>
            <div class="h-[240px]">
              <Chart id="money-num" :option="recMonthOption" />
            </div>
          </a-card>
        </a-col>
        <a-col :md="12">
          <com-chart
            title="财务收款金额类型统计（万）"
            pie-title="收款金额"
            default-chart-type="bar"
            chart-id="payment-type"
            :data="paymentTypeData"
          >
            <a-select v-model:value="paymentTypeYear" :options="yearList" size="small" @change="loadPaymentType" />
          </com-chart>
        </a-col>
        <a-col :md="12">
          <com-chart
            title="应收逾期账龄统计（万元）"
            pie-title="逾期账龄"
            chart-id="overdue-age"
            :data="overdueAgeData"
          />
        </a-col>
      </a-row>
    </div>
  </div>
  <!-- 编辑常用功能 -->
  <edit-custom-shortcuts ref="editCustomShortcutsRef" @refreshUserList="getUseList"></edit-custom-shortcuts>
</template>

<script setup>
import {
  leaseUnitStatusArea,
  leaseUnitStatusCount,
  houseOwnerTypeArea,
  houseOwnerTypeCount,
  customerCount,
  recMonth,
  recPaymentType,
  recOverdueAge
} from './apis'
import { message } from 'ant-design-vue'
import { listByCurrentUser } from '@/views/customShortcuts/apis'
import ComChart from '@/components/ComChart.vue'
import StatCard from './components/StatCard.vue'
import * as echarts from 'echarts'
import EditCustomShortcuts from './components/EditCustomShortcuts.vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/modules/user'
const router = useRouter()
const store = useUserStore()
const userInfo = computed(() => store.userInfo)
const manageCompany = ref('')
const todoList = reactive([
  { id: 1, name: '待审批', num: 0, url: '/task' },
  { id: 2, name: '我发起未完结', num: 0, url: '' },
  { id: 3, name: '已办未完结', num: 0, url: '' },
  { id: 4, name: '未读通知', num: 0, url: '' }
])

const useList = ref([
  // { id: 1, name: '新增客户', icon: 'a-icon-new-customer', url: '' },
  // { id: 2, name: '添加客户跟进', icon: 'a-icon-new-customer', url: '' },
  // { id: 3, name: '新建项目', icon: 'a-icon-new-file', url: '' },
  // { id: 4, name: '导入账单明细', icon: 'a-icon-new-file', url: '' },
  // { id: 3, name: '新增合同', icon: 'a-icon-new-file', url: '' },
  // { id: 4, name: '核销营收/缴费', icon: 'a-icon-new-file', url: '' },
  // { id: 3, name: '合同管理', icon: 'a-icon-new-file', url: '' },
  // { id: 4, name: '资料归档', icon: 'a-icon-new-file', url: '' }
])
const getUseList = async () => {
  const { result } = await listByCurrentUser()
  useList.value = result
}
// 我常用的 跳转夜间
const openPage = (item) => {
  router.push({ path: item.customCommonFunctionData.url })
}
const bg = (type, index) => {
  return new URL(`/src/assets/imgs/home/<USER>
}

const iconBg = (index) => {
  const colorList = ['#165DFF', '#05DFEB', '#FAB700', '#F03A1D']
  return colorList[index % 4]
}

const bgColor = (index) => {
  const colorList = ['#EAF0FE', '#E5FCFE', '#FFF8E5', '#FEEBEA']
  return colorList[index % 4]
}

const nowYear = new Date().getFullYear()
const yearList = reactive([])
for (let i = 0; i < 3; i++) {
  yearList.push({ label: `${nowYear - i}年`, value: nowYear - i })
}
const loadData = () => {
  loadLeaseUnitStatusArea()
  loadLeaseUnitStatusCount()
  loadLeaseOwnerTypeArea()
  loadLeaseOwnerCountArea()
  loadCustomerCount()
  loadAccountsReceivableStatistics()
  loadPaymentType()
  loadOverdueAge()
  getUseList()
}

// 资产面积状态统计
const leaseUnitStatusAreaData = ref([])
const loadLeaseUnitStatusArea = async () => {
  const { result } = await leaseUnitStatusArea({ manageCompany: manageCompany.value })
  result.forEach((r) => {
    r.name = r.bizStatus_dictText
    r.value = r.structureArea
  })
  leaseUnitStatusAreaData.value = result
}

// 资产面积状态分布(个)
const leaseUnitStatusCountData = ref([])
const loadLeaseUnitStatusCount = async () => {
  const { result } = await leaseUnitStatusCount({ manageCompany: manageCompany.value })
  const total = result.reduce((a, b) => {
    return a + b.count
  }, 0)
  result.forEach((r) => {
    r.name = r.bizStatus_dictText
    r.value = r.count
    r.ratio = (r.count / total) * 100
  })
  leaseUnitStatusCountData.value = result
}

// 资产面积类型统计
const houseOwnerTypeAreaData = ref([])
const loadLeaseOwnerTypeArea = async () => {
  const { result } = await houseOwnerTypeArea({ manageCompany: manageCompany.value })
  result.forEach((r) => {
    r.name = r.assetsType_dictText
    r.value = r.structureArea
  })
  houseOwnerTypeAreaData.value = result
}

// 资产数量类型分布
const houseOwnerTypeCountData = ref([])
const loadLeaseOwnerCountArea = async () => {
  const { result } = await houseOwnerTypeCount({ manageCompany: manageCompany.value })
  const total = result.reduce((a, b) => {
    return a + b.count
  }, 0)
  result.forEach((r) => {
    r.name = r.assetsType_dictText
    r.value = r.count
    r.ratio = (r.count / total) * 100
  })
  houseOwnerTypeCountData.value = result
}

// 新增客户数量统计(个)
const dateType = ref('month')
const customerOption = ref({})
const loadCustomerCount = async () => {
  const params = { manageCompany: manageCompany.value, dateType: dateType.value }
  if (dateType.value === 'year') {
    params.year = nowYear
  }
  const { result } = await customerCount(params)
  const xAxisData = result.map((r) => `${r.dateOrMonth}${dateType.value === 'year' ? '月' : '日'}`)
  const seriesFirst = { name: '意向客户', data: result.map((r) => r.intentionCount) }
  const seriesSecond = { name: '正式客户', data: result.map((r) => r.officialCount) }
  customerOption.value = initMulLineChart(xAxisData, seriesFirst, seriesSecond)
}

// 财务应收统计(万元)
const daterange = ref([dayjs().format('YYYY-01'), dayjs().format('YYYY-12')])
const startDate = ref('')
// 当用户选择开始日期时触发
const handleCalendarChange = (dates) => {
  if (dates) {
    return (startDate.value = dates[0]) // 更新开始日期
  }
  startDate.value = ''
}
const disabledDate = (current) => {
  if (!startDate.value) {
    return false // 如果还没选开始日期，不限制
  }
  const start = dayjs(startDate.value)
  const minEndDate = start // 结束日期不能早于开始日期
  const maxEndDate = start.add(2, 'year') // 结束日期不能晚于开始日期 + 2年
  // 如果 current 不在 [startDate, startDate + 2年] 范围内，就禁用
  return current < minEndDate || current > maxEndDate
}
const recMonthOption = ref({})
const loadAccountsReceivableStatistics = async () => {
  if (!daterange.value) return message.warning('请选择日期范围')
  const params = {
    company: manageCompany.value || userInfo.value.currentCompany,
    begin: daterange.value ? daterange.value[0] : '',
    end: daterange.value ? daterange.value[1] : ''
  }
  const { result } = await recMonth(params)
  const xAxisData = result.map((r) => r.month)
  const seriesFirst = { name: '应收合计', data: result.map((r) => r.receivable) }
  const seriesSecond = { name: '已收合计', data: result.map((r) => r.receipts) }
  const bar1Color = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 1, color: 'rgba(57,126,255,1)', opacity: 1 },
    { offset: 0, color: 'rgba(0,216,254,1)', opacity: 0.6 }
  ])
  const bar2Color = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 1, color: 'rgba(0,201,255,1)', opacity: 1 },
    { offset: 0, color: 'rgba(122,255,184,1)', opacity: 0.6 }
  ])
  const extraConfig = {
    tooltip: {
      // trigger: 'axis',
      // axisPointer: {
      //   type: 'cross',
      //   crossStyle: {
      //     color: '#999'
      //   }
      // }
    },
    color: [bar1Color, bar2Color],
    series: [
      {
        type: 'bar',
        name: seriesFirst.name,
        barWidth: 12,
        itemStyle: {
          normal: {
            borderRadius: 4,
            color(params) {
              return params.dataIndex === 5 ? bar1Color : '#C2E0FF'
            }
          }
        },
        data: seriesFirst.data || []
      },
      {
        type: 'bar',
        name: seriesSecond.name,
        barWidth: 12,
        itemStyle: {
          normal: {
            borderRadius: 4,
            color(params) {
              return params.dataIndex === 5 ? bar2Color : '#C5EBEB'
            }
          }
        },
        data: seriesSecond.data || []
      },
      {
        type: 'line',
        name: seriesFirst.name,
        // 设置折线颜色
        lineStyle: {
          color: '#3FBAFF'
        },
        data: seriesFirst.data || []
      },
      {
        type: 'line',
        name: seriesSecond.name,
        // 设置折线颜色
        lineStyle: {
          color: '#7AFFB8'
        },
        data: seriesSecond.data || []
      }
    ]
  }

  recMonthOption.value = initMulLineChart(xAxisData, seriesFirst, seriesSecond, extraConfig)
}

const initMulLineChart = (xAxisData, seriesFirst = {}, seriesSecond = {}, extraConfig = {}) => {
  return Object.assign(
    {
      grid: { top: '10%', left: '12%', right: '5%', bottom: '30%' },
      tooltip: {},
      legend: { bottom: 0 },
      xAxis: {
        type: 'category',
        axisPointer: { type: 'shadow' },
        data: xAxisData,
        axisLabel: {
          interval: 0,
          rotate: 0,
          formatter(value) {
            if (xAxisData.length > 12) {
              // 将文本拆分为单个字符数组
              const chars = value.split('')
              // 只取前4个字符
              const limitedChars = chars.slice(0, 4)
              // 将字符用换行符连接，实现每个字符一行
              const verticalText = limitedChars.join('\n')
              // 如果原文本超过4个字符，添加省略号
              return chars.length > 4 ? `${verticalText}\n...` : verticalText
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        splitLine: { show: true, lineStyle: { type: 'dashed' } },
        axisLabel: { formatter: '{value}', textStyle: { color: '#B4C0CC', fontSize: 12 } },
        nameTextStyle: { color: '#B3CFFF', fontSize: 12 }
      },
      series: [
        {
          type: 'bar',
          name: seriesFirst.name,
          barWidth: 12,
          itemStyle: {
            normal: {
              borderRadius: 4,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: 'rgba(57,126,255,1)', opacity: 1 },
                { offset: 0, color: 'rgba(0,216,254,1)', opacity: 0.6 }
              ])
            }
          },
          data: seriesFirst.data || []
        },
        {
          type: 'bar',
          name: seriesSecond.name,
          barWidth: 12,
          itemStyle: {
            normal: {
              borderRadius: 4,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: 'rgba(0,201,255,1)', opacity: 1 },
                { offset: 0, color: 'rgba(122,255,184,1)', opacity: 0.6 }
              ])
            }
          },
          data: seriesSecond.data || []
        }
      ],
      // 缩放组件配置
      dataZoom: [
        {
          type: 'slider', // 滑动条型数据区域缩放组件
          xAxisIndex: 0, // 控制哪个x轴
          backgroundColor: '#EFF1F6',
          height: 6,
          start: 0, // 初始起始位置(0-100)
          end: 100 // 初始结束位置(0-100)
        },
        {
          type: 'inside', // 内置型数据区域缩放组件
          xAxisIndex: 0, // 控制哪个x轴
          backgroundColor: '#BCC1CC',
          height: 6
        }
      ]
    },
    extraConfig
  )
}

// 财务收款金额类型统计(万元)
const paymentTypeYear = ref(nowYear)
const paymentTypeData = ref([])
const loadPaymentType = async () => {
  const params = { manageCompany: manageCompany.value, dateType: 'year', year: paymentTypeYear.value }
  const { result } = await recPaymentType(params)
  const total = result.reduce((a, b) => {
    return a + b.amount
  }, 0)
  result.forEach((r) => {
    r.name = r.paymentType
    r.value = r.amount || 0
    r.ratio = (r.value / total) * 100
  })
  paymentTypeData.value = result
}

// 应收逾期账龄统计(万元)
const overdueAgeData = ref([])
const loadOverdueAge = async () => {
  const { result } = await recOverdueAge({ manageCompany: manageCompany.value })
  const total = result.reduce((a, b) => {
    return a + b.amount
  }, 0)
  result.forEach((r) => {
    r.name = r.durationType
    r.value = r.amount
    r.ratio = (r.amount / total) * 100
  })
  overdueAgeData.value = result
}

const toPage = (path) => {
  router.push({ path })
}
// 自定常用功能
const editCustomShortcutsRef = ref()
const setupCustomShortcuts = () => {
  editCustomShortcutsRef.value.open(
    useList.value.map((item) => {
      return {
        id: item.id,
        customCommonFunction: item.customCommonFunction,
        url: item.customCommonFunctionData.url,
        name: item.customCommonFunctionData.name
      }
    })
  )
}

onMounted(() => {
  loadData()
})
</script>

<style lang="less" scoped>
.home {
  background: linear-gradient(to bottom, #fff 10%, #f7feff 100%);
}

.content {
  max-width: 1680px;
  margin: -100px auto 0;
  padding: 0 20px 100px;
}

.todo {
  height: 154px;
  border-radius: 10px;
  padding: 20px;
}

.use {
  height: 64px;
  padding: 0 20px;
  color: #1d335c;
  border-radius: 8px;
  border: 1px solid #e6e9f0;

  &-icon {
    width: 28px;
    height: 28px;
    color: #fff;
    border-radius: 8px;
  }
}

.custom {
  cursor: pointer;
  height: 32px;
  line-height: 32px;
  padding: 0 10px;
  background: #eaf0fe;
  border-radius: 8px;
}
</style>
