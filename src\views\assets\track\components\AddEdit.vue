<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="ruleForm.id ? '编辑资产跟踪' : '新建资产跟踪'"
    placement="right"
    width="1072px"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '120px' } }"
      autocomplete="off"
    >
      <div class="mb-[12px] mt-[12px] flex items-center">
        <div class="text-[16px] font-bold">跟踪类型</div>
        <a-radio-group class="!m-0 !ml-[57px]" v-model:value="curTrackingType">
          <a-radio-button
            v-for="item in trackingTypeDic"
            :key="item.value"
            :value="item.value"
            :disabled="!!ruleForm.id"
          >
            {{ item.label }}
          </a-radio-button>
        </a-radio-group>
      </div>

      <div class="text-[16px] font-bold mb-[12px] mt-[12px]">跟踪信息</div>
      <a-row :gutter="24">
        <a-col :span="12" v-if="['Idle', 'Occupy'].includes(curTrackingType)">
          <a-form-item label="资产" name="houseOwner">
            <assets-select
              v-model="ruleForm.houseOwner"
              :options="
                ruleForm.houseOwner ? [{ value: ruleForm.houseOwner, label: ruleForm.houseOwner_dictText }] : []
              "
              placeholder="请选择资产"
              :disabled="hasHouseOwner"
              @selectChange="houseOwnerChange"
            ></assets-select>
            <!-- <f7-select v-model="ruleForm.houseOwner" f7-type="assets" placeholder="请选择所属项目" @change="houseOwnerChange"></f7-select> -->
          </a-form-item>
        </a-col>

        <!-- 闲置 -->
        <template v-if="curTrackingType === 'Idle'">
          <a-col :span="12">
            <a-form-item label="闲置面积" name="unUsedArea">
              <a-input-number
                allow-clear
                v-model:value="ruleForm.unUsedArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入闲置面积"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="闲置状态开始日期" name="unUsedBeginDate">
              <a-date-picker
                allow-clear
                v-model:value="ruleForm.unUsedBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择闲置状态开始日期"
                class="!w-[100%]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="闲置原因" name="unUsedReason">
              <a-input v-model:value="ruleForm.unUsedReason" placeholder="请输入闲置原因" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="闲置时间" name="unUsedTime">
              <dict-select
                v-model="ruleForm.unUsedTime"
                placeholder="请选择闲置时间"
                code="CT_BASE_ENUM_IdleAssetTrackingInfo_UnUsedTime"
              ></dict-select>
            </a-form-item>
          </a-col>
        </template>
        <!-- 占用 -->
        <template v-if="curTrackingType === 'Occupy'">
          <a-col :span="12">
            <a-form-item label="被占用面积" name="occupyArea">
              <a-input-number
                allow-clear
                v-model:value="ruleForm.occupyArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入被占用面积"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="占用人" name="occupyPerson">
              <a-input allow-clear v-model:value="ruleForm.occupyPerson" class="!w-[100%]" placeholder="请输入占用人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="占用原因" name="occupyReason">
              <a-input v-model:value="ruleForm.occupyReason" placeholder="请输入占用原因" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="被占用开始日期" name="occupyBeginDate">
              <a-date-picker
                allow-clear
                v-model:value="ruleForm.occupyBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择被占用开始日期"
                class="!w-[100%]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="其他情况" name="otherSituations">
              <a-checkbox v-model:checked="ruleForm.changeUse">改变用途</a-checkbox>
              <a-checkbox v-model:checked="ruleForm.illegal">违建</a-checkbox>
            </a-form-item>
          </a-col>
        </template>

        <a-col :span="12">
          <a-form-item label="业务日期" name="bizDate">
            <a-date-picker
              allow-clear
              v-model:value="ruleForm.bizDate"
              picker="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="请选择业务日期"
              class="!w-[100%]"
            />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="ruleForm.remark"
              placeholder="请输入备注"
              :maxlength="255"
              :rows="4"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>

      <div
        class="mb-[12px] mt-[12px] flex justify-between items-center"
        v-if="['Idle', 'Occupy'].includes(curTrackingType)"
      >
        <div class="text-[16px] font-bold">盘活记录</div>
        <a-button type="primary" ghost @click="handleAddIdleAssetActivateList">
          <span class="a-icon-plus mr-[8px]"></span>
          增加记录
        </a-button>
      </div>
      <div
        class="mb-[12px] mt-[12px] flex justify-between items-center"
        v-if="['Borrow', 'Self'].includes(curTrackingType)"
      >
        <div class="text-[16px] font-bold">{{ curTrackingType === 'Borrow' ? '借用资产' : '自用资产' }}</div>
        <a-button type="primary" ghost @click="handleAddIdleAssetActivateList">
          <span class="a-icon-plus mr-[8px]"></span>
          添加
        </a-button>
      </div>
      <!-- 闲置 -->
      <draggable
        v-if="curTrackingType === 'Idle'"
        v-model="ruleForm.idleAssetActivateList"
        handle=".a-icon-move"
        item-key="id"
      >
        <template #item="{ element, index }">
          <div class="row">
            <a-form-item
              class="col"
              :label="index ? '' : '操作'"
              name=""
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
            >
              <i
                class="a-icon-move cursor-move text-secondary mr-[10px] text-[18px]"
                v-if="showMoveBtn(element, index)"
              ></i>
              <i class="a-icon-remove cursor-pointer text-secondary text-[18px]" @click="warningVisible = true"></i>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['idleAssetActivateList', index, 'fillDate']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="[
                {
                  required: true,
                  message: '请选择盘活措施填报日期',
                  trigger: 'change'
                }
              ]"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '盘活措施填报日期' }}
                </span>
              </template>
              <a-date-picker
                allow-clear
                v-model:value="element.fillDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择闲置时间"
                class="!w-[100%]"
              />
            </a-form-item>
            <a-form-item
              class="col"
              :name="['idleAssetActivateList', index, 'ctrlMeasure']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="[
                {
                  required: true,
                  message: '请输入已采取的盘活管理措施',
                  trigger: 'change'
                }
              ]"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '已采取的盘活管理措施' }}
                </span>
              </template>
              <a-input
                v-model:value="element.ctrlMeasure"
                placeholder="请输入已采取的盘活管理措施"
                allow-clear
              ></a-input>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['idleAssetActivateList', index, 'activateAdvise']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="[
                {
                  required: true,
                  message: '请输入下一个盘活建议',
                  trigger: 'change'
                }
              ]"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '下一个盘活建议' }}
                </span>
              </template>
              <a-input v-model:value="element.activateAdvise" placeholder="请输入下一步盘活建议" allow-clear></a-input>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['idleAssetActivateList', index, 'activateMethod']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="{
                required: true,
                message: '请选择盘活方式',
                trigger: 'change'
              }"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '盘活方式' }}
                </span>
              </template>
              <dict-select
                v-model="element.activateMethod"
                placeholder="请选择盘活方式"
                code="CT_BASE_ENUM_AssetActivateMethod"
              ></dict-select>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['idleAssetActivateList', index, 'isActivate']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="{
                required: true,
                message: '请选择是否已盘活',
                trigger: 'change'
              }"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '是否已盘活' }}
                </span>
              </template>
              <a-select v-model:value="element.isActivate" placeholder="请选择是否已盘活" :options="isOrNotDic" />
            </a-form-item>
          </div>
        </template>
      </draggable>
      <!-- 占用 -->
      <draggable
        v-if="curTrackingType === 'Occupy'"
        v-model="ruleForm.occupyAssetActivateList"
        handle=".a-icon-move"
        item-key="id"
      >
        <template #item="{ element, index }">
          <div class="flex row">
            <a-form-item
              class="col"
              :label="index ? '' : '操作'"
              name=""
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
            >
              <i
                class="a-icon-move cursor-move text-secondary mr-[10px] text-[18px]"
                v-if="showMoveBtn(element, index)"
              ></i>
              <i class="a-icon-remove cursor-pointer text-secondary text-[18px]" @click="warningVisible = true"></i>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['occupyAssetActivateList', index, 'fillDate']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="{
                required: true,
                message: '请选择盘活措施填报日期',
                trigger: 'change'
              }"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '盘活措施填报日期' }}
                </span>
              </template>
              <a-date-picker
                allow-clear
                v-model:value="element.fillDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择闲置时间"
                class="!w-[100%]"
              />
            </a-form-item>
            <a-form-item
              class="col"
              :name="['occupyAssetActivateList', index, 'ctrlMeasure']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="{
                required: true,
                message: '请输入已采取的盘活管理措施',
                trigger: 'change'
              }"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '已采取的盘活管理措施' }}
                </span>
              </template>
              <a-input
                v-model:value="element.ctrlMeasure"
                placeholder="请输入已采取的盘活管理措施"
                allow-clear
              ></a-input>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['occupyAssetActivateList', index, 'activateAdvise']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="{
                required: true,
                message: '请输入下一个盘活建议',
                trigger: 'change'
              }"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '下一个盘活建议' }}
                </span>
              </template>
              <a-input v-model:value="element.activateAdvise" placeholder="请输入下一步盘活建议" allow-clear></a-input>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['occupyAssetActivateList', index, 'activateMethod']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="{
                required: true,
                message: '请选择盘活方式',
                trigger: 'change'
              }"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '盘活方式' }}
                </span>
              </template>
              <dict-select
                v-model="element.activateMethod"
                placeholder="请选择盘活方式"
                code="CT_BASE_ENUM_AssetActivateMethod"
              ></dict-select>
            </a-form-item>
            <a-form-item
              class="col"
              :name="['occupyAssetActivateList', index, 'isActivate']"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              :rules="{
                required: true,
                message: '请选择是否已盘活',
                trigger: 'change'
              }"
            >
              <template #label v-if="index === 0">
                <span class="table-header-col">
                  {{ index ? '' : '是否已盘活' }}
                </span>
              </template>
              <a-select v-model:value="element.isActivate" placeholder="请选择是否已盘活" :options="isOrNotDic" />
            </a-form-item>
          </div>
        </template>
      </draggable>

      <!-- 借用 -->
      <a-table
        :row-key="(record, index) => record.id || index"
        v-if="curTrackingType === 'Borrow'"
        :data-source="ruleForm.borrowAssetTrackingInfoList"
        :columns="borrowColumns"
        :scroll="{ y: 300, x: 2000 }"
        :pagination="false"
      >
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'houseOwner'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'borrowBeginDate'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>

          <template v-if="column.dataIndex === 'borrowPerson'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'borrowReason'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'houseOwner'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['borrowAssetTrackingInfoList', index, 'houseOwner']"
              :rules="{
                required: true,
                message: '请选择资产',
                trigger: ['change', 'blur']
              }"
            >
              <div class="flex items-center w-[100%]">
                <span
                  class="a-icon-remove remove-btn cursor-pointer text-[18px] mr-[5px]"
                  @click="rowDel(index)"
                ></span>
                <assets-select
                  width="85%"
                  v-model="record.houseOwner"
                  :options="[{ value: record.houseOwner, label: record.houseOwner_dictText }]"
                  placeholder="请选择资产"
                  @selectChange="rowSelectChange($event, record)"
                ></assets-select>
              </div>
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'borrowBeginDate'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['borrowAssetTrackingInfoList', index, 'borrowBeginDate']"
              :rules="{
                required: true,
                message: '请选择借用开始时间',
                trigger: 'change'
              }"
            >
              <a-date-picker
                allow-clear
                v-model:value="record.borrowBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择借用开始时间"
                class="!w-[100%]"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'borrowEndDate'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['borrowAssetTrackingInfoList', index, 'borrowEndDate']"
              :rules="{
                required: false,
                message: '请选择借用结束时间',
                trigger: 'change'
              }"
            >
              <a-date-picker
                allow-clear
                v-model:value="record.borrowEndDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择借用结束时间"
                class="!w-[100%]"
                disabled
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'borrowPerson'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['borrowAssetTrackingInfoList', index, 'borrowPerson']"
              :rules="{
                required: true,
                message: '请选择借用人',
                trigger: 'change'
              }"
            >
              <a-input v-model:value="record.borrowPerson" placeholder="请输入借用人" allow-clear></a-input>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'borrowReason'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['borrowAssetTrackingInfoList', index, 'borrowReason']"
              :rules="{
                required: true,
                message: '请输入借用原因',
                trigger: 'change'
              }"
            >
              <a-input v-model:value="record.borrowReason" placeholder="请输入借用原因" allow-clear></a-input>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'changeUse'">
            <a-form-item class="mb-[24px]" label="" :name="['borrowAssetTrackingInfoList', index, 'changeUse']">
              <a-checkbox v-model:checked="record.changeUse"></a-checkbox>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'remark'">
            <a-form-item class="mb-[24px]" label="" :name="['borrowAssetTrackingInfoList', index, 'remark']">
              <a-input v-model:value="record.remark" placeholder="请输入备注" allow-clear></a-input>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-form-item class="mb-[24px]" label="" :name="['borrowAssetTrackingInfoList', index, 'status']">
              <dict-select
                disabled
                v-model="record.status"
                placeholder="请选择状态"
                code="CT_BASE_ENUM_SelfAssetTrackingInfo_Status"
              ></dict-select>
            </a-form-item>
          </template>
        </template>
      </a-table>

      <!-- 自用 -->
      <a-table
        :row-key="(record, index) => record.id || index"
        v-if="curTrackingType === 'Self'"
        :data-source="ruleForm.selfAssetTrackingInfoList"
        :columns="borrowColumns"
        :scroll="{ y: 300, x: 2200 }"
        :pagination="false"
      >
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'houseOwner'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'selfBeginDate'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
          <template v-if="column.dataIndex === 'selfUse'">
            <span class="table-header-col">{{ column.title }}</span>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'houseOwner'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['selfAssetTrackingInfoList', index, 'houseOwner']"
              :rules="{
                required: true,
                message: '请选择资产',
                trigger: ['change', 'blur']
              }"
            >
              <div class="flex items-center">
                <span
                  class="a-icon-remove remove-btn cursor-pointer text-[18px] mr-[5px]"
                  @click="rowDel(index)"
                ></span>
                <assets-select
                  width="85%"
                  v-model="record.houseOwner"
                  :options="[{ value: record.houseOwner, label: record.houseOwner_dictText }]"
                  placeholder="请选择资产"
                  @selectChange="rowSelectChange($event, record)"
                ></assets-select>
              </div>
            </a-form-item>
          </template>

          <template v-if="column.dataIndex === 'selfBeginDate'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['selfAssetTrackingInfoList', index, 'selfBeginDate']"
              :rules="{
                required: true,
                message: '请选择自用开始时间',
                trigger: 'change'
              }"
            >
              <a-date-picker
                allow-clear
                v-model:value="record.selfBeginDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择自用开始时间"
                class="!w-[100%]"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'selfEndDate'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['selfAssetTrackingInfoList', index, 'selfEndDate']"
              :rules="{
                required: false,
                message: '请选择自用结束时间',
                trigger: 'change'
              }"
            >
              <a-date-picker
                allow-clear
                v-model:value="record.selfEndDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择自用结束时间"
                class="!w-[100%]"
                disabled
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'selfArea'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['selfAssetTrackingInfoList', index, 'selfArea']"
              :rules="{
                required: true,
                message: '请输入自用面积',
                trigger: 'blur'
              }"
            >
              <a-input-number
                v-model:value="record.selfArea"
                :min="0"
                :precision="2"
                placeholder="请输入自用面积"
                allow-clear
              ></a-input-number>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'selfUse'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['selfAssetTrackingInfoList', index, 'selfUse']"
              :rules="{
                required: true,
                message: '请输入使用用途',
                trigger: 'blur'
              }"
            >
              <a-input v-model:value="record.selfUse" placeholder="请输入使用用途" allow-clear></a-input>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'profitable'">
            <a-form-item class="mb-[24px]" label="" :name="['selfAssetTrackingInfoList', index, 'profitable']">
              <a-checkbox v-model:checked="record.profitable"></a-checkbox>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'devStandards'">
            <a-form-item class="mb-[24px]" label="" :name="['selfAssetTrackingInfoList', index, 'devStandards']">
              <a-checkbox v-model:checked="record.devStandards"></a-checkbox>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'outLimit'">
            <a-form-item class="mb-[24px]" label="" :name="['selfAssetTrackingInfoList', index, 'outLimit']">
              <a-checkbox v-model:checked="record.outLimit"></a-checkbox>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'remark'">
            <a-form-item
              class="mb-[24px]"
              label=""
              :name="['selfAssetTrackingInfoList', index, 'remark']"
              :rules="{
                required: true,
                message: '请输入备注',
                trigger: 'change'
              }"
            >
              <a-input v-model:value="record.remark" placeholder="请输入备注" allow-clear></a-input>
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-form-item class="mb-[24px]" label="" :name="['selfAssetTrackingInfoList', index, 'status']">
              <dict-select
                disabled
                v-model="record.status"
                placeholder="请选择状态"
                code="CT_BASE_ENUM_SelfAssetTrackingInfo_Status"
              ></dict-select>
            </a-form-item>
          </template>
        </template>
      </a-table>
    </a-form>

    <template #footer>
      <a-button type="primary" @click="handleConfirm">提交</a-button>
      <a-button type="primary" ghost @click="handleStash" :loading="stashLoading">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>

    <a-modal v-model:open="warningVisible" title="确定删除盘活记录吗？" @ok="handleDel" :closable="false"></a-modal>
  </a-drawer>
</template>
<script setup>
import {
  idleAdd,
  occupyAdd,
  borrowAdd,
  selfAdd,
  idleSubmit,
  occupySubmit,
  borrowSubmit,
  selfSubmit,
  idleEdit,
  occupyEdit,
  borrowEdit,
  selfEdit,
  queryIdleAssetActivateByMainId,
  queryOccupyAssetActivateByMainId,
  queryBorrowAssetTrackingInfoByMainId,
  querySelfAssetTrackingInfoByMainId
} from '../apis.js'
import { detailById } from '@/views/assets/manage/apis'
import { useDictStore } from '@/store/modules/dict'
import { message, Modal } from 'ant-design-vue'
import draggable from 'vuedraggable'
import dayjs from 'dayjs'
import { isOrNotDic } from '@/store/modules/dict.js'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const hasHouseOwner = ref(false)
const open = (data = {}, houseOwnerData = {}) => {
  if (data.id) {
    handleEditData(data)
  } else {
    // 新增
    ruleForm.value.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
  }
  // 资产详情-资产跟踪操作
  if (houseOwnerData.id) {
    hasHouseOwner.value = true
    ruleForm.value.houseOwner = houseOwnerData.id
    ruleForm.value.houseOwner_dictText = houseOwnerData.name
    ruleForm.value.borrowAssetTrackingInfoList = [
      {
        id: '',
        houseOwner: houseOwnerData.id,
        houseOwner_dictText: houseOwnerData.name,
        ownerNumber: houseOwnerData.ownerNumber,
        collectionCompany_dictText: houseOwnerData.collectionCompany_dictText,
        ownerCompany_dictText: houseOwnerData.ownerCompany_dictText,
        structureArea: houseOwnerData.structureArea,
        assetsType_dictText: houseOwnerData.assetsType_dictText,
        borrowBeginDate: '',
        borrowEndDate: '',
        borrowPerson: '',
        borrowReason: '',
        changeUse: false,
        remark: '',
        status: 'UnFinish'
      }
    ]
    ruleForm.value.selfAssetTrackingInfoList = [
      {
        id: '',
        houseOwner: houseOwnerData.id,
        houseOwner_dictText: houseOwnerData.name,
        ownerNumber: houseOwnerData.ownerNumber,
        collectionCompany_dictText: houseOwnerData.collectionCompany_dictText,
        ownerCompany_dictText: houseOwnerData.ownerCompany_dictText,
        structureArea: houseOwnerData.structureArea,
        assetsType_dictText: houseOwnerData.assetsType_dictText,
        selfBeginDate: '',
        selfEndDate: '',
        selfUse: '',
        profitable: false,
        devStandards: false,
        outLimit: false,
        selfArea: '',
        remark: '',
        status: 'UnFinish'
      }
    ]
  }
  visible.value = true
}
defineExpose({ open })

const handleEditData = (data) => {
  // 编辑
  curTrackingType.value = data.trackingType
  // 闲置占用才需要校验总面积
  if (['Idle', 'Occupy'].includes(curTrackingType.value)) {
    getAllAreaByHouseOwner(data.houseOwner)
  }
  getRecordsByMainId(data)
  ruleForm.value = { ...ruleForm.value, ...data }
}

// 通过资产id获取资产的总面积
const getAllAreaByHouseOwner = async (id) => {
  const { result } = await detailById(id)
  curStructureArea.value = result.structureArea
}

const getRecordsByMainId = async (data) => {
  let recordsByMainIdFunc = null
  if (data.trackingType === 'Idle') {
    recordsByMainIdFunc = queryIdleAssetActivateByMainId
  } else if (data.trackingType === 'Occupy') {
    recordsByMainIdFunc = queryOccupyAssetActivateByMainId
  } else if (data.trackingType === 'Borrow') {
    recordsByMainIdFunc = queryBorrowAssetTrackingInfoByMainId
  } else if (data.trackingType === 'Self') {
    recordsByMainIdFunc = querySelfAssetTrackingInfoByMainId
  }
  const { result: records } = await recordsByMainIdFunc(data.id)
  if (data.trackingType === 'Idle') {
    ruleForm.value.idleAssetActivateList = records
  }
  if (data.trackingType === 'Occupy') {
    ruleForm.value.occupyAssetActivateList = records
  }
  if (data.trackingType === 'Borrow') {
    ruleForm.value.borrowAssetTrackingInfoList = records
  }
  if (data.trackingType === 'Self') {
    ruleForm.value.selfAssetTrackingInfoList = records
  }
}

const curTrackingType = ref('Borrow')
const store = useDictStore()
const trackingTypeDic = computed(() => {
  if (!store.dict) return []
  if (!Object.keys(store.dict).length) return []
  return store.dict.CT_BASE_ENUM_TrackingType
})
// Borrow:借用 Occupy：占用；Idle：闲置 Self：自用
const requestFunc = computed(() => {
  if (curTrackingType.value === 'Idle') {
    return {
      add: idleAdd,
      submit: idleSubmit,
      edit: idleEdit
    }
  }
  if (curTrackingType.value === 'Occupy') {
    return {
      add: occupyAdd,
      submit: occupySubmit,
      edit: occupyEdit
    }
  }
  if (curTrackingType.value === 'Borrow') {
    return {
      add: borrowAdd,
      submit: borrowSubmit,
      edit: borrowEdit
    }
  }
  return {
    add: selfAdd,
    submit: selfSubmit,
    edit: selfEdit
  }
})

const ruleForm = ref({
  id: '',
  bizDate: '',
  remark: '',
  trackingType: '',
  houseOwner: undefined, // 闲置和占用
  // 闲置
  unUsedArea: '',
  unUsedBeginDate: '',
  unUsedEndDate: '',
  unUsedReason: '',
  unUsedTime: '',
  // 占用
  occupyArea: '',
  occupyPerson: '',
  occupyReason: '',
  occupyBeginDate: '',
  occupyEndDate: '',
  changeUse: false,
  illegal: false,
  idleAssetActivateList: [],
  occupyAssetActivateList: [],
  borrowAssetTrackingInfoList: [],
  selfAssetTrackingInfoList: []
})

const submitRuleForm = computed(() => {
  if (curTrackingType.value === 'Idle') {
    return {
      trackingType: curTrackingType.value,
      id: ruleForm.value.id,
      houseOwner: ruleForm.value.houseOwner,
      unUsedArea: ruleForm.value.unUsedArea,
      unUsedBeginDate: ruleForm.value.unUsedBeginDate,
      unUsedReason: ruleForm.value.unUsedReason,
      unUsedTime: ruleForm.value.unUsedTime,
      bizDate: ruleForm.value.bizDate,
      remark: ruleForm.value.remark,
      idleAssetActivateList: ruleForm.value.idleAssetActivateList
    }
  }
  if (curTrackingType.value === 'Occupy') {
    return {
      trackingType: curTrackingType.value,
      id: ruleForm.value.id,
      houseOwner: ruleForm.value.houseOwner,
      occupyArea: ruleForm.value.occupyArea,
      occupyPerson: ruleForm.value.occupyPerson,
      occupyReason: ruleForm.value.occupyReason,
      occupyBeginDate: ruleForm.value.occupyBeginDate,
      changeUse: ruleForm.value.changeUse,
      illegal: ruleForm.value.illegal,
      bizDate: ruleForm.value.bizDate,
      remark: ruleForm.value.remark,
      occupyAssetActivateList: ruleForm.value.occupyAssetActivateList
    }
  }
  if (curTrackingType.value === 'Borrow') {
    return {
      trackingType: curTrackingType.value,
      id: ruleForm.value.id,
      bizDate: ruleForm.value.bizDate,
      remark: ruleForm.value.remark,
      borrowAssetTrackingInfoList: ruleForm.value.borrowAssetTrackingInfoList
    }
  }
  return {
    trackingType: curTrackingType.value,
    id: ruleForm.value.id,
    bizDate: ruleForm.value.bizDate,
    remark: ruleForm.value.remark,
    selfAssetTrackingInfoList: ruleForm.value.selfAssetTrackingInfoList
  }
})

const rules = computed(() => {
  if (curTrackingType.value === 'Idle') {
    return {
      houseOwner: [{ required: true, message: '请选择资产', trigger: ['change', 'blur'] }],
      unUsedArea: [
        { required: true, message: '请输入闲置面积', trigger: ['blur'] },
        { validator: validateOccupyArea, trigger: 'blur' }
      ],
      unUsedBeginDate: [{ required: true, message: '请选择闲置状态开始日期', trigger: ['change'] }],
      unUsedReason: [{ required: true, message: '请输入闲置原因', trigger: ['blur'] }],
      bizDate: [{ required: true, message: '请选择业务日期', trigger: ['change'] }]
    }
  }
  if (curTrackingType.value === 'Occupy') {
    return {
      houseOwner: [{ required: true, message: '请选择资产', trigger: ['change', 'blur'] }],
      occupyArea: [
        { required: true, message: '请输入被占用面积', trigger: ['blur'] },
        { validator: validateOccupyArea, trigger: 'blur' }
      ],
      occupyPerson: [{ required: true, message: '请输入占用人', trigger: ['blur'] }],
      occupyReason: [{ required: true, message: '请输入占用原因', trigger: ['blur'] }],
      occupyBeginDate: [{ required: true, message: '请选择被占用开始日期', trigger: ['change'] }],
      bizDate: [{ required: true, message: '请选择业务日期', trigger: ['change'] }]
    }
  }
  return {
    bizDate: [{ required: true, message: '请选择业务日期', trigger: ['change'] }]
  }
})
const handleAddIdleAssetActivateList = () => {
  switch (curTrackingType.value) {
    case 'Idle':
      ruleForm.value.idleAssetActivateList.push({
        id: '',
        fillDate: '',
        ctrlMeasure: '',
        activateAdvise: '',
        activateMethod: '',
        isActivate: undefined,
        seq: undefined,
        parent: '',
        remark: '',
        delFlag: undefined
      })
      break
    case 'Occupy':
      ruleForm.value.occupyAssetActivateList.push({
        id: '',
        fillDate: '',
        ctrlMeasure: '',
        activateAdvise: '',
        activateMethod: '',
        isActivate: undefined,
        seq: undefined,
        parent: '',
        remark: '',
        delFlag: undefined
      })
      break
    case 'Borrow':
      ruleForm.value.borrowAssetTrackingInfoList.push({
        id: '',
        houseOwner: undefined,
        borrowBeginDate: '',
        borrowEndDate: '',
        borrowPerson: '',
        borrowReason: '',
        changeUse: false,
        remark: '',
        status: 'UnFinish'
      })
      break
    case 'Self':
      ruleForm.value.selfAssetTrackingInfoList.push({
        id: '',
        houseOwner: undefined,
        selfBeginDate: '',
        selfEndDate: '',
        selfUse: '',
        profitable: false,
        devStandards: false,
        outLimit: false,
        selfArea: '',
        remark: '',
        status: 'UnFinish'
      })
      break
  }
}
const showMoveBtn = () => {
  let boolean = false
  switch (curTrackingType.value) {
    case 'Idle':
      boolean = ruleForm.value.idleAssetActivateList.length > 1
      break
    case 'Occupy':
      boolean = ruleForm.value.occupyAssetActivateList.length > 1
      break
    case 'Borrow':
      boolean = ruleForm.value.borrowAssetTrackingInfoList.length > 1
      break
    case 'Self':
      boolean = ruleForm.value.selfAssetTrackingInfoList.length > 1
      break
  }

  return boolean
}
const warningVisible = ref(false)
const handleDel = (element, index) => {
  switch (curTrackingType.value) {
    case 'Idle':
      ruleForm.value.idleAssetActivateList.splice(index, 1)
      break
    case 'Occupy':
      ruleForm.value.occupyAssetActivateList.splice(index, 1)
      break
    case 'Borrow':
      ruleForm.value.borrowAssetTrackingInfoList.splice(index, 1)
      break
    case 'Self':
      ruleForm.value.selfAssetTrackingInfoList.splice(index, 1)
      break
  }
  warningVisible.value = false
}

const borrowColumns = computed(() => {
  if (curTrackingType.value === 'Borrow') {
    return [
      { title: '资产', dataIndex: 'houseOwner', width: 200, fixed: true },
      { title: '产权号', dataIndex: 'ownerNumber' },
      { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
      { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', ellipsis: true },
      { title: '建筑面积', dataIndex: 'structureArea' },
      { title: '资产类型', dataIndex: 'assetsType_dictText' },
      { title: '借用开始时间', dataIndex: 'borrowBeginDate', width: 180 },
      { title: '借用结束时间', dataIndex: 'borrowEndDate', width: 180 },
      { title: '借用人', dataIndex: 'borrowPerson' },
      { title: '借用原因', dataIndex: 'borrowReason', width: 180 },
      { title: '改变用途', dataIndex: 'changeUse' },
      { title: '备注', dataIndex: 'remark' },
      { title: '状态', dataIndex: 'status' }
    ]
  }
  return [
    { title: '资产', dataIndex: 'houseOwner', width: 200, fixed: true },
    { title: '产权号', dataIndex: 'ownerNumber' },
    { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
    { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', ellipsis: true },
    { title: '建筑面积', dataIndex: 'structureArea' },
    { title: '资产类型', dataIndex: 'assetsType_dictText' },
    { title: '自用开始时间', dataIndex: 'selfBeginDate', width: 180 },
    { title: '自用结束时间', dataIndex: 'selfEndDate', width: 180 },
    { title: '自用面积（㎡）', dataIndex: 'selfArea', width: 150 },
    { title: '使用用途', dataIndex: 'selfUse' },
    { title: '有收益', dataIndex: 'profitable' },
    { title: '定制办公用房标准', dataIndex: 'devStandards', width: 180 },
    { title: '超标', dataIndex: 'outLimit' },
    { title: '备注', dataIndex: 'remark' },
    { title: '状态', dataIndex: 'status' }
  ]
})

// 当前选择的资产所占的面积
const curStructureArea = ref(0)
// 选择资产后的回调
const houseOwnerChange = (list) => {
  if (list.length) {
    curStructureArea.value = list[0].structureArea
  }
}
// 校验占用面积
const validateOccupyArea = (rule, value, callback) => {
  if (curStructureArea.value && value > curStructureArea.value) {
    if (curTrackingType.value === 'Idle') {
      return callback(new Error(`闲置面积不能大于资产的总面积（${curStructureArea.value}㎡）`))
    }
    return callback(new Error(`被占用面积不能大于资产的总面积（${curStructureArea.value}㎡）`))
  }
  callback()
}
// 借用资产 行 选择资产后的回调
const rowSelectChange = (list, row) => {
  const selectItem = list[0]
  // 需要展示 产权号 租金归集公司 资产权属公司 建筑面积 资产类型
  row.ownerNumber = selectItem.ownerNumber
  row.collectionCompany_dictText = selectItem.collectionCompany_dictText
  row.ownerCompany_dictText = selectItem.ownerCompany_dictText
  row.structureArea = selectItem.structureArea
  row.assetsType_dictText = selectItem.assetsType_dictText
}

// 自用、借用资产记录删除操作
const rowDel = (index) => {
  Modal.confirm({
    title: '确定删除当前资产？',
    content: '',
    centered: true,
    onOk: () => {
      // 借用
      if (curTrackingType.value === 'Borrow') {
        ruleForm.value.borrowAssetTrackingInfoList.splice(index, 1)
        return
      }
      ruleForm.value.selfAssetTrackingInfoList.splice(index, 1)
    }
  })
}

// 提交
const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  const data = await requestFunc.value.submit({ ...submitRuleForm.value })
  message.success(data.message)
  emits('loadData')
  handleCancel()
}
// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  await formRef.value.validate()
  stashLoading.value = true
  try {
    const { result } = await (ruleForm.value.id
      ? requestFunc.value.edit({ ...submitRuleForm.value })
      : requestFunc.value.add({ ...submitRuleForm.value }))
    message.success('保存成功')
    handleEditData(result) // 返回值覆盖
    emits('loadData')
    stashLoading.value = false
  } finally {
    stashLoading.value = false
  }
}
// 取消
const handleCancel = () => {
  ruleForm.value = {
    id: '',
    bizDate: '',
    remark: '',
    trackingType: '',
    houseOwner: undefined, // 闲置和占用
    // 闲置
    unUsedArea: '',
    unUsedBeginDate: '',
    unUsedEndDate: '',
    unUsedReason: '',
    unUsedTime: '',
    // 占用
    occupyArea: '',
    occupyPerson: '',
    occupyReason: '',
    occupyBeginDate: '',
    occupyEndDate: '',
    changeUse: false,
    illegal: false,
    idleAssetActivateList: [],
    occupyAssetActivateList: [],
    borrowAssetTrackingInfoList: [],
    selfAssetTrackingInfoList: []
  }
  formRef.value.clearValidate()
  visible.value = false
}
</script>
<style lang="less" scoped>
.row {
  display: flex;
  width: 100%;
  min-width: 0;
  gap: 20px;
  .col:first-child {
    flex: 0 0 50px;
  }
  .col:not(:first-child) {
    flex: 1;
    min-width: 0;
  }
}
</style>
