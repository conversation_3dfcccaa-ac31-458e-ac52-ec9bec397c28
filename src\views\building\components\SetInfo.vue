设置水电分摊信息
<template>
  <div>
    <a-table :data-source="list" :columns="columns" :pagination="false" :scroll="{ y: '50vh', x: 2100 }">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'shareType'">
          <div class="flex items-center">
            <a-popconfirm title="是否确认移除？" @confirm="handleRemoveWaterElectricity(index)">
              <div class="cursor-pointer text-error mr-[12px]">
                <i class="a-icon-remove text-[16px]"></i>
              </div>
            </a-popconfirm>
            <dict-select
              v-model="record.shareType"
              code="CT_BASE_ENUM_WaterShareBill_ShareType"
              width="140px"
              @change="onShareTypeChange(record)"
            ></dict-select>
          </div>
        </template>
        <template v-if="column.dataIndex === 'waterEleTableNum'">
          <span v-if="record.shareType === 'rubbish' || !record.shareType">-</span>
          <f7-table-select
            v-model="record.waterEleTableNum"
            f7-type="waterElectricity"
            :mitt-id="mittId"
            @change="(val, data) => onWaterElectricityChange(val, data, record)"
            style="width: 200px"
            :field-names="{ label: 'name', value: 'id' }"
            @click="handleF7SelectClick(record)"
            v-else
          ></f7-table-select>
        </template>
        <template v-if="column.dataIndex === 'doubleRate'">
          <a-input v-if="record.shareType === 'rubbish'" v-model:value="record.doubleRate"></a-input>
          <span v-else>{{ record.doubleRate }}</span>
        </template>
        <template v-if="column.dataIndex === 'price'">
          <a-input v-if="record.shareType === 'rubbish'" v-model:value="record.price">
            <template #suffix>元</template>
          </a-input>
          <span v-else>{{ record.price ? `${renderMoney(record.price, 6, '元')}` : '' }}</span>
        </template>
        <template v-if="column.dataIndex === 'unitShare'">
          <common-select
            v-model="record.unitShare"
            :field-names="{ label: 'name', value: 'id' }"
            :options="unitShareList"
          ></common-select>
        </template>
        <template v-if="column.dataIndex === 'shareAmount'">
          <common-select
            v-model="record.shareAmount"
            :field-names="{ label: 'name', value: 'id' }"
            :options="shareAmountList"
          ></common-select>
        </template>
        <template v-if="column.dataIndex === 'remission'">
          <common-select
            v-model="record.remission"
            :field-names="{ label: 'name', value: 'id' }"
            :options="remissionList"
          ></common-select>
        </template>
        <template v-if="column.dataIndex === 'selfAmount'">
          <common-select
            v-model="record.selfAmount"
            :field-names="{ label: 'name', value: 'id' }"
            :options="selfAmountList"
          ></common-select>
        </template>
        <template v-if="column.dataIndex === 'totalAmount'">
          <common-select
            v-model="record.totalAmount"
            :field-names="{ label: 'name', value: 'id' }"
            :options="totalAmountList"
          ></common-select>
        </template>
        <template v-if="column.dataIndex === 'taxAmount'">
          <common-select
            v-model="record.taxAmount"
            :field-names="{ label: 'name', value: 'id' }"
            :options="taxAmountList"
          ></common-select>
        </template>
        <template v-if="column.dataIndex === 'containTaxTotalAmount'">
          <common-select
            v-model="record.containTaxTotalAmount"
            :field-names="{ label: 'name', value: 'id' }"
            :options="containTaxTotalAmountList"
          ></common-select>
        </template>
      </template>
    </a-table>
    <a-button @click="handleAddWaterElectricity" type="primary" ghost size="medium" class="mt-[16px]">
      <i class="a-icon-plus"></i>
      添加
    </a-button>
    <f7-modal
      f7-type="waterElectricity"
      :extra-params="{ filter: 'RelationUsed', manageCompany: userInfo.currentCompany, type: typeValue }"
      :mitt-id="mittId"
    ></f7-modal>
  </div>
</template>

<script setup>
import { getWaterElectricityList } from '@/views/waterElectricity/manage/apis/waterElectricity.js'
import { message, Modal } from 'ant-design-vue'
import { moneyRegexp } from '@/utils/validate'
import { checkBeUsed } from '../apis/building'
import { renderMoney } from '@/utils/render'
import { page as getFormulaPage } from '@/views/billingManage/formula/apis'
import useUserStore from '@/store/modules/user'

const { list, mainId, relationType } = defineProps({
  /**
   * mittId: f7-modal组件和f7-table-select组件的通信id
   * 因为编辑楼栋时，既有楼栋的水电分摊，又有楼层的水电分摊，该组件在edit-building里就会出现两次
   * 也就意为着，会出现两个f7-modal，两个f7-modal需要用不同的mittId区分开，否则就会出现同时打开两个f7-modal的情况
   */
  mittId: { type: String, default: 'water-electricity' },
  mainId: { required: true, type: String }, // 主键id，传入项目/楼栋/楼层/租赁单元的id，用于校验选择的表号是否已被占用
  /**
   * 关联类型，项目/楼栋/楼层/租赁单元，数据字典CT_BASE_ENUM_WaterShareFormulaRelations_RelationType
   * 项目: WyProject
   * 楼栋: WyBuilding
   * 楼层: WyFloor
   * 租赁单元: LeaseUnit
   */
  relationType: { required: true, type: String },
  list: { required: true, type: Array }
})

const { userInfo } = useUserStore()

const columns = [
  { title: '分摊类别', dataIndex: 'shareType', width: 200, fixed: 'left' },
  { title: '编码(表号)', dataIndex: 'waterEleTableNum', width: 240 },
  { title: '类型', dataIndex: 'type_dictText' }, // 由水电表号数据带来的字段回显，不需要传给后端
  { title: '属性', dataIndex: 'property_dictText' }, // 由水电表号数据带来的字段回显，不需要传给后端
  { title: '倍率', dataIndex: 'doubleRate' },
  { title: '单价', dataIndex: 'price' },
  { title: '单位分摊计算公式', dataIndex: 'unitShare' },
  { title: '公摊金额计算公式', dataIndex: 'shareAmount' },
  { title: '减免金额计算公式', dataIndex: 'remission' },
  { title: '自用金额计算公式', dataIndex: 'selfAmount' },
  { title: '合计计算公式', dataIndex: 'totalAmount' },
  { title: '税金计算公式', dataIndex: 'taxAmount' },
  { title: '含税合计计算公式', dataIndex: 'containTaxTotalAmount' }
]

const waterElectricityList = ref([]) // 水电表列表
const loadWaterElectricityList = async () => {
  const { result } = await getWaterElectricityList({ pageNo: 1, pageSize: 10000 })
  waterElectricityList.value = result.records.map((item) => {
    item.label = `${item.name}(${item.number})`
    item.value = item.id
    return item
  })
}

const onShareTypeChange = (record) => {
  record.waterEleTableNum = ''
  record.doubleRate = ''
  record.price = ''
  record.ullageQuantity = ''
  record.unitShare = ''
  record.shareAmount = ''
  record.remission = ''
  record.totalAmount = ''
  record.taxAmount = ''
  record.containTaxTotalAmount = ''
  record.selfAmount = ''
  record.type_dictText = ''
  record.property_dictText = ''
}

const typeValue = ref('') // 传递给f7-modal的筛选参数type，水电表类型 Water=水表 electricity=电表
const handleF7SelectClick = (record) => {
  typeValue.value = record.shareType === 'water' ? 'Water' : 'electricity'
}

const onWaterElectricityChange = async (val, data, record) => {
  if (!val) {
    record.type_dictText = ''
    record.property_dictText = ''
    record.doubleRate = ''
    record.price = ''
    return
  }
  const res = await checkBeUsed({ id: mainId, waterEleTableNum: val })
  if (res.result) {
    message.error(res.message)
    record.waterEleTableNum = ''
    return
  }
  record.type_dictText = data.type_dictText
  record.property_dictText = data.property_dictText
  record.doubleRate = data.doubleRate
  record.price = data.price
}

const handleAddWaterElectricity = () => {
  list.push({
    relationType,
    shareType: '',
    waterEleTableNum: '',
    doubleRate: '',
    price: '',
    ullageQuantity: '',
    unitShare: '',
    shareAmount: '',
    remission: '',
    totalAmount: '',
    taxAmount: '',
    containTaxTotalAmount: '',
    selfAmount: '',
    type_dictText: '', // 前端自定义字段，由水电表号数据带来的字段回显，不需要传给后端
    property_dictText: '' // 前端自定义字段，由水电表号数据带来的字段回显，不需要传给后端
  })
}
const handleRemoveWaterElectricity = (index) => {
  list.splice(index, 1)
}

const validate = () => {
  if (!list.length) return true
  const result = list.some((item, index) => {
    if (!item.shareType) {
      message.warning(`第${index + 1}条数据请选择分摊类别`)
      return true
    }
    if (item.shareType !== 'rubbish') {
      if (!item.waterEleTableNum) {
        message.warning(`第${index + 1}条数据请选择表号`)
        return true
      }
    }
    if (item.shareType === 'water' && item.type_dictText !== '水表') {
      message.warning(`第${index + 1}条数据，分摊类别为水费分摊，只允许选择水表`)
      return true
    }
    if (item.shareType === 'electricity' && item.type_dictText !== '电表') {
      message.warning(`第${index + 1}条数据，分摊类别为电费分摊，只允许选择电表`)
      return true
    }
    if (item.doubleRate && !moneyRegexp.test(item.doubleRate)) {
      message.warning(`第${index + 1}条数据倍率填写不正确`)
      return true
    }
    if (item.price && !moneyRegexp.test(item.price)) {
      message.warning(`第${index + 1}条数据单价填写不正确`)
      return true
    }
    return false
  })
  if (result) return false
  const stat = {}
  list.forEach((item, index) => {
    if (item.waterEleTableNum) {
      if (stat[item.waterEleTableNum]) {
        stat[item.waterEleTableNum].push(index)
      } else {
        stat[item.waterEleTableNum] = [index]
      }
    }
  })
  const isRepeat = Object.values(stat).some((item) => item.length > 1)
  if (isRepeat) {
    const messageList = []
    for (const key in stat) {
      if (stat[key].length > 1) {
        messageList.push(`第${stat[key].map((i) => i + 1).join('、')}行数据，表号重复`)
      }
    }
    Modal.warning({
      title: '系统提示',
      content: `水电分摊信息中，${messageList.join('；')}，请重新选择`,
      centered: true
    })
    return false
  }
  return true
}

const formulaList = ref([]) // 公式列表
const loadFormulaList = async () => {
  const { result } = await getFormulaPage({ pageNo: 1, pageSize: 10000, status: 'ENABLE' })
  formulaList.value = result.records
}

const unitShareList = computed(() => formulaList.value.filter((item) => item.calculResult === 'unitShare'))
const shareAmountList = computed(() => formulaList.value.filter((item) => item.calculResult === 'shareAmount'))
const remissionList = computed(() => formulaList.value.filter((item) => item.calculResult === 'remission'))
const selfAmountList = computed(() => formulaList.value.filter((item) => item.calculResult === 'selfAmount'))
const totalAmountList = computed(() => formulaList.value.filter((item) => item.calculResult === 'totalAmount'))
const taxAmountList = computed(() => formulaList.value.filter((item) => item.calculResult === 'taxAmount'))
const containTaxTotalAmountList = computed(() =>
  formulaList.value.filter((item) => item.calculResult === 'containTaxTotalAmount')
)

onMounted(() => {
  loadWaterElectricityList()
  loadFormulaList()
})

defineExpose({ validate })
</script>
