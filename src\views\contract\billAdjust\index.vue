<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd" v-auth="'biz.contractmanage:ct_biz_receive_amount_adjust:add'">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'biz.contractmanage:ct_biz_receive_amount_adjust:importExcel'">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button
          :loading="exportLoading"
          @click="handleExport"
          v-auth="'biz.contractmanage:ct_biz_receive_amount_adjust:exportXls'"
        >
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button
          v-show="selectedRowKeys.length"
          @click="handleRemove(false)"
          v-auth="'biz.contractmanage:ct_biz_receive_amount_adjust:deleteBatch'"
        >
          批量删除
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.number"
          placeholder="搜索单据编号"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span
            class="primary-btn"
            @click="handleView(record)"
            v-auth="'biz.contractmanage:ct_biz_receive_amount_adjust:view'"
          >
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div class="primary-btn" @click="handleAudit(record, true)">审核(临时功能)</div>
                </a-menu-item>
                <a-menu-item><div class="primary-btn" @click="handleViewContract(record)">查看合同</div></a-menu-item>
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div class="primary-btn" @click="handleWithdraw(record)">撤回</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleRemove(record)">删除</div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div class="primary-btn" @click="handleAudit(record, false)">反审核</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail
      ref="detailRef"
      @edit="handleEdit"
      @withdraw="handleWithdraw"
      @viewContract="handleViewContract"
      @audit="handleAudit"
      @remove="handleRemove"
    ></detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('账单调整导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <contract-detail ref="contractDetailRef" readonly></contract-detail>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit, withdraw } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag } from '@/utils/render'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import { hasPermission } from '@/utils/permission'

const params = reactive({
  column: 'number',
  order: 'desc',
  id: undefined,
  number: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  contract: undefined,
  signDate: undefined,
  customer: undefined,
  contractType: undefined,
  operator: undefined,
  operatorDepart: undefined,
  startDate: undefined,
  expireDate: undefined,
  leaseUnit: undefined,
  status: undefined,
  actualDealAmount: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  attachmentIds: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined,
  ctrlUnit: undefined
})
const searchList = [
  { label: '客户名称', name: 'customer', type: 'customer-select' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '业务日期', name: 'bizDate', type: 'date' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 120 },
  { title: '合同编号', dataIndex: 'contract_dictText', width: 100 },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '减免款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '业务日期', dataIndex: 'bizDate', width: 130 },
  { title: '创建时间', dataIndex: 'createTime', width: 140 },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_receive_amount_adjust:edit')) return
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

const contractDetailRef = ref()
const handleViewContract = (data) => {
  if (!hasPermission('biz.contractmanage:ct_con_lease:view')) return
  contractDetailRef.value.open(data.contract)
}
const handleWithdraw = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_receive_amount_adjust:edit')) return
  Modal.confirm({
    title: '是否确认撤回该合同账单调整申请？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await withdraw({ id: data.id })
      message.success(msg)
      onTableChange(pagination.value)
      if (detailRef.value && detailRef.value.visible) {
        detailRef.value.loadData(data.id)
      }
    }
  })
}

const handleAudit = async (data, result) => {
  if (result && !hasPermission('biz.contractmanage:ct_biz_receive_amount_adjust:audit')) return
  if (!result && !hasPermission('biz.contractmanage:ct_biz_receive_amount_adjust:unAudit')) return
  const { message: msg } = await (result ? audit({ id: data.id }) : unAudit({ id: data.id }))
  message.success(msg)
  onTableChange(pagination.value)
  if (detailRef.value && detailRef.value.visible) {
    detailRef.value.loadData(data.id)
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  if (!hasPermission('biz.contractmanage:ct_biz_receive_amount_adjust:delete')) return
  Modal.confirm({
    title: '确认删除合同账单调整申请？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success(msg)
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
      if (detailRef.value && detailRef.value.visible) {
        detailRef.value.handleClose()
      }
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('账单调整数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
