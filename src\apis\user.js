import request from './http'

export const login = (data) => {
  return request({
    method: 'POST',
    url: '/sys/login',
    data
  })
}

export const queryPermissions = () => {
  return request({
    method: 'get',
    url: `/sys/permission/getUserPermissionByToken?_t=${Date.now()}`
  })
}

// 获取当前用户所属公司之下的部门列表，以及用户当前部门
export const getCurrentDeparts = () => {
  return request({
    method: 'get',
    url: '/sys/user/getCurrentUserDeparts'
  })
}

// 获取当前用户所属公司列表
export const getCurrentUserCompanies = (params) => {
  return request({
    method: 'get',
    url: '/sys/user/getCurrentUserCompanies',
    params
  })
}

export const getCaptchaImg = (checkKey) => {
  return request({
    method: 'get',
    url: `/sys/randomImage/${checkKey}`
  })
}

export const logout = () => {
  return request({
    method: 'get',
    url: `/sys/logout?t=${Date.now()}`
  })
}

export const updatePassword = (data) => {
  return request({
    method: 'put',
    url: '/sys/user/updatePassword',
    data
  })
}
