import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/stampTaxJTBill/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/stampTaxJTBill/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/edit',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/unAudit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/taxaccrual/stampTaxJTBill/deleteBatch',
    params
  })
}

// 印花税计提单-生成计提单
export const generateJT = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/stampTaxJTBill/beginJT',
    data
  })
}

// 印花税计提单分录主表ID查询
export const queryStampUseTaxJTBill = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/stampTaxJTBill/queryStampTaxJTBillEntryByMainId',
    params
  })
}
