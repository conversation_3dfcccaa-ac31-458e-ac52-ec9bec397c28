<template>
  <a-modal
    v-model:open="visible"
    :title="`数据权限配置-${form.roleName}-${form.permissionName}`"
    width="800px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="false"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ y: '50vh' }"
    ></a-table>
  </a-modal>
</template>

<script setup>
import useTableSelection from '@/hooks/useTableSelection'
import { queryDataRule, saveDataRule } from '../apis'
import { message } from 'ant-design-vue'

const visible = ref(false)

const open = (data) => {
  form.roleId = data.roleId
  form.roleName = data.roleName
  form.permissionId = data.value
  form.permissionName = data.slotTitle
  visible.value = true
  loadList()
}

const list = ref([])
const tableLoading = ref(false)
const loadList = async () => {
  tableLoading.value = true
  const { result } = await queryDataRule({ functionId: form.permissionId, roleId: form.roleId })
  list.value = result.datarule
  tableLoading.value = false
}

const columns = [
  { title: '规则名称', dataIndex: 'ruleName' },
  { title: '规则字段', dataIndex: 'ruleColumn' },
  { title: '规则值', dataIndex: 'ruleValue' }
]

const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

const form = reactive({
  dataRuleIds: '',
  permissionId: '',
  permissionName: '',
  roleId: '',
  roleName: ''
})

const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    await saveDataRule({
      dataRuleIds: selectedRowKeys.value.join(','),
      permissionId: form.permissionId,
      roleId: form.roleId
    })
    message.success('保存成功')
    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  list.value = []
  selectedRowKeys.value = []
  visible.value = false
}

defineExpose({ open })
</script>
