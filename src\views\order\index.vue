<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button
          v-auth="'biz.tripartsettle:ct_biz_order_bill:add'"
          class="mb-[10px]"
          type="primary"
          @click="handleAdd"
        >
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button v-auth="'biz.tripartsettle:ct_biz_order_bill:importExcel'" class="mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'biz.tripartsettle:ct_biz_order_bill:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleCreateReceiveBill">生成应收单</div>
              </a-menu-item>
              <a-menu-item>
                <div
                  v-auth="'biz.tripartsettle:ct_biz_order_bill:deleteBatch'"
                  class="primary-btn"
                  @click="handleRemove(false)"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="">
          <s-input
            v-model="search.number"
            placeholder="搜索订单编号"
            class="ml-[10px] !w-[280px]"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <a-form-item>
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 1500 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span v-auth="'biz.tripartsettle:ct_biz_order_bill:view'" class="primary-btn" @click="rowView(record)">
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <!-- 审核中才有审核操作（临时） -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_order_bill:audit'"
                    class="primary-btn"
                    @click="handleVerify(record)"
                  >
                    审核通过(临时)
                  </div>
                </a-menu-item>
                <!-- 审核中才有撤回操作 -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_order_bill:edit'"
                    class="primary-btn"
                    @click="handleBack(record)"
                  >
                    撤回
                  </div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_order_bill:unAudit'"
                    class="primary-btn"
                    @click="rowReverse(record)"
                  >
                    反审核
                  </div>
                </a-menu-item>
                <a-menu-item v-if="record.status === 'AUDITOK' && !record.generatedBill">
                  <div class="primary-btn" @click="rowCreateReceiveBill(record)">生成应收单</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="handleViewReceiveBill(record)">查看应收单</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div v-auth="'biz.tripartsettle:ct_biz_order_bill:edit'" class="primary-btn" @click="rowEdit(record)">
                    编辑
                  </div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_order_bill:delete'"
                    class="primary-btn"
                    @click="handleRemove(record)"
                  >
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <detail ref="detailRef" @load-data="onTableChange"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入订单"
      :download-fn="() => exportExcel('订单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import usePageTable from '@/hooks/usePageTable'
import { renderDictTag, renderMoney } from '@/utils/render'
import AddEdit from './components/AddEdit.vue'
import Detail from './components/Detail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import { getPage, audit, createReceiveBill, back, unAudit, deleteBatch, exportExcel, importExcel } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
})
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
const search = ref({
  column: 'number',
  order: 'desc',
  number: ''
})
const searchFilter = ref({})
const searchList = reactive([
  { label: '下单时间', name: 'manageCompany', type: 'date', placeholder: '请选择下单时间' },
  {
    label: '客户',
    name: 'customer',
    type: 'customerSelect',
    placeholder: '请选择客户'
  },
  { label: '计费说明', name: 'remark', type: 'input', placeholder: '请输入计费说明' },
  { label: '订单金额', name: 'amount', type: 'number', placeholder: '请输入订单金额' },
  { label: '定金', name: 'depositAmount', type: 'number', placeholder: '请输入定金' },
  {
    label: '状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  }
])

const defaultColumns = [
  { title: '订单编号', dataIndex: 'number', width: 180, fixed: true },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '下单时间', dataIndex: 'bizDate' },
  { title: '客户', dataIndex: 'customer_dictText', ellipsis: true },
  { title: '计费说明', dataIndex: 'remark', width: 350, ellipsis: true },
  { title: '订单金额', dataIndex: 'amount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '定金', dataIndex: 'depositAmount', customRender: ({ text }) => renderMoney(text, 2) },

  { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)
const addEditRef = ref()
// 新增
const handleAdd = () => {
  addEditRef?.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row)
}
// 审核操作（临时）
const handleVerify = (row) => {
  Modal.confirm({
    title: '确认审核通过？',
    content: '',
    async onOk() {
      const data = await audit({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 撤回
const handleBack = (row) => {
  // if (!hasPermission('biz.funds:ct_fun_refund_req_bill:back')) return
  Modal.confirm({
    title: '确认撤回该订单？',
    content: '',
    async onOk() {
      const data = await back({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 反审核
const rowReverse = (row) => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await unAudit({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前资产处置？' : '确认批量删除选中资产处置？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

// 生成应收单 批量
const generateLoading = ref(false)
const handleCreateReceiveBill = async () => {
  if (generateLoading.value) return
  const list = selectedRows.value.filter((item) => item.status === 'AUDITOK')
  if (list.length === 0) {
    Modal.info({
      title: '系统提示',
      content: '只有审核通过，并且还未生成应收单的订单，才可以生成应收单，您本次选择的订单，不符合生成应收单的条件。',
      centered: true
    })
    return
  }
  try {
    generateLoading.value = true
    const data = await createReceiveBill(list.map((item) => item.id).join(','))
    message.success(data.message)
  } finally {
    generateLoading.value = false
  }
}
// 单个 生成应收单
const rowCreateReceiveBill = async (row) => {
  if (row.loading) return
  try {
    row.loading = true
    const data = await createReceiveBill(row.id)
    message.success(data.message)
    onTableChange(pagination.value)
  } finally {
    row.loading = false
  }
}

// 查看应收单
const router = useRouter()
const handleViewReceiveBill = (data) => {
  sessionStorage.setItem('sourceBillIdFromWaterShareBill', data.id)
  router.push({ path: '/statement/receiveCertificate' })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('订单数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
