<template>
  <a-modal
    v-model:open="visible"
    title="批量更新片区管理员"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    width="500px"
    class="common-modal"
    :mask-closable="false"
  >
    <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="片区管理员" name="areaManager" :rules="[{ required: true, message: '请选择片区管理员' }]">
        <a-form-item-rest>
          <f7-select v-model="formData.areaManager" placeholder="请选择片区管理员" f7-type="user" />
        </a-form-item-rest>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { batchChangeAreaManager } from '../apis/leaseUnit'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const selectedRecords = ref([])

const formData = reactive({
  areaManager: undefined
})

/**
 * 打开弹窗
 */
const open = (records = []) => {
  selectedRecords.value = records
  formData.areaManager = undefined
  visible.value = true
}

/**
 * 取消操作
 */
const handleCancel = () => {
  emits('refresh')
  visible.value = false
  formData.areaManager = undefined
  selectedRecords.value = []
}

/**
 * 提交批量更新片区管理员
 */
const handleSubmit = async () => {
  if (!formData.areaManager) {
    message.warning('请选择片区管理员')
    return
  }

  if (!selectedRecords.value || selectedRecords.value.length === 0) {
    message.warning('请选择要更新的租赁单元')
    return
  }

  loading.value = true
  try {
    const params = {
      newAreaManager: formData.areaManager,
      leaseUnitList: selectedRecords.value
    }

    await batchChangeAreaManager(params)
    message.success('批量更新片区管理员成功')
  } finally {
    loading.value = false
  }
  handleCancel()
}

defineExpose({
  open
})
</script>
