<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="流程单据类型详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleEdit">编辑</span>
        <a-dropdown>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">流程单据类型</h2>
        <status-tag :dict-value="detail.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.updateBy_dictText }} 提交于{{ detail.updateTime }}</span>
      </div>
      <h4 class="text-[16px] font-bold mb-[12px]">基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">名称: {{ detail.name }}</span>
        <span class="w-[50%]">类名key: {{ detail.classNameKey }}</span>
        <span class="w-[50%]">单据状态: {{ detail.status_dictText }}</span>
        <span class="w-[50%]">完整类名: {{ detail.className }}</span>
        <span class="w-full">审核详情页面显示路径: {{ detail.vueUi }}</span>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, deleteBatch } from '../apis.js'
import { Modal, message } from 'ant-design-vue'

const emit = defineEmits(['edit', 'audit', 'refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const loading = ref(false)
const detail = reactive({})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  loading.value = false
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该流程单据类型？',
    content: '',
    centered: true,
    onOk: async () => {
      const { message: msg } = await deleteBatch({ ids: detail.id })
      message.success(msg)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
