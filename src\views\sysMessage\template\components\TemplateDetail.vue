<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" width="1072px" title="消息模板详情">
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          class="text-primary cursor-pointer mr-[16px]"
          @click="handleEdit"
          v-auth="'message:sys_sms_template:edit'"
        >
          编辑
        </span>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">{{ detailData.templateName }}</h2>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>{{ detailData.templateCode || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span class="">{{ detailData.createBy_dictText || '-' }} 提交于 {{ detailData.createTime || '-' }}</span>
      </div>
      <div id="basic" class="mb-[40px]">
        <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">基础信息</h2>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">模板类型：{{ detailData.templateType_dictText }}</span>
          <span class="w-[50%]">应用状态：{{ detailData.useStatus === '0' ? '禁用' : '启用' }}</span>
          <span class="w-[50%]" v-if="detailData.mpTemplateId">
            公众号消息模板ID：{{ detailData.mpTemplateId || '-' }}
          </span>
          <span class="w-[50%]" v-if="detailData.mpTemplateId">微信小程序路径：{{ detailData.maPath || '-' }}</span>
          <span class="w-[10%]" v-if="detailData.mpTemplateId">参数映射：</span>
          <div class="w-[90%] text-left" v-if="detailData.mpTemplateId">
            <div v-if="parsedMpTemplateMap && parsedMpTemplateMap.length" class="flex flex-col gap-y-[8px]">
              <div v-for="(param, index) in parsedMpTemplateMap" :key="index" class="flex items-center">
                <span>{{ param.key }}={{ param.value }}</span>
              </div>
            </div>
            <div v-else>无参数映射</div>
          </div>
        </div>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">模版内容</h2>
      <a-typography-paragraph>
        <div class="p-[20px] bg-[#F5F5F5] rounded-[8px]" v-html="detailData.templateContent"></div>
      </a-typography-paragraph>
    </a-spin>
  </a-drawer>
  <edit-template ref="editTemplateRef" @refresh="loadDetail(detailData.id)" />
</template>

<script setup>
import { getTemplateById } from '../apis'
import EditTemplate from './EditTemplate.vue'

const editTemplateRef = ref()

const visible = ref(false)
const loading = ref(false)
const detailData = ref({})

/**
 * 解析参数映射JSON字符串为数组
 */
const parsedMpTemplateMap = computed(() => {
  if (!detailData.value.mpTemplateMap) return []

  try {
    if (typeof detailData.value.mpTemplateMap === 'string') {
      return JSON.parse(detailData.value.mpTemplateMap)
    }
    return Array.isArray(detailData.value.mpTemplateMap) ? detailData.value.mpTemplateMap : []
  } catch {
    return []
  }
})

/**
 * 打开详情抽屉
 */
const open = async (id) => {
  visible.value = true
  await loadDetail(id)
}

/**
 * 编辑当前模板
 */
const handleEdit = () => {
  editTemplateRef.value.open(detailData.value)
}

/**
 * 加载模板详情
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const response = await getTemplateById({ id })
    const data = response.result || response.data || response
    detailData.value = data
  } finally {
    loading.value = false
  }
}

defineExpose({ open })
</script>
