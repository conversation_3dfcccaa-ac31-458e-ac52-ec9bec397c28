import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: 'flowable/instance/processList',
    params
  })
}

// 流程任务管理-取消申请
export const stopProcess = (data) => {
  return request({
    method: 'post',
    url: 'flowable/task/stopProcess',
    data
  })
}

// 任务流转记录
export function flowRecord(query) {
  return request({
    url: '/flowable/task/flowRecord',
    method: 'get',
    params: query
  })
}

// 获取流程变量
export function processVariables(taskId) {
  return request({
    url: `/flowable/task/processVariables/${taskId}`,
    method: 'get'
  })
}

// 可跳转任务列表
export function jumpList(data) {
  return request({
    url: '/flowable/task/findJumpTaskListByDataId',
    method: 'post',
    data
  })
}

// 跳转任务
export function jumpTask(data) {
  return request({
    url: '/flowable/task/taskJumpByDataId',
    method: 'post',
    data
  })
}
