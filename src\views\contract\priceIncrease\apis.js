import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/priceIncreaseWay/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/priceIncreaseWay/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/priceIncreaseWay/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/priceIncreaseWay/importExcel',
    data
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/priceIncreaseWay/updateEnableDisableStatus',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/priceIncreaseWay/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/priceIncreaseWay/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/priceIncreaseWay/deleteBatch',
    params
  })
}

// 价格递增方式分录主表ID查询
export const wayList = (params) => {
  return request({
    method: 'get',
    url: '/bas/priceIncreaseWay/queryPriceIncreaseWayEntryByMainId',
    params
  })
}
