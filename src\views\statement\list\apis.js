import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/detailBill/list',
    params
  })
}

// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/biz/detailBill/queryById?id=${id}`
  })
}
// 明细账单分录-通主表ID查询
export const queryDetailBillEntryByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/detailBill/queryDetailBillEntryByMainId?id=${id}`
  })
}
// 退款申请单-通主表ID查询
export const queryRefundReqBillByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/detailBill/queryRefundReqBillByMainId?id=${id}`
  })
}
// 账单金额调整单-通主表ID查询
export const queryReceiveAmountAdjustByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/detailBill/queryReceiveAmountAdjustByMainId?id=${id}`
  })
}

// 租赁应收单-查询明细账单生成的应收单Id
export const getIdsByDetail = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/receiveBill/getIdsByDetail?id=${id}`
  })
}
// 反审核
export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/detailBill/unAudit',
    data
  })
}
// 审核
export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/detailBill/audit',
    data
  })
}
// 撤回
export const back = (data) => {
  return request({
    method: 'post',
    url: '/biz/detailBill/back',
    data
  })
}
// 明细账单-生成应收单
export const createReceiveBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/detailBill/createReceiveBill',
    data
  })
}
// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/receiveBill/delete?id=${id}`
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/tripartsettle/receiveBill/importExcel', data, controller)
}
