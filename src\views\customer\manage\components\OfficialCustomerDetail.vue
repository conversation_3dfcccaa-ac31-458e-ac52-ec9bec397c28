<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="客户详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra v-if="!readonly">
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          class="primary-btn"
          @click="handleEdit"
          v-auth="'bas:ct_bas_customer:edit'"
          v-if="detailData.status !== 'ENABLE'"
        >
          编辑
        </span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="delete">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">客户详情: {{ detailData.name }}</h2>
        <status-tag
          :dict-value="detailData.customerStatus"
          dict-code="CT_BASE_ENUM_Customer_CustomerStatus"
        ></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #basic>
          <div class="flex flex-col gap-y-[40px]">
            <div>
              <div class="flex flex-wrap gap-y-[12px] text-secondary">
                <span class="w-[50%]">客户名称：{{ detailData.name || '-' }}</span>
                <span class="w-[50%]">
                  是否集团内公司：{{ detailData.isInternalCompany ? '是' : '否' }}
                  <span v-if="detailData.isInternalCompany">（{{ detailData.internalCompany_dictText }}）</span>
                </span>
                <span class="w-[50%]">管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
                <span class="w-[50%]">客户来源：{{ detailData.customerSource_dictText || '-' }}</span>
                <span class="w-[50%]">客户类型：{{ detailData.customerType_dictText || '-' }}</span>
                <span class="w-[50%]">营业执照：{{ busiLicenceFileName || '-' }}</span>
                <span class="w-[50%]">营业执照号：{{ detailData.busiLicenceNum || '-' }}</span>
                <span class="w-[50%]">法人/自然人：{{ detailData.legalPerson || '-' }}</span>
                <span class="w-[50%]">法人身份证：{{ detailData.legalPerson || '-' }}</span>
                <span class="w-[50%]">注册地址：{{ detailData.registeredAddress || '-' }}</span>
                <span class="w-[50%]">履约情况：{{ detailData.performance_dictText || '-' }}</span>
                <span class="w-[50%]">安全等级：{{ detailData.safeRate_dictText || '-' }}</span>
              </div>
            </div>

            <!-- 客户需求 -->
            <div>
              <h4 class="text-[16px] font-bold mb-[12px]">客户需求</h4>
              <div class="flex flex-wrap gap-y-[12px] text-secondary">
                <span class="w-[50%]">维护日期：{{ detailData.maintainDate || '-' }}</span>
                <span class="w-[50%]">维护人员：{{ detailData.maintainPerson_dictText || '-' }}</span>
                <span class="w-[100%] break-words whitespace-pre-wrap">
                  初步需求：{{ detailData.initRequire || '-' }}
                </span>
              </div>
            </div>

            <!-- 联系信息 -->
            <div>
              <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">联系信息</h4>
              <div class="flex flex-wrap gap-y-[12px] text-secondary">
                <span class="w-[50%]">联系人：{{ detailData.linkman || '-' }}</span>
                <span class="w-[50%]">法定送达地址：{{ detailData.linkDetailAddress || '-' }}</span>
                <span class="w-[50%]">联系电话：{{ detailData.linkmanPhone || '-' }}</span>
                <span class="w-[50%]">推送手机：{{ detailData.pushMobile || '-' }}</span>
                <span class="w-[50%]">邮箱：{{ detailData.email || '-' }}</span>
              </div>
            </div>
          </div>
        </template>

        <template #financial>
          <div>
            <div class="flex flex-wrap gap-y-[12px] text-secondary">
              <span class="w-[50%]">开户银行：{{ detailData.depositBank || '-' }}</span>
              <span class="w-[50%]">开户行账号：{{ detailData.depositBankAccount || '-' }}</span>
              <span class="w-[50%]">发票名称：{{ detailData.invoiceName || '-' }}</span>
              <span class="w-[50%]">发票类型：{{ detailData.invoiceType_dictText || '-' }}</span>
              <span class="w-[50%]">税率：{{ renderMoney(detailData.taxRate, 4, '%') }}</span>
            </div>
          </div>
        </template>

        <template #followRecord-title>
          <div class="flex items-center justify-between mb-[12px]">
            <h4 class="text-[16px] font-bold">跟进记录</h4>
            <a-button
              type="primary"
              @click="handleAddFollowRecord"
              v-if="!readonly"
              v-auth="'bas:ct_bas_follow_record:add'"
            >
              添加记录
            </a-button>
          </div>
        </template>
        <template #followRecord>
          <a-table
            v-if="list.length > 0"
            :data-source="list"
            :columns="followRecordColumns"
            :loading="tableLoading"
            :pagination="pagination"
            row-key="id"
            :scroll="{ x: 1200 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'followContent'">
                <span>{{ record.layoutPreference }}/{{ record.supportFacilityRequire }}/{{ record.otherContent }}</span>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <span
                  class="primary-btn"
                  @click="handleEditFollowRecord(record)"
                  v-auth="'bas:ct_bas_follow_record:edit'"
                >
                  编辑
                </span>
                <span
                  class="primary-btn"
                  @click="handleDeleteFollowRecord(record)"
                  v-auth="'bas:ct_bas_follow_record:delete'"
                >
                  删除
                </span>
              </template>
            </template>
          </a-table>
          <div v-else class="flex flex-col items-center py-[40px]">
            <img src="@/assets/imgs/no-data.png" class="w-[80px] h-[80px]" />
            <span class="text-tertiary mt-[8px]">暂无数据</span>
          </div>
        </template>

        <!-- 合同信息 -->
        <template #contract>
          <a-table
            :data-source="contractList"
            :columns="contractColumns"
            :loading="contractTableLoading"
            :pagination="contractPagination"
            row-key="id"
            :scroll="{ x: 1200 }"
            @change="onContractTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="handleContractDetail(record)">查看</span>
              </template>
            </template>
          </a-table>
        </template>

        <!-- 应收明细 -->
        <template #receiveBill>
          <div class="mb-[16px] flex items-center">
            <s-input
              v-model="receiveBillSearch.number"
              placeholder="搜索单据编号"
              class="!w-[280px] mr-[16px]"
              @input="handleReceiveBillInput"
            ></s-input>
            <filter-more
              :params="receiveBillSearchFilter"
              :search-list="receiveBillSearchList"
              width="320px"
              label-width="100px"
              @query="onReceiveBillTableChange({ pageNo: 1, pageSize: receiveBillPagination.pageSize })"
            ></filter-more>
          </div>
          <a-table
            :data-source="receiveBillList"
            :columns="receiveBillColumns"
            :loading="receiveBillTableLoading"
            :pagination="receiveBillPagination"
            row-key="id"
            :scroll="{ x: 1200 }"
            @change="onReceiveBillTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="handleReceiveBillDetail(record)">查看</span>
              </template>
            </template>
          </a-table>
        </template>

        <!-- 收付记录 -->
        <template #receivePayRecord>
          <div class="mb-[16px] flex items-center">
            <s-input
              v-model="receivePayRecordSearch.number"
              placeholder="搜索单据编号"
              class="!w-[280px] mr-[16px]"
              @input="handleReceivePayRecordInput"
            ></s-input>
            <filter-more
              :params="receivePayRecordSearchFilter"
              :search-list="receivePayRecordSearchList"
              width="320px"
              label-width="100px"
              @query="onReceivePayRecordTableChange({ pageNo: 1, pageSize: receivePayRecordPagination.pageSize })"
            ></filter-more>
          </div>
          <a-table
            :data-source="receivePayRecordList"
            :columns="receivePayRecordColumns"
            :loading="receivePayRecordTableLoading"
            :pagination="receivePayRecordPagination"
            row-key="id"
            :scroll="{ x: 1200 }"
            @change="onReceivePayRecordTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="handleReceivePayRecordDetail(record)">查看</span>
              </template>
            </template>
          </a-table>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <edit-customer ref="editDrawerRef" @refresh="refreshData" />

  <edit-follow-record ref="editFollowRecordModalRef" @refresh="onTableChange" />

  <contract-detail ref="contractDetailRef" />

  <receive-bill-detail ref="receiveBillDetailRef" />

  <receive-pay-record-detail ref="receivePayRecordDetailRef" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import { hasPermission } from '@/utils/permission'
import { renderMoney, renderDictTag } from '@/utils/render'
import { getAttachmentByIds } from '@/apis/common'
import { getFollowRecordList, deleteFollowRecordById } from '@/views/customer/followRecord/apis'
import { page as getContractPage } from '@/views/contract/management/apis'
import { getPage as getReceiveBillPage } from '@/views/statement/receiveCertificate/apis'
import { getPage as getReceivePayRecordPage } from '@/views/receivePayRecords/apis'
import { queryCustomerById, deleteCustomer } from '../apis'
import EditFollowRecord from '@/views/customer/followRecord/components/EditFollowRecord.vue'
import EditCustomer from './EditOfficialCustomer.vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import ReceiveBillDetail from '@/views/statement/receiveCertificate/components/Detail.vue'
import ReceivePayRecordDetail from '@/views/receivePayRecords/components/Detail.vue'

defineProps({
  readonly: { type: Boolean, default: false }
})

const emits = defineEmits(['refresh'])

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getFollowRecordList)

const {
  list: contractList,
  pagination: contractPagination,
  tableLoading: contractTableLoading,
  onTableFetch: onContractTableFetch
} = usePageTable(getContractPage)

const {
  list: receiveBillList,
  pagination: receiveBillPagination,
  tableLoading: receiveBillTableLoading,
  onTableFetch: onReceiveBillTableFetch
} = usePageTable(getReceiveBillPage)

const {
  list: receivePayRecordList,
  pagination: receivePayRecordPagination,
  tableLoading: receivePayRecordTableLoading,
  onTableFetch: onReceivePayRecordTableFetch
} = usePageTable(getReceivePayRecordPage)

const visible = ref(false)
const loading = ref(false)
const editFollowRecordModalRef = ref()
const editDrawerRef = ref()
const contractDetailRef = ref()
const receiveBillDetailRef = ref()
const receivePayRecordDetailRef = ref()
const detailData = ref({})
const busiLicenceFileName = ref('')

const searchParams = reactive({
  followCustomer: undefined
})

const contractSearchParams = reactive({
  customer: undefined,
  column: 'number',
  order: 'desc'
})

const receiveBillSearch = reactive({
  column: 'number',
  order: 'desc',
  number: undefined,
  customer: undefined
})

const receiveBillSearchFilter = reactive({})

const receivePayRecordSearch = reactive({
  column: 'number',
  order: 'desc',
  number: undefined,
  customer: undefined
})

const receivePayRecordSearchFilter = reactive({})

const tabList = [
  { title: '基础信息', name: 'basic' },
  { title: '财务信息', name: 'financial' },
  { title: '跟进记录', name: 'followRecord' },
  { title: '合同信息', name: 'contract' },
  { title: '应收明细', name: 'receiveBill' },
  { title: '收付记录', name: 'receivePayRecord' }
]

const receiveBillSearchList = reactive([
  {
    label: '单据状态',
    name: 'status',
    type: 'dict-select',
    placeholder: '请选择单据状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择物业管理公司' },
  {
    label: '租金归集公司',
    name: 'collectionCompany',
    type: 'company-select',
    companyType: 'all',
    placeholder: '请选择租金归集公司'
  },
  { label: '应收日期', name: 'receiveDate', type: 'date', placeholder: '请选择应收日期' },
  { label: '金额', name: 'amount', type: 'input', placeholder: '请输入金额' },
  { label: '含税金额', name: 'containTaxAmount', type: 'input', placeholder: '请输入含税金额' },
  {
    label: '单据来源',
    name: 'billSource',
    type: 'dict-select',
    placeholder: '请选择单据来源',
    code: 'CT_BASE_ENUM_ReceiveBill_BillSource'
  },
  { label: '经办人', name: 'operator', type: 'user-select', placeholder: '请选择经办人' },
  { label: '业务部门', name: 'operatorDepart', type: 'depart-select', placeholder: '请选择经办部门' }
])

const receivePayRecordSearchList = reactive([
  {
    label: '收付款公司',
    name: 'manageCompany',
    type: 'company-select',
    companyType: 'all',
    placeholder: '请选择收付款公司'
  },
  { label: '收付金额', name: 'actualReceiveAmt', type: 'input', placeholder: '请输入收付金额' },
  { label: '手续费', name: 'serviceCharge', type: 'input', placeholder: '请输入手续费' },
  { label: '合计金额', name: 'sumAmt', type: 'input', placeholder: '请输入合计金额' },
  {
    label: '数据状态',
    name: 'status',
    type: 'dict-select',
    placeholder: '请选择数据状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  {
    label: '单据来源',
    name: 'billSource',
    type: 'dict-select',
    placeholder: '请选择单据来源',
    code: 'CT_BASE_ENUM_PayExplainBook_BillSource'
  },
  { label: '经办人', name: 'operator', type: 'user-select', placeholder: '请选择经办人' },
  { label: '收款日期', name: 'receiveDate', type: 'date', placeholder: '请选择收款日期' }
])

const followRecordColumns = [
  { title: '登记时间', dataIndex: 'followTime', width: 200, fixed: 'left' },
  { title: '跟进客户', dataIndex: 'followCustomer_dictText', width: 120, ellipsis: true },
  { title: '跟进人', dataIndex: 'followPerson_dictText', width: 120 },
  { title: '跟进方式', dataIndex: 'followMethod_dictText', width: 120 },
  { title: '参观租赁单元', dataIndex: 'visitLeaseUnit_dictText', width: 160, ellipsis: true },
  { title: '意向面积', dataIndex: 'intendArea', width: 120 },
  { title: '入住意向时间', dataIndex: 'moveIntendTime', width: 120 },
  { title: '布局偏好', dataIndex: 'layoutPreference', width: 160, ellipsis: true },
  { title: '配套设施需求', dataIndex: 'supportFacilityRequire', width: 160, ellipsis: true },
  { title: '其他内容', dataIndex: 'otherContent', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const contractColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '合同', dataIndex: 'contractNumber', width: 160, ellipsis: true },
  { title: '客户', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '签约日期', dataIndex: 'signDate', width: 120 },
  { title: '业务人员', dataIndex: 'operator_dictText', width: 120 },
  { title: '操作', dataIndex: 'action', width: 80, fixed: 'right' }
]

const receiveBillColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: true },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '客户', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '金额', dataIndex: 'amount', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '含税金额',
    dataIndex: 'containTaxAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '单据来源', dataIndex: 'billSource_dictText', width: 120 },
  { title: '经办人', dataIndex: 'operator_dictText', width: 120 },
  { title: '经办部门', dataIndex: 'operatorDepart_dictText', width: 120, ellipsis: true },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 80, fixed: 'right' }
]

const receivePayRecordColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: true, ellipsis: true },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '收付款公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  {
    title: '实收金额',
    dataIndex: 'actualReceiveAmt',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '手续费', dataIndex: 'serviceCharge', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '合计金额', dataIndex: 'sumAmt', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '核销情况', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '数据状态', dataIndex: 'status_dictText', width: 120 },
  { title: '单据来源', dataIndex: 'billSource_dictText', width: 120 },
  { title: '经办人', dataIndex: 'operator_dictText', width: 120 },
  { title: '收款日期', dataIndex: 'receiveDate', width: 120 },
  { title: '操作', dataIndex: 'action', width: 80, fixed: 'right' }
]

/**
 * 打开详情抽屉
 */
const open = (record) => {
  if (!record || !record.id) {
    message.error('缺少必要参数')
    return
  }

  visible.value = true
  loadDetail(record.id)
  searchParams.followCustomer = record.id

  contractSearchParams.customer = record.id
  receiveBillSearch.customer = record.id
  receivePayRecordSearch.customer = record.id

  onTableChange()
  onContractTableChange()
  onReceiveBillTableChange()
  onReceivePayRecordTableChange()
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
}

/**
 * 编辑客户信息
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 添加跟进记录
 */
const handleAddFollowRecord = () => {
  editFollowRecordModalRef.value.open({ followCustomer: detailData.value.id })
}

/**
 * 编辑跟进记录
 * @param {Object} record - 跟进记录数据
 */
const handleEditFollowRecord = (record) => {
  editFollowRecordModalRef.value.open(record)
}

/**
 * 删除跟进记录
 * @param {Object} record - 跟进记录数据
 */
const handleDeleteFollowRecord = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该跟进记录吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      await deleteFollowRecordById({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 查看合同详情
 * @param {Object} record - 合同记录
 */
const handleContractDetail = (record) => {
  contractDetailRef.value.open(record.id)
}

/**
 * 查看应收明细详情
 * @param {Object} record - 应收明细记录
 */
const handleReceiveBillDetail = (record) => {
  receiveBillDetailRef.value.open(record.id)
}

/**
 * 查看收付记录详情
 * @param {Object} record - 收付记录
 */
const handleReceivePayRecordDetail = (record) => {
  receivePayRecordDetailRef.value.open(record)
}

/**
 * 删除客户
 */
const handleDelete = () => {
  if (!hasPermission('bas:ct_bas_customer:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除客户"${detailData.value.name || detailData.value.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteCustomer({ id: detailData.value.id })
      message.success('删除成功')
      visible.value = false
      emits('refresh')
    }
  })
}

/**
 * 获取客户详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const res = await queryCustomerById({ id })
    detailData.value = res.result || {}
    getBusiLicenceFileName(detailData.value.busiLicence)
  } finally {
    loading.value = false
  }
}

/**
 * 刷新详情数据
 */
const refreshData = () => {
  loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 获取营业执照文件名
 */
const getBusiLicenceFileName = async (fileId) => {
  const attachmentData = await getAttachmentByIds(fileId)
  if (attachmentData && attachmentData.result && attachmentData.result.length > 0) {
    const fileInfo = attachmentData.result[0]
    busiLicenceFileName.value = `${fileInfo.fileName}.${fileInfo.fileType}`
  }
}

/**
 * 跟进记录表格变化处理
 */
const onTableChange = ({ pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo, pageSize, ...searchParams })
}

/**
 * 合同表格变化处理
 */
const onContractTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onContractTableFetch({ pageNo: current ?? pageNo, pageSize, ...contractSearchParams })
}

/**
 * 应收明细表格变化处理
 */
const onReceiveBillTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onReceiveBillTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...receiveBillSearch,
    ...receiveBillSearchFilter
  })
}

/**
 * 应收明细搜索输入防抖处理
 */
let receiveBillTimer
const handleReceiveBillInput = () => {
  clearTimeout(receiveBillTimer)
  receiveBillTimer = setTimeout(() => {
    onReceiveBillTableChange({ pageNo: 1, pageSize: receiveBillPagination.value.pageSize })
  }, 600)
}

/**
 * 收付记录表格变化处理
 */
const onReceivePayRecordTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onReceivePayRecordTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...receivePayRecordSearch,
    ...receivePayRecordSearchFilter
  })
}

/**
 * 收付记录搜索输入防抖处理
 */
let receivePayRecordTimer
const handleReceivePayRecordInput = () => {
  clearTimeout(receivePayRecordTimer)
  receivePayRecordTimer = setTimeout(() => {
    onReceivePayRecordTableChange({ pageNo: 1, pageSize: receivePayRecordPagination.value.pageSize })
  }, 600)
}

defineExpose({
  open
})
</script>
