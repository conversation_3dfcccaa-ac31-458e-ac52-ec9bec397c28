<template>
  <div>
    <div class="flex mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新增
      </a-button>
      <a-button @click="handleRemoveBatch" v-show="selectedRowKeys.length">
        <i class="a-icon-delete"></i>
        批量删除
      </a-button>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <s-input
        v-model="params.name"
        placeholder="搜索职务名称"
        class="ml-[40px] !w-[200px]"
        @input="handleInput"
      ></s-input>
      <company-select
        v-model="params.company"
        placeholder="搜索所属公司"
        type="all"
        width="200px"
        class="!mx-[16px]"
        @change="onCompanyChange"
      ></company-select>
      <depart-select
        v-model="params.depart"
        :company-id="params.company"
        width="200px"
        placeholder="搜索所属部门"
        @change="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></depart-select>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-popconfirm title="是否确认删除？" ok-text="确认" cancel-text="取消" @confirm="handleRemove(record)">
            <span class="primary-btn">删除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <edit-position ref="editPositionRef" @refresh="refreshData"></edit-position>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message, Modal } from 'ant-design-vue'
import { page, remove, removeBatch } from './apis'
import EditPosition from './components/EditPosition.vue'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  name: '',
  company: '',
  depart: ''
})

const columns = [
  { title: '序号', dataIndex: 'index', width: 60 },
  { title: '职务名称', dataIndex: 'name' },
  { title: '职务编码', dataIndex: 'code' },
  { title: '所属公司', dataIndex: 'company_dictText' },
  { title: '所属部门', dataIndex: 'depart_dictText' },
  { title: '操作', dataIndex: 'action', width: 200 }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const onCompanyChange = () => {
  params.depart = ''
  onTableChange({ pageNo: 1, pageSize: pagination.pageSize })
}

const editPositionRef = ref()
const handleAdd = () => {
  editPositionRef.value.open()
}
const handleEdit = (row) => {
  editPositionRef.value.open({
    id: row.id,
    name: row.name,
    code: row.code,
    company: row.company,
    depart: row.depart
  })
}

const refreshData = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const handleRemove = async (data) => {
  await remove({ id: data.id })
  message.success('删除成功')
  let pageNo = pagination.value.current
  if (pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

const handleRemoveBatch = () => {
  Modal.confirm({
    title: '确认删除选中职务？',
    content: '',
    centered: true,
    onOk: async () => {
      await removeBatch({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (pageNo > 1 && selectedRowKeys.value.length === list.value.length) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

onMounted(() => {
  onTableChange()
})
</script>
