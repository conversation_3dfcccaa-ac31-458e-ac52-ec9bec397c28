<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="拆合单详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          class="primary-btn"
          @click="handleEdit"
          v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)"
          v-auth="'biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:edit'"
        >
          编辑
        </span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="submit" v-if="['BACK', 'AUDITNO'].includes(detailData.status)">
                <div class="primary-btn" @click="handleSubmit">提交</div>
              </a-menu-item>
              <a-menu-item key="back" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleBack">撤回</div>
              </a-menu-item>
              <a-menu-item key="audit" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleAudit">审核</div>
              </a-menu-item>
              <a-menu-item key="unAudit" v-if="detailData.status === 'AUDITOK'">
                <div class="primary-btn" @click="handleUnAudit">反审核</div>
              </a-menu-item>
              <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(detailData.status)">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">拆合单详情</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        单据编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <!-- 基础信息 -->
      <div class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">基础信息</h4>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">物业管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
          <span class="w-[50%]">业务日期：{{ detailData.bizDate || '-' }}</span>
          <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
          <span class="w-[50%]">经办部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
          <span class="w-[50%]">拆合类型：{{ detailData.splitMergeType_dictText || '-' }}</span>
          <span class="w-[100%] break-words whitespace-pre-wrap">备注：{{ detailData.remark || '-' }}</span>
        </div>
      </div>
      <!-- 拆合单元信息 -->
      <div class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">拆合单元信息</h4>
        <div class="flex items-center overflow-hidden">
          <div class="transfer-panel">
            <div class="panel-header">
              <div class="font-medium text-base">源单元</div>
            </div>
            <div class="panel-content">
              <div v-if="!oriEntryList?.length" class="empty-state">
                <img src="@/assets/imgs/no-data.png" />
                <span class="text-tertiary">暂无数据</span>
              </div>
              <div v-else>
                <div v-for="item in oriEntryList" :key="item.id" class="unit-item">
                  <div class="unit-info">
                    <div class="unit-title">{{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})</div>
                    <div class="unit-details">
                      <div class="flex mt-1">
                        <span class="flex-1">
                          地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                          }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                        </span>
                        <span class="flex-1">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                      </div>
                      <div class="flex mt-1">
                        <span class="flex-1">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                        <span class="flex-1">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="unit-actions">
                    <span
                      class="icon-btn"
                      @click="handleViewDetail(item.leaseUnitObject)"
                      v-auth="'bas:ct_bas_lease_unit:view'"
                    >
                      <i class="a-icon-arrow-right"></i>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="transfer-arrow">
            <div class="arrow-icon">
              <i class="a-icon-arrow-right"></i>
            </div>
          </div>

          <div class="transfer-panel">
            <div class="panel-header">
              <div class="font-medium text-base">新单元</div>
            </div>
            <div class="panel-content">
              <div v-if="!destEntryList?.length" class="empty-state">
                <img src="@/assets/imgs/no-data.png" />
                <span class="text-tertiary">暂无数据</span>
              </div>
              <div v-else>
                <div v-for="item in destEntryList" :key="item.id" class="unit-item">
                  <div class="unit-info">
                    <div class="unit-title">{{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})</div>
                    <div class="unit-details">
                      <div class="flex mt-1">
                        <span class="flex-1">
                          地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                          }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                        </span>
                        <span class="flex-1">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                      </div>
                      <div class="flex mt-1">
                        <span class="flex-1">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                        <span class="flex-1">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="unit-actions">
                    <span
                      class="icon-btn"
                      @click="handleViewDetail(item.leaseUnitObject)"
                      v-auth="'bas:ct_bas_lease_unit:view'"
                    >
                      <i class="a-icon-arrow-right"></i>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-drawer>
  <lease-unit-detail ref="detailDrawerRef" readonly></lease-unit-detail>
  <edit-lease-unit-split-merge ref="editDrawerRef" @refresh="handleRefresh"></edit-lease-unit-split-merge>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import EditLeaseUnitSplitMerge from './EditLeaseUnitSplitMerge.vue'
import {
  getLeaseUnitSplitMergeBillById,
  queryLeaseUnitSplitMergeBillEntryOriByMainId,
  queryLeaseUnitSplitMergeBillEntryDestByMainId,
  deleteLeaseUnitSplitMergeBill,
  submitLeaseUnitSplitMergeBill,
  auditLeaseUnitSplitMergeBill,
  unAuditLeaseUnitSplitMergeBill,
  backLeaseUnitSplitMergeBill
} from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const detailDrawerRef = ref()
const editDrawerRef = ref()
const detailData = ref({})
const oriEntryList = ref([])
const destEntryList = ref([])
const currentId = ref('')

/**
 * 打开详情抽屉
 */
const open = async (record) => {
  if (!record || !record.id) {
    message.error('缺少必要参数')
    return
  }

  currentId.value = record.id
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 获取详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  const res = await getLeaseUnitSplitMergeBillById({ id })
  const oriRes = await queryLeaseUnitSplitMergeBillEntryOriByMainId({ id })
  const destRes = await queryLeaseUnitSplitMergeBillEntryDestByMainId({ id })
  if (res.success) {
    detailData.value = res.result || {}
    oriEntryList.value = oriRes.result || []
    destEntryList.value = destRes.result || []
  } else {
    message.error(res.message || '获取详情失败')
  }
  loading.value = false
}

/**
 * 查看源单元详情
 */
const handleViewDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 编辑拆合单
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 提交拆合单
 */
const handleSubmit = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:submit')) return
  Modal.confirm({
    title: '确认提交',
    content: '确定要提交该条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      if (!oriEntryList.value?.length || !destEntryList.value?.length) {
        message.error('源租赁单元或目标租赁单元信息不完整，无法提交！')
        return
      }

      // 获取源单元和目标单元数据
      const sourceUnits = oriEntryList.value.map((entry) => {
        return entry.leaseUnitObject || entry
      })
      const targetUnits = destEntryList.value.map((entry) => {
        return entry.leaseUnitObject || entry
      })

      // 验证租赁面积
      const sourceLeaseAreaSum = sourceUnits.reduce((sum, unit) => {
        return sum + (parseFloat(unit.leaseArea) || 0)
      }, 0)

      const targetLeaseAreaSum = targetUnits.reduce((sum, unit) => {
        return sum + (parseFloat(unit.leaseArea) || 0)
      }, 0)

      if (targetLeaseAreaSum < sourceLeaseAreaSum) {
        message.error(
          `目标租赁单元的租赁面积总和(${targetLeaseAreaSum}㎡)不能小于源租赁单元的租赁面积总和(${sourceLeaseAreaSum}㎡)`
        )
        return
      }

      const submitData = {
        ...detailData.value,
        leaseUnitSplitMergeBillEntryOriList: sourceUnits.map((unit) => ({
          leaseUnit: unit.id,
          leaseUnitObject: unit,
          houseOwnerObject: unit.houseOwnerObject,
          parent: detailData.value.id
        })),
        leaseUnitSplitMergeBillEntryDestList: targetUnits.map((unit) => ({
          leaseUnit: unit.id,
          leaseUnitObject: unit,
          houseOwnerObject: unit.houseOwnerObject,
          parent: detailData.value.id
        }))
      }

      await submitLeaseUnitSplitMergeBill(submitData)
      message.success('提交成功')
      await loadDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 撤回拆合单
 */
const handleBack = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回租赁单元拆合单"${detailData.value.number}"审核吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await backLeaseUnitSplitMergeBill({ id: detailData.value.id })
      message.success('撤回成功')
      await loadDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 反审核拆合单
 */
const handleUnAudit = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: `确定要对租赁单元拆合单"${detailData.value.number}"执行反审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await unAuditLeaseUnitSplitMergeBill({ id: detailData.value.id })
      message.success('反审核成功')
      await loadDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 审核拆合单
 */
const handleAudit = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: `确定要对租赁单元拆合单"${detailData.value.number}"执行审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await auditLeaseUnitSplitMergeBill({ id: detailData.value.id })
      message.success('审核成功')
      await loadDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 删除拆合单
 */
const handleDelete = () => {
  if (!hasPermission('biz.basicdatadeal:ct_bas_lease_unit_split_merge_bill:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnitSplitMergeBill({ id: detailData.value.id })
      message.success('删除成功')
      handleClose()
      emits('refresh')
    }
  })
}

/**
 * 处理刷新事件
 */
const handleRefresh = async () => {
  await loadDetail(currentId.value)
  emits('refresh')
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
  oriEntryList.value = []
  destEntryList.value = []
  currentId.value = ''
}

defineExpose({
  open
})
</script>

<style scoped lang="less">
.transfer-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 400px;
  background-color: white;
  border: 1px solid #e6e9f0;
  border-radius: 8px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f7f8fa;
  border-bottom: 1px solid #e6e9f0;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  min-height: 300px;
  padding: 0 16px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  min-height: 300px;
}

.unit-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.3s ease;
  padding: 16px 0;
  border-bottom: 1px solid #e6e9f0;
  &:last-child {
    border-bottom: none;
  }
}

.unit-info {
  flex: 1;
  overflow: hidden;
}

.unit-title {
  font-weight: 500;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unit-details {
  font-size: 12px;
  color: #495a7a;
}

.unit-actions {
  display: flex;
  align-items: center;
  width: 40px;
  flex-direction: row;
  justify-content: flex-end;
}

.transfer-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #eaf0fe;
  width: 32px;
  height: 32px;
  margin: 0 8px;
  border-radius: 4px;
}

.arrow-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 16px;
}
.icon-btn {
  color: #165dff;
  font-size: 20px;
  cursor: pointer;
}
</style>
