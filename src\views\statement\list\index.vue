<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button v-auth="'biz.contractmanage:ct_con_detail_bill:importExcel'" class="!mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'biz.contractmanage:ct_con_detail_bill:exportXls'"
          class="!mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleCreateReceiveBill(0)">生成应收单</div>
              </a-menu-item>
              <a-menu-item>
                <div class="primary-btn" @click="handleCreateReceiveBill(1)">应收冲销</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="!mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!mb-[10px] !ml-[40px]" label="业务状态">
          <dict-select
            class="!w-[280px]"
            v-model="search.bizStatus"
            placeholder="请选择业务状态"
            code="CT_BASE_ENUM_DetailBill_BizStatus"
          ></dict-select>
        </a-form-item>
        <a-form-item class="!mb-[10px]" label="">
          <s-input class="!w-[280px]" v-model:value="search.number" placeholder="搜索账单编号"></s-input>
        </a-form-item>
        <a-form-item class="!mb-[10px]">
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 4000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #expandedRowRender="{ record }">
        <a-table
          class="inner-table"
          :data-source="record.detailBillEntryList"
          :columns="moreColumns"
          :scroll="{ y: 400, x: 3000 }"
          :pagination="false"
        ></a-table>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowView(record)">详情</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <!-- 跳转到合同详情 -->
                  <div class="primary-btn" @click="rowViewContractDetail(record)">查看合同</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="rowViewRefund(record)">查看退款</div>
                </a-menu-item>
                <a-menu-item>
                  <div class="primary-btn" @click="rowLeaseReceivableInvoice(record)">查看应收单</div>
                </a-menu-item>
                <!-- 审核中才有审核操作（临时） -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.contractmanage:ct_con_detail_bill:audit'"
                    class="primary-btn"
                    @click="rowVerify(record)"
                  >
                    审核通过(临时)
                  </div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div
                    v-auth="'biz.contractmanage:ct_con_detail_bill:unAudit'"
                    class="primary-btn"
                    @click="rowReverse(record)"
                  >
                    反审核
                  </div>
                </a-menu-item>
                <!-- 审核中才有撤回操作 -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.contractmanage:ct_con_detail_bill:edit'"
                    class="primary-btn"
                    @click="rowBack(record)"
                  >
                    撤回
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <!-- 详情 -->
    <detail
      ref="detailRef"
      @load-data="onTableChange({ pageNo: pagination.current, pageSize: pagination.pageSize })"
    ></detail>
    <!--      @refresh="refreshFromDetail"
      @edit="handleEdit"
      @abort="handleTermination"
      @cancel-abort="handleCancelTermination"
      @change="handleChange"
      @renew="handleRenew"
      @withdraw="handleWithdraw" -->
    <!-- 合同详情 -->
    <contract-detail ref="contractDetailRef"></contract-detail>

    <!-- 退款单详情 -->
    <refund-detail ref="detailDrawerRef" />
    <!-- 应收单详情 -->
    <receivable-voucher-detail ref="receivableVoucherDetailRef"></receivable-voucher-detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入应收明细"
      :download-fn="() => exportExcel('应收明细数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import ReceivableVoucherDetail from '@/views/statement/receiveCertificate/components/Detail.vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import { renderDict, renderDictTag, renderBoolean, renderMoney } from '@/utils/render'
import Detail from './components/Detail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import {
  getPage,
  queryRefundReqBillByMainId,
  createReceiveBill,
  getIdsByDetail,
  unAudit,
  audit,
  back,
  exportExcel,
  importExcel
} from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  const contractId = sessionStorage.getItem('viewBillFromContract')
  if (contractId) {
    searchFilter.value.contract = contractId
    sessionStorage.removeItem('viewBillFromContract')
  }
  onTableChange()
})
const search = ref({
  column: 'number',
  order: 'desc',
  bizStatus: '',
  number: ''
})
const searchFilter = ref({})
const searchList = reactive([
  {
    label: '合同编号',
    name: 'contractNumber',
    type: 's-input',
    placeholder: '请输入合同编号'
  },
  { label: '物业管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择物业管理公司' },
  {
    label: '客户',
    name: 'customer',
    type: 'customerSelect',
    placeholder: '请选择客户'
  },
  {
    label: '业务员',
    name: 'operator',
    type: 'userSelect',
    placeholder: '请选择业务员'
  },
  { label: '业务部门', name: 'operatorDepart', type: 'departSelect', placeholder: '请选择业务部门' },
  { label: '业务日期', name: 'bizDate', type: 'date', placeholder: '请选择业务日期' },
  {
    label: '单据状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择单据状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  {
    label: '账单类型',
    name: 'billType',
    type: 'dic',
    placeholder: '请选择账单类型',
    code: 'CT_BASE_ENUM_DetailBill_BillType'
  },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dic',
    placeholder: '请选择业务状态',
    code: 'CT_BASE_ENUM_DetailBill_BizStatus'
  },
  {
    label: '租赁单元',
    name: 'mergeLeaseUnit',
    type: 'leaseUnitSelect',
    placeholder: '请选择租赁单元'
  },
  { label: '租金归集公司', name: 'mergeCollectionCompany', type: 'departSelect', placeholder: '请选择业务部门' },
  {
    label: '片区管理员',
    name: 'mergeAreaManager',
    type: 'userSelect',
    placeholder: '请选择片区管理员'
  },
  {
    label: '款项类型',
    name: 'mergePaymentType',
    type: 'paymentTypeSelect',
    placeholder: '请选择款项类型',
    fieldNames: { label: 'name', value: 'id' }
  },
  { label: '缴交周期', name: 'mergePeriodSeq', type: 'date', placeholder: '请选择缴交周期' },
  { label: '应收日期', name: 'mergeReceiveDate', type: 'date', placeholder: '请选择应收日期' },
  { label: '开始日期', name: 'receiveBeginDate', type: 'date', placeholder: '请选择开始日期' },
  { label: '到期日期', name: 'receiveEndDate', type: 'date', placeholder: '请选择到期日期' },
  { label: '应收金额', name: 'sumPaymentAmount', type: 'number', placeholder: '请输入应收金额' },
  { label: '减免金额', name: 'sumRemission', type: 'number', placeholder: '请输入减免金额' },
  { label: '实际应收金额', name: 'sumActualReceiveAmount', type: 'number', placeholder: '请输入实际应收金额' },
  { label: '已收金额', name: 'sumPaid', type: 'number', placeholder: '请输入已收金额' },
  { label: '未收金额', name: 'sumResidual', type: 'number', placeholder: '请输入未收金额' },
  { label: '已转应收金额', name: 'sumTransfered', type: 'number', placeholder: '请输入已转应收金额' },
  { label: '未转应收金额', name: 'sumTransferdBalance', type: 'number', placeholder: '请输入未转应收金额' },
  {
    label: '创建人',
    name: 'createBy',
    type: 'userSelect',
    placeholder: '请选择创建人'
  },
  { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
  {
    label: '修改人',
    name: 'updateBy',
    type: 'userSelect',
    placeholder: '请选择修改人'
  },
  { label: '修改时间', name: 'updateTime', type: 'date', placeholder: '请选择修改时间' }
])
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const defaultColumns = [
  { title: '账单编号', dataIndex: 'number', width: 180, fixed: true },
  {
    title: '单据状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '合同编号', dataIndex: 'contractNumber', ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
  { title: '客户', dataIndex: 'customer_dictText', ellipsis: true },
  { title: '业务员', dataIndex: 'operator_dictText', ellipsis: true },
  { title: '业务部门', dataIndex: 'operatorDepart_dictText', ellipsis: true },
  { title: '业务日期', dataIndex: 'bizDate' },
  {
    title: '账单类型',
    dataIndex: 'billType',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_DetailBill_BillType')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_DetailBill_BizStatus')
  },
  { title: '租赁单元', dataIndex: 'mergeLeaseUnit_dictText', ellipsis: true },
  { title: '租金归集公司', dataIndex: 'mergeCollectionCompany_dictText', ellipsis: true },
  { title: '片区管理员', dataIndex: 'mergeAreaManager_dictText' },
  { title: '款项类型', dataIndex: 'mergePaymentType_dictText' },
  { title: '缴交周期', dataIndex: 'mergePeriodSeq' },
  { title: '应收日期', dataIndex: 'mergeReceiveDate', ellipsis: true },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '到期日期', dataIndex: 'receiveEndDate' },
  { title: '应收金额', dataIndex: 'sumPaymentAmount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '减免金额', dataIndex: 'sumRemission', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '实际应收金额', dataIndex: 'sumActualReceiveAmount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已收金额', dataIndex: 'sumPaid', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未收金额', dataIndex: 'sumResidual', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已转应收金额', dataIndex: 'sumTransfered', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未转应收金额', dataIndex: 'sumTransferdBalance', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '创建人', dataIndex: 'createBy_dictText' },
  { title: '创建时间', dataIndex: 'createTime', ellipsis: true },
  { title: '修改人', dataIndex: 'updateBy_dictText' },
  { title: '修改时间', dataIndex: 'updateTime', ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const moreColumns = [
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', width: 260, fixed: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '缴交周期序号', dataIndex: 'periodSeq' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '到期日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '实际应收金额', dataIndex: 'actualReceiveAmount', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已转应收金额', dataIndex: 'transfered', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未转应收金额', dataIndex: 'transferdBalance', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已抵扣金额', dataIndex: 'transferDeduction', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已退款金额', dataIndex: 'refunded', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '尾差已处理金额', dataIndex: 'offDifference', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '可抵退转金额', dataIndex: 'balance', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已提房产税', dataIndex: 'houseTax', customRender: ({ text }) => renderMoney(text, 2) }
]

const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 查看合同详情
const contractDetailRef = ref()
const rowViewContractDetail = (row) => {
  contractDetailRef.value.open(row.contract)
}
// 查看退款
const router = useRouter()
const detailDrawerRef = ref()
const rowViewRefund = async (row) => {
  const { result } = await queryRefundReqBillByMainId(row.id)
  if (!result.length) {
    return message.warning('暂无退款单')
  }
  const ids = result.map((item) => item.id).join(',')
  if (result.length > 1) {
    sessionStorage.setItem('idFromStatementList', ids)
    return router.push({ path: '/finance/refund' })
  }
  // 退款单详情页
  detailDrawerRef.value.open(result[0])
}
// 生成应收单
const handleCreateReceiveBill = async (type) => {
  const detailBillEntryIdList = []
  selectedRows.value.forEach((item) => {
    detailBillEntryIdList.push(...item.detailBillEntryList.map((it) => it.id))
  })
  const params = {
    detailBillIdList: selectedRowKeys.value,
    detailBillEntryIdList,
    operatorType: type ? 'WriteOff' : 'CreateReceiveBill'
  }
  const data = await createReceiveBill(params)
  message.success(data.message)
}
// 查看租赁应收单
const receivableVoucherDetailRef = ref()
const rowLeaseReceivableInvoice = async (row) => {
  const { result } = await getIdsByDetail(row.id)
  if (!result.length) {
    return message.warning('暂无应收单数据！')
  }
  if (result.length && result.length === 1) {
    receivableVoucherDetailRef.value.open(result[0])
    return
  }
  router.push({ path: '/statement/receiveCertificate', query: { ids: result.join(',') } })
}

// 审核操作（临时）
const rowVerify = (row) => {
  Modal.confirm({
    title: '确认审核通过？',
    content: '',
    async onOk() {
      const data = await audit({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 反审核
const rowReverse = (row) => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await unAudit({ id: row.id })
      message.success(data.message)
    }
  })
}
// 撤回
const rowBack = (row) => {
  // if (!hasPermission('biz.funds:ct_fun_refund_req_bill:back')) return
  Modal.confirm({
    title: '确认撤回该资产？',
    content: '',
    async onOk() {
      const data = await back({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('明细账单数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>

<style lang="less" scoped>
:deep(.ant-table) {
  .ant-table-expanded-row {
    .ant-table-cell {
      .ant-table-expanded-row-fixed {
        padding-left: 0;
        padding-right: 0;
        .ant-spin-container {
          .ant-table {
            margin-inline: 0 -16px;
            border-radius: 0;
            .ant-table-thead {
              > tr {
                > th {
                  background-color: #e6e9f0;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
