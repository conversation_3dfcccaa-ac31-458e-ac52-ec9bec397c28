<template>
  <div>
    <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">资产基础信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">资产名称：{{ contractInfo.assetName }}</span>
      <span class="w-[50%]">资产类型：{{ contractInfo.assetType }}</span>
      <span class="w-[50%]">关联项目楼栋：{{ contractInfo.projectBuilding }}</span>
      <span class="w-[50%]">地址：{{ contractInfo.address }}</span>
      <span class="w-[50%]">归集公司：{{ contractInfo.gatheringCompany }}</span>
      <span class="w-[50%]">权属公司：{{ contractInfo.ownershipCompany }}</span>
      <span class="w-[50%]">管理公司：{{ contractInfo.managementCompany }}</span>
      <span class="w-[50%]">税费计提公司：{{ contractInfo.taxCompany }}</span>
      <span class="w-[50%]">业务状态：{{ contractInfo.businessStatus }}</span>
      <span class="w-[50%]">资产分类：{{ contractInfo.assetCategory }}</span>
      <span class="w-[50%]">代管委托方：{{ contractInfo.trustee }}</span>
      <span class="w-[50%]">备注：{{ contractInfo.remark }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">产权信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">产权证号：{{ contractInfo.certificateNo }}</span>
      <span class="w-[50%]">权证取得日期：{{ contractInfo.certificateDate }}</span>
      <span class="w-[50%]">取得来源：{{ contractInfo.source }}</span>
      <span class="w-[50%]">权利状态：{{ contractInfo.rightStatus }}</span>
      <span class="w-[50%]">产权用途：{{ contractInfo.usage }}</span>
      <span class="w-[50%]">房地产权证合一：{{ contractInfo.isUnified }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">土地信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">用地性质：{{ contractInfo.landNature }}</span>
      <span class="w-[50%]">土地建设情况：{{ contractInfo.landConstructionStatus }}</span>
      <span class="w-[50%]">土地批准使用开始日期：{{ contractInfo.landApprovalStartDate }}</span>
      <span class="w-[50%]">土地批准使用结束日期：{{ contractInfo.landApprovalEndDate }}</span>
      <span class="w-[50%]">土地取得价格：{{ contractInfo.landObtainPrice }}</span>
      <span class="w-[50%]">地价款(租金)/欠缴金额：{{ contractInfo.landArrears }}</span>
      <span class="w-[50%]">租赁土地租金：{{ contractInfo.landLeaseRent }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">建筑信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">房产类型：{{ contractInfo.propertyType }}</span>
      <span class="w-[50%]">建筑面积(m²)：{{ contractInfo.buildingArea }}</span>
      <span class="w-[50%]">宗地面积(m²)：{{ contractInfo.landArea }}</span>
      <span class="w-[50%]">建筑结构：{{ contractInfo.structure }}</span>
      <span class="w-[50%]">建成年份：{{ contractInfo.yearBuilt }}</span>
      <span class="w-[50%]">层数/总层数：{{ contractInfo.floorInfo }}</span>
      <span class="w-[50%]">层高(m)：{{ contractInfo.floorHeight }}</span>
      <span class="w-[50%]">户型：{{ contractInfo.houseType }}</span>
      <span class="w-[50%]">房屋安全等级：{{ contractInfo.safetyLevel }}</span>
      <span class="w-[50%]">消防等级：{{ contractInfo.fireLevel }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">税务信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">产权比例：{{ contractInfo.propertyRatio }}</span>
      <span class="w-[50%]">房产税计税原值：{{ contractInfo.propertyTaxOriginalValue }}</span>
      <span class="w-[50%]">增值税率(%)：{{ contractInfo.vatRate }}</span>
      <span class="w-[50%]">从价月税率(%)：{{ contractInfo.priceMonthTaxRate }}</span>
      <span class="w-[50%]">已租赁面积(m²)：{{ contractInfo.leasedArea }}</span>
      <span class="w-[50%]">土地面积(m²)：{{ contractInfo.taxLandArea }}</span>
      <span class="w-[50%]">土地使用税年收率标准：{{ contractInfo.landTaxYearRate }}</span>
      <span class="w-[50%]">取得日期：{{ contractInfo.obtainDate }}</span>
      <span class="w-[50%]">处置日期：{{ contractInfo.disposeDate }}</span>
      <span class="w-[50%]">接收日期：{{ contractInfo.receiveDate }}</span>
      <span class="w-[50%]">视同销售计提税费：{{ contractInfo.isDeemedSaleTax }}</span>
    </div>
  </div>
</template>
<script setup>
const { contractInfo } = defineProps({
  contractInfo: { type: Object, required: true }
})
</script>
