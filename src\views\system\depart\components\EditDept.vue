<template>
  <a-modal
    v-model:open="visible"
    :title="deptName ? `${deptName} - 添加下级` : '新增部门'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <base-info-form ref="formRef" :dept-tree="deptTree"></base-info-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import BaseInfoForm from './BaseInfoForm.vue'

defineProps({
  deptTree: { type: Array, default: () => [] }
})

const emit = defineEmits(['refresh'])

const visible = ref(false)
const deptName = ref('')
const open = async (dept) => {
  deptName.value = dept ? dept.title : ''
  visible.value = true
  await nextTick()
  if (dept) {
    formRef.value.setFormData({
      parentId: dept.id,
      parentName: dept.title,
      orgType: '2'
    })
  } else {
    formRef.value.setFormData({
      parentId: '',
      orgCategory: '1'
    })
  }
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    await formRef.value.save()
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>
