import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 获取客户列表
export const getCustomerList = (params) => {
  return request({
    method: 'get',
    url: '/bas/customer/list',
    params
  })
}

export const f7List = (params) => {
  return request({
    method: 'get',
    url: '/bas/customer/f7list',
    params
  })
}

// 通过 id 获取详情
export const queryCustomerById = (params) => {
  return request({
    method: 'get',
    url: '/bas/customer/queryById',
    params
  })
}

// 暂存
export const addCustomer = (data) => {
  return request({
    method: 'post',
    url: '/bas/customer/add',
    data
  })
}

// 更新
export const editCustomer = (data) => {
  return request({
    method: 'post',
    url: '/bas/customer/edit',
    data
  })
}

// 通过 id 删除
export const deleteCustomer = (params) => {
  return request({
    method: 'delete',
    url: '/bas/customer/delete',
    params
  })
}

// 批量删除
export const batchDeleteCustomer = (params) => {
  return request({
    method: 'delete',
    url: '/bas/customer/deleteBatch',
    params
  })
}

// 更新状态
export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/customer/updateEnableDisableStatus',
    data
  })
}

// 校验客户名称是否重复
export const verifyNameExists = (data) => {
  return request({
    method: 'post',
    url: '/bas/customer/verifyNameExists',
    data
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/customer/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

export const importExcel = (data, controller) => {
  return advanceUpload('/bas/customer/importExcel', data, controller)
}
