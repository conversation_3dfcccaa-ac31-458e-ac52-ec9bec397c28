import { queryEntityByKeys, queryEntityByClassPath } from './apis.js'

const useFormula = (classPath) => {
  const classInfos = ref([])
  const loadEntity = async () => {
    const { result } = await queryEntityByKeys('ct_share_water_share_bill,ct_share_water_share_bill_entry')
    classInfos.value = result
  }

  const classPathInfos = ref([])
  const loadPathEntity = async () => {
    const { result } = await queryEntityByClassPath(classPath)
    classPathInfos.value = result
  }

  const getScript = (script = '') => {
    if (!script) {
      return script
    }
    for (const info of classInfos.value) {
      for (const field of info.fields) {
        // 如果code包含 类名.属性 格式的替换
        // const reg = new RegExp(`(?<!\\\\S)${info.name}\\.${field.name}(?<!\\\\S)`, 'g')
        // const reg = new RegExp(`(^|\\s)${info.name}\\.${field.name}($|\\s)`, 'g');
        const reg = new RegExp(
          `(?<!\\p{sc=Han})(?=\\p{sc=Han})${info.name}\\.${field.name}(?<=\\p{sc=Han})(?!\\p{sc=Han})`,
          'gu'
        )
        script = script.replace(reg, `${info.clazz}.${field.field}`)
      }
      if (info.clazz === 'WaterShareBillEntry') {
        for (const field of info.fields) {
          // 特殊处理分录 .属性 格式替换
          const reg = new RegExp(`\\.${field.name}(?<=\\p{sc=Han})(?!\\p{sc=Han})`, 'gu')
          script = script.replace(reg, `.${field.field}`)
        }
      }
    }
    return script
  }

  const getScriptZn = (script = '') => {
    if (!script) {
      return ''
    }
    // 替换实体类属性
    for (const info of classInfos.value) {
      for (const field of info.fields) {
        // 如果code包含 "类名.属性" 格式的替换
        const reg = new RegExp(`\\b${info.clazz}\\.${field.field}\\b`, 'g')
        script = script.replace(reg, `${info.name}.${field.name}`)
      }
      if (info.clazz === 'WaterShareBillEntry') {
        for (const field of info.fields) {
          // 特殊处理分录 ".属性" 格式替换
          const reg = new RegExp(`(?<=[\\p{sc=Han}\\]])\\.${field.field}(?!\\p{sc=Han})`, 'gu')
          script = script.replace(reg, `.${field.name}`)
        }
      }
    }
    return script
  }

  onMounted(() => {
    loadEntity()
    if (classPath) {
      loadPathEntity()
    }
  })

  return {
    classInfos,
    classPathInfos,
    getScript,
    getScriptZn
  }
}

export default useFormula
