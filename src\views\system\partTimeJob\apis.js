import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/system/sysUserPartTime/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/system/sysUserPartTime/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/system/sysUserPartTime/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/system/sysUserPartTime/importExcel',
    data
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/system/sysUserPartTime/updateFinish',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/system/sysUserPartTime/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/system/sysUserPartTime/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/system/sysUserPartTime/deleteBatch',
    params
  })
}
