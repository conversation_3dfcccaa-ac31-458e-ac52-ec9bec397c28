<template>
  <a-modal
    v-model:open="visible"
    title="选择账单明细"
    width="1072px"
    class="common-modal contract-choose-bill-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center mb-[16px]">
      <s-input
        v-model:value="params.number"
        placeholder="搜索单据编号"
        @input="handleInput"
        class="!w-[280px] mr-[16px]"
      ></s-input>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <filter-more
        :params="params"
        label-width="100px"
        :search-list="searchList"
        :stat-ignore-keys="['customer']"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 2200, y: 'calc(75vh - 180px)' }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'detailAddress'">
          {{ record.province }}{{ record.city }}{{ record.district }}{{ record.detailAddress }}
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { billPage } from '../apis.js'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message } from 'ant-design-vue'
import { renderDictTag, renderBoolean, renderMoney } from '@/utils/render'
import { f7List } from '@/views/paymentType/apis'

const { customer, currentBillId } = defineProps({
  customer: { required: true, type: String },
  currentBillId: { required: true, type: String }
})

const emit = defineEmits(['updateBillList'])

const visible = ref(false)
const open = (list) => {
  selectedRowKeys.value = list.map((item) => item.id)
  selectedRows.value = list
  params.customer = customer
  params.currentBillId = currentBillId || undefined
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
}

const { list, pagination, tableLoading, onTableFetch } = usePageTable(billPage)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const params = reactive({
  column: 'periodTotalPeriod',
  order: 'desc',
  number: undefined,
  customer: undefined,
  currentBillId: undefined,
  paymentType: undefined,
  status: undefined,
  paymentAmount: undefined,
  actualReceiveAmount: undefined,
  paid: undefined,
  receiveDate: undefined
})
const searchList = [
  { label: '款项类型', name: 'paymentType', type: 'api-select', asyncFn: f7List },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '款项金额', name: 'paymentAmount', type: 'input' },
  { label: '应收金额', name: 'actualReceiveAmount', type: 'input' },
  { label: '已收金额', name: 'paid', type: 'input' },
  { label: '应收日期', name: 'receiveDate', type: 'date' }
]

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer_dictText' },
  { title: '合同编号', dataIndex: 'contractNumber', width: 180 },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text) },
  { title: '实际应收金额', dataIndex: 'actualReceiveAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' }
  // { title: '结清日期', dataIndex: 'wyProject_dictText' }
]

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const handleConfirm = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择账单明细')
    return
  }
  emit('updateBillList', [...selectedRows.value])
  handleCancel()
}

const handleCancel = () => {
  list.value = []
  clearSelection()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.contract-choose-bill-modal {
  top: 5vh;
  .ant-modal-body {
    max-height: 75vh !important;
  }
}
</style>
