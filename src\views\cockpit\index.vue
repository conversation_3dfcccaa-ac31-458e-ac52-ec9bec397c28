<template>
  <div class="cockpit">
    <div class="content h-full flex flex-col">
      <!-- 数据看板 -->
      <div class="flex justify-between mt-[40px] mb-[10px]">
        <div class="flex items-center">
          <span class="text-[18px] font-bold text-[#1D335C] mx-[8px]">欢迎来到数据驾驶舱</span>
          <span class="text-[14px] text-[#919191] mt-[5px]">数据统计至：{{ nowTime }}</span>
        </div>
        <company-select
          v-model="manageCompany"
          type="all"
          placeholder="全部公司"
          width="240px"
          allow-clear
        ></company-select>
      </div>

      <stat-card :manage-company="manageCompany" />
      <div class="flex-1 mt-[15px]">
        <tdt-map />
      </div>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import StatCard from '@/views/home/<USER>/StatCard.vue'

const manageCompany = ref('')
const nowTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
</script>

<style lang="less" scoped>
.cockpit {
  height: calc(100vh - 120px);
  background: linear-gradient(to bottom, #fff 10%, #f7feff 100%);
}

.content {
  max-width: 1680px;
  margin: 0 auto;
  padding: 0 20px 20px;
}
</style>
