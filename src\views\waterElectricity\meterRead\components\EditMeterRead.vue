<template>
  <a-modal
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}抄表数`"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="水电表" name="waterElectriCityTableNumId">
          <a-form-item-rest>
            <f7-select
              v-model="formData.waterElectriCityTableNumId"
              f7-type="waterElectricity"
              placeholder="请选择水电表"
              @change="handleWaterElectricityChange"
            />
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="上月表数" name="lastMonthTableNumber">
          <a-input-number
            v-model:value="formData.lastMonthTableNumber"
            placeholder="请输入上月表数"
            style="width: 100%"
            :min="0"
            :precision="4"
          />
        </a-form-item>
        <a-form-item label="收入归属年月" name="incomeBelongYm">
          <a-date-picker
            v-model:value="formData.incomeBelongYm"
            placeholder="请选择收入归属年月"
            style="width: 100%"
            value-format="YYYY-MM"
            picker="month"
          />
        </a-form-item>
        <a-form-item label="本月抄表时间" name="meterDate">
          <a-date-picker
            v-model:value="formData.meterDate"
            placeholder="请选择本月抄表时间"
            style="width: 100%"
            value-format="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item label="本月表数" name="thisMonthTableNumber">
          <a-input-number
            v-model:value="formData.thisMonthTableNumber"
            placeholder="请输入本月表数"
            style="width: 100%"
            :min="0"
            :precision="4"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addMeterRead, editMeterRead } from '../apis'
import { queryWaterElectricityById } from '@/views/waterElectricity/manage/apis/waterElectricity'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  id: undefined,
  number: undefined,
  waterElectriCityTableNumId: undefined,
  waterElectriCityTableNum: undefined,
  incomeBelongYm: undefined,
  meterDate: undefined,
  lastMonthTableNumber: undefined,
  thisMonthTableNumber: undefined,
  dataSource: 'ByHand'
}

const formData = reactive({ ...formDataDefault })

const rules = {
  waterElectriCityTableNum: [{ required: true, message: '请选择水电表' }],
  incomeBelongYm: [{ required: true, message: '请选择归属年月', trigger: 'change' }],
  meterDate: [{ required: true, message: '请选择本月抄表时间', trigger: 'change' }],
  lastMonthTableNumber: [
    { required: true, message: '请输入上月表数', trigger: 'blur' },
    { type: 'number', min: 0, message: '表数不能为负数', trigger: 'blur' }
  ],
  thisMonthTableNumber: [
    { required: true, message: '请输入本月表数', trigger: 'blur' },
    { type: 'number', min: 0, message: '表数不能为负数', trigger: 'blur' }
  ]
}

/**
 * 打开弹窗
 */
const open = (record) => {
  visible.value = true
  confirmLoading.value = true

  if (record?.id) {
    Object.assign(formData, record)
  }
  confirmLoading.value = false
}

/**
 * 处理水电表选择变化
 */
const handleWaterElectricityChange = (selectedId) => {
  if (!selectedId) {
    formData.waterElectriCityTableNumId = undefined
    return
  }
  formData.waterElectriCityTableNumId = selectedId
}

/**
 * 取消操作
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  visible.value = false
  emits('refresh')
}

/**
 * 保存抄表数信息
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true
  try {
    const { result } = await queryWaterElectricityById({ id: formData.waterElectriCityTableNumId })
    formData.waterElectriCityTableNum = result?.number

    if (formData.id) {
      await editMeterRead(formData)
    } else {
      await addMeterRead(formData)
    }
  } finally {
    confirmLoading.value = false
  }
  message.success('保存成功')
  handleCancel()
}

defineExpose({ open })
</script>
