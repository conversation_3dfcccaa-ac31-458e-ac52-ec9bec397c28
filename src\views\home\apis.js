import request from '@/apis/http'

// 数据看板-资产总面积-资产数量(个)-本年资产应收(万元)-本年资产已收(万元)
export const houseOwnerData = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/houseOwnerData',
    params
  })
}

// 数据看板-资产面积状态统计(㎡)
export const leaseUnitStatusArea = (params) => {
  return request({
    method: 'get',
    url: 'api/workbench/leaseUnitStatusArea',
    params
  })
}

// 数据看板-资产面积状态分布(个)
export const leaseUnitStatusCount = (params) => {
  return request({
    method: 'get',
    url: 'api/workbench/leaseUnitStatusCount',
    params
  })
}

// 数据看板-资产面积类型统计(㎡)
export const houseOwnerTypeArea = (params) => {
  return request({
    method: 'get',
    url: 'api/workbench/houseOwnerTypeArea',
    params
  })
}

// 数据看板-资产数量类型统计(个
export const houseOwnerTypeCount = (params) => {
  return request({
    method: 'get',
    url: 'api/workbench/houseOwnerTypeCount',
    params
  })
}

// 数据看板-新增客户数量统计(个)
export const customerCount = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/customerCount',
    params
  })
}

// 数据看板-每月财务应收统计(万元)
export const recMonth = ({ begin, end, company }) => {
  return request({
    method: 'get',
    url: `/api/workbench/recMonthlyTrend/${begin}/${end}/${company}`
  })
}

// 数据看板-财务收款金额类型统计(万元)
export const recPaymentType = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/recPaymentType',
    params
  })
}

// 数据看板-应收逾期账龄统计(万元)
export const recOverdueAge = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/recOverdueAge',
    params
  })
}
