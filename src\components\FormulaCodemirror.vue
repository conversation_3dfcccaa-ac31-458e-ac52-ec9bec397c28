<template>
  <div class="formula-codemirror-wrapper">
    <codemirror
      v-model="innerValue"
      :disabled="disabled"
      :extensions="[oneDark, javascript()]"
      :basic="true"
      :style="{ height }"
      @blur="handleBlur"
    ></codemirror>
  </div>
</template>

<script setup>
import { Codemirror } from 'vue-codemirror'
import { javascript } from '@codemirror/lang-javascript'
// 主题可选：@codemirror/theme-one-dark 等
import { oneDark } from '@codemirror/theme-one-dark'
import JSBeautify from 'js-beautify'

const props = defineProps({
  modelValue: { type: String, default: '' },
  disabled: { type: Boolean, default: false },
  height: { type: String, default: '400px' },
  beautify: { type: Boolean, default: false }
})
const emit = defineEmits(['update:modelValue'])
const innerValue = computed({
  get: () => {
    if (props.beautify) {
      return JSBeautify.js(props.modelValue, { indent_size: 2 })
    }
    return props.modelValue
  },
  set: (val) => emit('update:modelValue', val)
})

const lastCursor = ref(-1) // 鼠标离开编辑器时，记录光标所在位置

const handleBlur = (e) => {
  lastCursor.value = e.state.selection.main.head
}

defineExpose({
  lastCursor
})
</script>

<style scoped>
.formula-codemirror-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.cm-editor {
  height: 100%;
}
</style>
