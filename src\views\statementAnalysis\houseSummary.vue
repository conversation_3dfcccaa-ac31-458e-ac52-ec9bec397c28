房产情况汇总表
<template>
  <div class="house-summary">
    <a-form layout="inline" class="!mb-[16px]">
      <a-form-item label="查询截止日期">
        <a-date-picker
          v-model:value="params.endTime"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="loadData"
          :allow-clear="false"
        ></a-date-picker>
      </a-form-item>
      <a-form-item label="物业管理公司">
        <company-select v-model="params.manageCompanyList" type="all" disabled width="240px"></company-select>
      </a-form-item>
      <a-form-item>
        <a-button @click="loadData">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          :clear-ignore-keys="['manageCompanyList', 'endTime']"
          :stat-ignore-keys="['manageCompanyList', 'endTime']"
          @query="loadData"
        ></filter-more>
      </a-form-item>
    </a-form>
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="false"
      :scroll="{ y: height }"
      bordered
      :row-class-name="(record) => (record.ownerCompanyName === '小计' ? 'sum-row' : '')"
    >
      <template #summary>
        <a-table-summary-row>
          <a-table-summary-cell>合计</a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell>{{ totalLeaseUnitArea }}</a-table-summary-cell>
          <a-table-summary-cell>
            {{ bizStatusGrandTotals.find((i) => i.bizStatus === 'InLease')?.grandTotalArea }}
          </a-table-summary-cell>
          <a-table-summary-cell>
            {{ bizStatusGrandTotals.find((i) => i.bizStatus === 'Occupy')?.grandTotalArea }}
          </a-table-summary-cell>
          <a-table-summary-cell>
            {{ bizStatusGrandTotals.find((i) => i.bizStatus === 'NotUse')?.grandTotalArea }}
          </a-table-summary-cell>
          <a-table-summary-cell>
            {{ bizStatusGrandTotals.find((i) => i.bizStatus === 'OurSelf')?.grandTotalArea }}
          </a-table-summary-cell>
          <a-table-summary-cell>
            {{ bizStatusGrandTotals.find((i) => i.bizStatus === 'notConditLease')?.grandTotalArea }}
          </a-table-summary-cell>
          <a-table-summary-cell>
            {{ bizStatusGrandTotals.find((i) => i.bizStatus === 'Dispute')?.grandTotalArea }}
          </a-table-summary-cell>
        </a-table-summary-row>
      </template>
    </a-table>
    <house-detail ref="houseDetailRef"></house-detail>
    <!-- <search-more ref="searchMoreRef" @refresh="refresh"></search-more> -->
  </div>
</template>

<script setup>
import { houseSummaryList } from './apis'
import HouseDetail from './components/HouseDetail.vue'
// import SearchMore from './components/SearchMore.vue'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'

const { userInfo } = useUserStore()

const params = reactive({
  ownerCompanyList: '', // 权属公司列表
  manageCompanyList: userInfo.value.currentCompany, // 管理公司列表
  leaseUseList: '', // 租赁类型列表
  bizStatusList: '', // 业务状态列表
  endTime: dayjs().format('YYYY-MM-DD') // 截止日期
})

const searchList = [
  { label: '权属单位', name: 'ownerCompanyList', type: 'company-select', companyType: 'all' },
  { label: '租赁用途', name: 'leaseUseList', type: 'dict-select', code: 'CT_BAS_LeaseUse' },
  { label: '业务状态', name: 'bizStatusList', type: 'dict-select', code: 'CT_BASE_ENUM_LeaseUnit_BizStatus' }
]

// const filterCount = computed(() => {
//   let count = 0
//   if (params.ownerCompanyList && params.ownerCompanyList.length) count++
//   if (params.manageCompanyList && params.manageCompanyList.length) count++
//   if (params.leaseUseList && params.leaseUseList.length) count++
//   if (params.bizStatusList && params.bizStatusList.length) count++
//   return count
// })

const loading = ref(false)
const tableData = ref([])
const totalLeaseUnitArea = ref(0) // 总面积
const bizStatusGrandTotals = ref([]) // 各个租赁单元业务状态的统计

const loadData = async () => {
  if (loading.value) return
  try {
    tableData.value = []
    loading.value = true
    const tempParams = {}
    for (const key in params) {
      if (key === 'endTime') {
        tempParams[key] = params[key]
      } else {
        tempParams[key] = params[key] && params[key].length ? [params[key]] : []
      }
    }
    const { result } = await houseSummaryList(tempParams)
    totalLeaseUnitArea.value = result.totalLeaseUnitArea
    bizStatusGrandTotals.value = result.bizStatusGrandTotals
    result.dataList.forEach((item) => {
      item.leaseUseDetails.forEach((leaseUnit, leaseUnitIndex) => {
        if (leaseUnitIndex === 0) {
          leaseUnit.rowSpan = item.leaseUseDetails.length
        } else {
          leaseUnit.rowSpan = 0
        }
        leaseUnit.ownerCompany = item.ownerCompany // 权属公司
        leaseUnit.ownerCompanyName = item.ownerCompanyName // 权属公司
        leaseUnit.bizStatusDetails.forEach((biz) => {
          leaseUnit[biz.bizStatus] = biz.bizStatusArea
        })
        tableData.value.push(leaseUnit)
      })
      tableData.value.push({
        ownerCompanyName: '小计',
        leaseUseName: '',
        leaseUseArea: item.totalLeaseArea,
        InLease: item.bizStatusTotalAreas.find((i) => i.bizStatus === 'InLease')?.totalArea,
        bizStatusName: item.bizStatusTotalAreas.find((i) => i.bizStatus === 'bizStatusName')?.totalArea,
        NotUse: item.bizStatusTotalAreas.find((i) => i.bizStatus === 'NotUse')?.totalArea,
        OurSelf: item.bizStatusTotalAreas.find((i) => i.bizStatus === 'OurSelf')?.totalArea,
        notConditLease: item.bizStatusTotalAreas.find((i) => i.bizStatus === 'notConditLease')?.totalArea,
        Dispute: item.bizStatusTotalAreas.find((i) => i.bizStatus === 'Dispute')?.totalArea
      })
    })
  } finally {
    loading.value = false
  }
}

// const searchMoreRef = ref()
// const handleFilter = () => {
//   searchMoreRef.value.open()
// }

// const refresh = (data) => {
//   Object.assign(params, data)
//   loadData()
// }

const houseDetailRef = ref()

const columns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
  {
    title: '权属公司',
    dataIndex: 'ownerCompanyName',
    width: 260,
    customCell: (record) => {
      return {
        rowSpan: record.rowSpan,
        style: {
          cursor: 'pointer'
        },
        onDblclick: () => {
          if (record.ownerCompanyName === '小计') return
          houseDetailRef.value.open({
            ownerCompanyList: record.ownerCompany
          })
        }
      }
    }
  },
  {
    title: '租赁用途',
    dataIndex: 'leaseUseName',
    customCell: (record) => {
      return {
        style: {
          cursor: 'pointer'
        },
        onDblclick: () => {
          if (record.ownerCompanyName === '小计') return
          houseDetailRef.value.open({
            ownerCompanyList: record.ownerCompany,
            leaseUseList: record.leaseUse
          })
        }
      }
    }
  },
  {
    title: '总面积(m²)',
    dataIndex: 'leaseUseArea',
    customCell: (record) => {
      return {
        style: {
          cursor: 'pointer'
        },
        onDblclick: () => {
          if (record.ownerCompanyName === '小计') return
          houseDetailRef.value.open({
            ownerCompanyList: record.ownerCompany,
            leaseUseList: record.leaseUse
          })
        }
      }
    }
  },
  {
    title: '租赁单元业务状态',
    children: [
      {
        title: '已出租面积(m²)',
        children: [
          {
            title: '在租',
            dataIndex: 'InLease',
            customRender: ({ text }) => text || '0.00',
            customCell: (record, _, column) => {
              return {
                style: {
                  cursor: 'pointer'
                },
                onDblclick: () => {
                  if (record.ownerCompanyName === '小计') return
                  houseDetailRef.value.open({
                    ownerCompanyList: record.ownerCompany,
                    leaseUseList: record.leaseUse,
                    bizStatusList: column.dataIndex
                  })
                }
              }
            }
          },
          {
            title: '占用',
            dataIndex: 'bizStatusName',
            customRender: ({ text }) => text || '0.00',
            customCell: (record, _, column) => {
              return {
                style: {
                  cursor: 'pointer'
                },
                onDblclick: () => {
                  if (record.ownerCompanyName === '小计') return
                  houseDetailRef.value.open({
                    ownerCompanyList: record.ownerCompany,
                    leaseUseList: record.leaseUse,
                    bizStatusList: column.dataIndex
                  })
                }
              }
            }
          }
        ]
      },
      {
        title: '未出租面积(m²)',
        children: [
          {
            title: '空置',
            dataIndex: 'NotUse',
            customRender: ({ text }) => text || '0.00',
            customCell: (record, _, column) => {
              return {
                style: {
                  cursor: 'pointer'
                },
                onDblclick: () => {
                  if (record.ownerCompanyName === '小计') return
                  houseDetailRef.value.open({
                    ownerCompanyList: record.ownerCompany,
                    leaseUseList: record.leaseUse,
                    bizStatusList: column.dataIndex
                  })
                }
              }
            }
          },
          {
            title: '自用',
            dataIndex: 'OurSelf',
            customRender: ({ text }) => text || '0.00',
            customCell: (record, _, column) => {
              return {
                style: {
                  cursor: 'pointer'
                },
                onDblclick: () => {
                  if (record.ownerCompanyName === '小计') return
                  houseDetailRef.value.open({
                    ownerCompanyList: record.ownerCompany,
                    leaseUseList: record.leaseUse,
                    bizStatusList: column.dataIndex
                  })
                }
              }
            }
          },
          {
            title: '不具备出租条件',
            dataIndex: 'notConditLease',
            customRender: ({ text }) => text || '0.00',
            customCell: (record, _, column) => {
              return {
                style: {
                  cursor: 'pointer'
                },
                onDblclick: () => {
                  if (record.ownerCompanyName === '小计') return
                  houseDetailRef.value.open({
                    ownerCompanyList: record.ownerCompany,
                    leaseUseList: record.leaseUse,
                    bizStatusList: column.dataIndex
                  })
                }
              }
            }
          },
          {
            title: '纠纷',
            dataIndex: 'Dispute',
            customRender: ({ text }) => text || '0.00',
            customCell: (record, _, column) => {
              return {
                style: {
                  cursor: 'pointer'
                },
                onDblclick: () => {
                  if (record.ownerCompanyName === '小计') return
                  houseDetailRef.value.open({
                    ownerCompanyList: record.ownerCompany,
                    leaseUseList: record.leaseUse,
                    bizStatusList: column.dataIndex
                  })
                }
              }
            }
          }
        ]
      }
    ]
  }
]

const height = ref('50vh')

let page // BasicLayout右侧的router-view
let table // a-table
let tableHead // a-table的表格头

function getDom() {
  page = document.getElementById('basic-router-view')
  table = page.querySelector('.ant-table-wrapper')
  tableHead = table.querySelector('.ant-table-thead')
}

function getHeight() {
  const tableHeadHeight = tableHead.getBoundingClientRect().height // 表格头的高度
  const tableTop = table.getBoundingClientRect().top // a-table在浏览器可视区域里的top值
  // 最后减去的2px是ant-table的border
  height.value = `${document.documentElement.offsetHeight - tableTop - tableHeadHeight - 32 - 4}px`
}

onMounted(() => {
  loadData()
  getDom()
  getHeight()
  window.addEventListener('resize', getHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', getHeight)
})
</script>

<style lang="less" scoped>
.house-summary {
  :deep(.ant-table-wrapper) {
    .ant-table-tbody > tr.ant-table-row:hover > td {
      background: transparent;
    }
    .ant-table-tbody > tr > td.ant-table-cell-row-hover {
      background: transparent;
    }
    .ant-table-tbody > tr.ant-table-row > td:hover {
      background-color: #eaf0fe;
    }
    .ant-table-tbody {
      .sum-row td {
        background-color: rgba(var(--color-primary-rgb), 0.1) !important;
      }
    }
    .ant-table-summary {
      .ant-table-cell {
        background-color: rgba(var(--color-primary-rgb), 0.1);
      }
    }
  }
}
</style>
