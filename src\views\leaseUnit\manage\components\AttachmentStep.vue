<template>
  <div>
    <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">附件</h4>
    <div class="p-4">
      <files-upload v-model="formData.attachmentIds" :biz-id="bizId" />
    </div>
  </div>
</template>

<script setup>
const { formData } = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const bizId = computed(() => {
  return formData.id || formData.bizId
})
</script>
