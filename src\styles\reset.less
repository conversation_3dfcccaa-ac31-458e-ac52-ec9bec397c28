/* 该文件实际上是ant-design-vue/dist/reset.css，在此基础上，做一点修改，兼容tailwindcss的样式预检 */
@font-face {
  font-family: siyuan;
  font-display: swap;
  src: url('@/assets/fonts/siyuan-Regular.ttf') format('trueType');
}
html,
body {
  width: 100%;
  height: 100%;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
html {
  font-family: siyuan;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
}
@-ms-viewport {
  width: device-width;
}
[tabindex='-1']:focus {
  outline: none;
}
abbr[title],
abbr[data-original-title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline;
  text-decoration: underline dotted;
  border-bottom: 0;
  cursor: help;
}
address {
  font-style: normal;
  line-height: inherit;
}
dfn {
  font-style: italic;
}
pre,
code,
kbd,
samp {
  font-family: siyuan;
}
pre {
  overflow: auto;
}
figure {
  margin: 0 0 1em;
}
a,
area,
button,
[role='button'],
input:not([type='range']),
label,
select,
summary,
textarea {
  touch-action: manipulation;
}
caption {
  padding-top: 0.75em;
  padding-bottom: 0.3em;
  text-align: left;
  caption-side: bottom;
}
input,
button,
select,
optgroup,
textarea {
  color: inherit;
  font-size: inherit;
  font-family: siyuan;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type='radio'],
input[type='checkbox'] {
  padding: 0;
}
input[type='date'],
input[type='time'],
input[type='datetime-local'],
input[type='month'] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
}
fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 0.5em;
  padding: 0;
  color: inherit;
  font-size: 1.5em;
  line-height: inherit;
  white-space: normal;
}
[type='search'] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type='search']::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
