<template>
  <a-drawer
    v-model:open="visible"
    class="edit-lease-unit-for-change-drawer common-drawer"
    title="编辑租赁单元(变更比较)"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <circle-steps
        :current="currentStep + 1"
        :step-list="stepList"
        width="900px"
        class="mx-auto mb-[40px]"
      ></circle-steps>
      <basic-info-step
        v-show="currentStep === 0"
        ref="basicFormRef"
        :form-data="formData"
        @house-owner-change="handleAssetChange"
      />

      <lease-info-step v-show="currentStep === 1" ref="leaseFormRef" :form-data="formData" />

      <building-info-step v-show="currentStep === 2" ref="landFormRef" :form-data="formData" />

      <water-electric-step v-show="currentStep === 3" :form-data="formData" />

      <attachment-step v-show="currentStep === 4" :form-data="formData" />
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">确认变更</a-button>
      <a-button :disabled="currentStep <= 0" @click="handlePrevStep">上一步</a-button>
      <a-button :disabled="currentStep >= stepList.length - 1" @click="handleNextStep">下一步</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { detailById } from '@/views/assets/manage/apis'
import BasicInfoStep from '@/views/leaseUnit/manage/components/BasicInfoStep.vue'
import LeaseInfoStep from '@/views/leaseUnit/manage/components/LeaseInfoStep.vue'
import BuildingInfoStep from '@/views/leaseUnit/manage/components/BuildingInfoStep.vue'
import WaterElectricStep from '@/views/leaseUnit/manage/components/WaterElectricStep.vue'
import AttachmentStep from '@/views/leaseUnit/manage/components/AttachmentStep.vue'

const emits = defineEmits(['refresh'])

const basicFormRef = ref()
const leaseFormRef = ref()
const landFormRef = ref()

const visible = ref(false)
const currentStep = ref(0)
const confirmLoading = ref(false)

const stepList = ['基础信息', '租赁信息', '土地及建筑物信息', '水电费用', '附件信息']

const formDataDefault = {
  id: undefined,
  name: undefined,
  virtualLeaseUnit: false,
  houseOwner: undefined,
  houseOwner_dictText: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  wyBuilding: undefined,
  wyFloor: undefined,
  wyProjectArray: [],
  province: undefined,
  city: undefined,
  area: undefined,
  pcaCode: [],
  detailAddress: undefined,
  assetType: undefined,
  ownerCompany: undefined,
  collectionCompany: undefined,
  manageCompany: undefined,
  landNature: undefined,
  treePath: [],
  treeId: undefined,
  supportFacility: undefined,
  remark: undefined,
  useType: undefined,
  leaseArea: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  effectDate: undefined,
  expireDate: undefined,
  houseType: undefined,
  structureArea: undefined,
  floorArea: undefined,
  buildStructrue: undefined,
  buildYear: undefined,
  layerNum: undefined,
  layerHight: undefined,
  houseModel: undefined,
  firefightingRate: undefined,
  houseSafeRate: undefined,
  houseTaxOrgValue: undefined,
  addTaxRate: undefined,
  invoiceAddress: undefined,
  waterShareFormulas: [],
  attachmentIds: undefined,
  currentLayer: undefined,
  totalLayer: undefined
}

const formData = reactive({ ...formDataDefault })

/**
 * 打开编辑弹窗，初始化表单数据
 * @param {Object} data - 租赁单元数据
 */
const open = (data) => {
  visible.value = true
  Object.assign(formData, formDataDefault)
  if (data?.id) {
    Object.assign(formData, data)
    formData.propertyUse = formData.propertyUse || undefined
    // 处理资产数据
    if (formData.virtualLeaseUnit !== true) {
      formData.houseOwner = undefined
      formData.houseOwner_dictText = undefined
    }
    // 处理层数数据
    if (formData.layerNum) {
      const [currentLayer = '', totalLayer = ''] = formData.layerNum.split('/')
      Object.assign(formData, { currentLayer, totalLayer })
    }
    if (formData.buildYear) {
      formData.buildYear = String(formData.buildYear)
    }
    // 处理省市区数据
    if (formData.pcaCode) {
      formData.pcaCode = formData.pcaCode.split(';')
    }
    if (formData.treePath && formData.treePath.length) {
      formData.treePath = formData.treePath.split(';')
    }
    formData.wyProjectArray = [formData.wyProject, formData.wyBuilding, formData.wyFloor].filter(Boolean)
  }
}

/**
 * 取消操作并重置表单数据
 */
const handleCancel = () => {
  basicFormRef.value?.resetFields()
  leaseFormRef.value?.resetFields()
  landFormRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  formData.files = []
  currentStep.value = 0
  visible.value = false
}

/**
 * 上一步
 */
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/**
 * 下一步，验证当前步骤表单后进入下一步
 */
const handleNextStep = async () => {
  if (currentStep.value >= stepList.length - 1) return

  try {
    switch (currentStep.value) {
      case 0:
        await basicFormRef.value?.validate()
        break
      case 1:
        await leaseFormRef.value?.validate()
        break
      case 2:
        await landFormRef.value?.validate()
        break
    }
    currentStep.value++
  } catch {
    message.error('请填写完必填项后再进入下一步')
  }
}

/**
 * 确认变更并返回数据
 */
const handleSubmit = async () => {
  if (confirmLoading.value) return

  try {
    await basicFormRef.value?.validate()
  } catch {
    message.error('请检查并填写完所有必填项')
    currentStep.value = 0
    return
  }

  try {
    await leaseFormRef.value?.validate()
  } catch {
    message.error('请检查并填写完所有必填项')
    currentStep.value = 1
    return
  }

  try {
    await landFormRef.value?.validate()
  } catch {
    message.error('请检查并填写完所有必填项')
    currentStep.value = 2
    return
  }

  confirmLoading.value = true

  try {
    formData.pcaCode = Array.isArray(formData.pcaCode) ? formData.pcaCode.join(';') : formData.pcaCode
    formData.treePath = Array.isArray(formData.treePath) ? formData.treePath.join(';') : formData.treePath
    message.success('变更确认成功')
    emits('refresh', { ...formData })
  } finally {
    confirmLoading.value = false
  }

  handleCancel()
}

/**
 * 处理资产选择变更事件，获取资产详情并同步相关字段
 * @param {string} selectId - 选中的资产ID
 */
const handleAssetChange = async (selectId) => {
  if (!selectId) {
    formData.houseOwner = undefined
    return
  }

  const { result } = await detailById(selectId)
  if (result) {
    formData.houseOwner = result.id
    // 关联项目-楼栋-楼层
    formData.wyProject = result.wyProject
    formData.wyBuilding = result.wyBuilding
    formData.wyFloor = result.wyFloor
    formData.wyProjectArray = [formData.wyProject, formData.wyBuilding, formData.wyFloor].filter(Boolean)
    // 关联省市区
    formData.province = result.province
    formData.city = result.city
    formData.area = result.area
    formData.pcaCode = [formData.province, formData.city, formData.area].filter(Boolean)
    // 关联详细地址
    formData.detailAddress = result.detailAddress
  }
}

defineExpose({ open })
</script>

<style lang="less">
.edit-lease-unit-for-change-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }

  .site-input-split {
    background-color: #fff;
  }
  .site-input-right {
    border-left-width: 0;
    &:hover,
    &:focus {
      border-left-width: 1px;
    }
  }

  .ant-input-rtl.site-input-right {
    border-right-width: 0;
    &:hover,
    &:focus {
      border-right-width: 1px;
    }
  }
}
</style>
