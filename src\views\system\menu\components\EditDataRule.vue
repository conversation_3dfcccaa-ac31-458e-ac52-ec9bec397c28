<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑规则' : '新增规则'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '74px' } }"
      autocomplete="off"
    >
      <a-form-item label="规则名称" name="ruleName">
        <a-input v-model:value="form.ruleName" placeholder="请输入规则名称" :maxlength="30" />
      </a-form-item>
      <a-form-item label="规则字段" name="ruleColumn" v-if="form.ruleConditions !== 'USE_SQL_RULES'">
        <a-input v-model:value="form.ruleColumn" placeholder="请输入规则字段" :maxlength="50" />
      </a-form-item>
      <a-form-item label="条件规则" name="ruleConditions">
        <dict-select v-model="form.ruleConditions" code="rule_conditions"></dict-select>
      </a-form-item>
      <a-form-item label="规则值" name="ruleValue">
        <a-form-item-rest>
          <a-input-group compact>
            <a-input v-model:value="form.ruleValue" style="width: 60%" @blur="handleBlur" />
            <a-select
              v-model:value="systemValue"
              :options="options"
              placeholder="可选择系统变量"
              allow-clear
              style="width: 40%"
              @change="onSystemValueChange"
            ></a-select>
          </a-input-group>
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="form.status">
          <a-radio value="1">有效</a-radio>
          <a-radio value="0">无效</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addPermissionRule, editPermissionRule } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
    const val = options.find((i) => i.value === data.ruleValue)
    if (val) {
      systemValue.value = val.value
    }
  }
  visible.value = true
}
const form = reactive({
  id: '',
  permissionId: '',
  ruleColumn: '',
  ruleConditions: '',
  ruleName: '',
  ruleValue: '',
  status: '1'
})

const rules = {
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  ruleConditions: [{ required: true, message: '请选择条件规则', trigger: 'change' }],
  ruleValue: [{ required: true, message: '请选择规则值', trigger: 'blur' }]
}

const systemValue = ref('') // 规则值<系统变量>
const options = [
  { label: '登录用户主键', value: '#{sys_user_id}' },
  { label: '当前日期', value: '#{sys_date}' },
  { label: '当前时间', value: '#{sys_time}' },
  { label: '登录用户公司', value: '#{sys_org_company}' },
  { label: '登录用户部门', value: '#{sys_org_depart}' },
  { label: '用户拥有公司', value: '#{sys_multi_company_id}' },
  { label: '用户拥有部门', value: '#{sys_multi_depart_id}' }
]
const onSystemValueChange = (val) => {
  form.ruleValue = val || ''
}
const handleBlur = () => {
  const data = options.find((item) => item.value === form.ruleValue)
  if (!data) {
    systemValue.value = ''
  }
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    if (form.ruleConditions === 'USE_SQL_RULES') {
      form.ruleColumn = ''
    }
    form.id ? await editPermissionRule(form) : await addPermissionRule(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.id = ''
  form.permissionId = ''
  form.ruleColumn = ''
  form.ruleConditions = ''
  form.ruleName = ''
  form.ruleValue = ''
  form.status = '1'
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
