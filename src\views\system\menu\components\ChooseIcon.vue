<template>
  <a-input :value="modelValue" placeholder="请选择菜单图标" readonly @click="visible = true">
    <template #suffix>
      <i :class="modelValue"></i>
      <i class="a-icon-close-solid text-tertiary cursor-pointer" @click.stop="handleClear"></i>
    </template>
  </a-input>
  <a-modal v-model:open="visible" title="选择菜单图标" width="600px" wrap-class-name="common-modal" :footer="null">
    <ul class="flex flex-wrap">
      <li
        v-for="item in iconList"
        :key="item"
        @click="handleChoose(item)"
        class="w-[48px] h-[48px] flex items-center justify-center cursor-pointer hover:text-primary transition-colors"
      >
        <i :class="[item, 'text-[32px]']"></i>
      </li>
    </ul>
  </a-modal>
</template>

<script setup>
defineProps({
  modelValue: { required: true, type: String }
})

const emits = defineEmits(['update:modelValue'])

const visible = ref(false)
const handleChoose = (icon) => {
  emits('update:modelValue', icon)
  visible.value = false
}

const handleClear = () => {
  emits('update:modelValue', '')
}

const iconList = [
  'a-icon-data-view',
  'a-icon-todo',
  'a-icon-org',
  'a-icon-audit',
  'a-icon-account-setting',
  'a-icon-new-file',
  'a-icon-new-user',
  'a-icon-account',
  'a-icon-question',
  'a-icon-key',
  'a-icon-placeholder',
  'a-icon-company',
  'a-icon-password',
  'a-icon-house',
  'a-icon-money2',
  'a-icon-import-right',
  'a-icon-category',
  'a-icon-new-customer',
  'a-icon-coins',
  'a-icon-hand-coin',
  'a-icon-help',
  'a-icon-bill',
  'a-icon-date',
  'a-icon-statistics',
  'a-icon-bell',
  'a-icon-phone',
  'a-icon-folder',
  'a-icon-wallet',
  'a-icon-money',
  'a-icon-setting',
  'a-icon-new-folder',
  'a-icon-pack-up',
  'a-icon-edit',
  'a-icon-download',
  'a-icon-filter',
  'a-icon-export-right',
  'a-icon-list'
]
</script>
