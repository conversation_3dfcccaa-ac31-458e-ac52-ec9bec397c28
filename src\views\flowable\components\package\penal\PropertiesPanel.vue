<template>
  <div class="process-panel__container" :style="{ width: `${this.width}px` }" :class="hide ? 'hide' : ''">
    <div class="switch-btn" @click="changePanelHide">
      <double-right-outlined class="hide-btn" v-if="!hide" />
      <double-left-outlined class="show-btn" v-else />
    </div>
    <div class="process-panel-box">
      <a-collapse v-model:active-key="activeTab">
        <a-collapse-panel key="base">
          <template #header>
            <div class="panel-tab__title">
              <info-circle-outlined />
              常规
            </div>
          </template>
          <element-base-info
            :id-edit-disabled="idEditDisabled"
            :business-object="elementBusinessObject"
            :type="elementType"
          />
        </a-collapse-panel>
        <a-collapse-panel v-if="elementType.indexOf('Task') !== -1" key="task">
          <template #header>
            <div class="panel-tab__title">任务</div>
          </template>
          <element-task :id="elementId" :type="elementType" />
        </a-collapse-panel>
        <a-collapse-panel v-if="elementType === 'Process'" key="message">
          <template #header>
            <div class="panel-tab__title">消息与信号</div>
          </template>
          <signal-and-massage />
        </a-collapse-panel>
        <a-collapse-panel v-if="conditionFormVisible" key="condition">
          <template #header>
            <div class="panel-tab__title">流转条件</div>
          </template>
          <flow-condition :business-object="elementBusinessObject" :type="elementType" />
        </a-collapse-panel>
        <a-collapse-panel v-if="formVisible" key="form">
          <template #header>
            <div class="panel-tab__title">表单</div>
          </template>
          <element-form :id="elementId" :type="elementType" />
        </a-collapse-panel>
        <a-collapse-panel v-if="elementType.indexOf('Task') !== -1" key="multiInstance">
          <template #header>
            <div class="panel-tab__title">多实例</div>
          </template>
          <element-multi-instance :business-object="elementBusinessObject" :type="elementType" />
        </a-collapse-panel>
        <a-collapse-panel key="listeners">
          <template #header>
            <div class="panel-tab__title">执行监听器</div>
          </template>
          <element-listeners :id="elementId" :type="elementType" />
        </a-collapse-panel>
        <a-collapse-panel v-if="elementType === 'UserTask'" key="taskListeners">
          <template #header>
            <div class="panel-tab__title">任务监听器</div>
          </template>
          <user-task-listeners :id="elementId" :type="elementType" />
        </a-collapse-panel>
        <a-collapse-panel key="extensions">
          <template #header>
            <div class="panel-tab__title">扩展属性</div>
          </template>
          <element-properties :id="elementId" :type="elementType" />
        </a-collapse-panel>
        <a-collapse-panel key="other">
          <template #header>
            <div class="panel-tab__title">其他</div>
          </template>
          <element-other-config :id="elementId" />
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>
<script>
import ElementBaseInfo from './base/ElementBaseInfo.vue'
import ElementOtherConfig from './other/ElementOtherConfig.vue'
import ElementTask from './task/ElementTask.vue'
import ElementMultiInstance from './multi-instance/ElementMultiInstance.vue'
import FlowCondition from './flow-condition/FlowCondition.vue'
import SignalAndMassage from './signal-message/SignalAndMessage.vue'
import ElementListeners from './listeners/ElementListeners.vue'
import ElementProperties from './properties/ElementProperties.vue'
import ElementForm from './form/ElementForm.vue'
import UserTaskListeners from './listeners/UserTaskListeners.vue'
import Log from '../Log'
import { InfoCircleOutlined, DoubleRightOutlined, DoubleLeftOutlined } from '@ant-design/icons-vue'
/**
 * 侧边栏
 * <AUTHOR>
 * @Home https://github.com/miyuesc
 * @Date 2021年3月31日18:57:51
 */
export default {
  name: 'MyPropertiesPanel',
  components: {
    InfoCircleOutlined,
    DoubleRightOutlined,
    DoubleLeftOutlined,
    UserTaskListeners,
    ElementForm,
    ElementProperties,
    ElementListeners,
    SignalAndMassage,
    FlowCondition,
    ElementMultiInstance,
    ElementTask,
    ElementOtherConfig,
    ElementBaseInfo
  },
  componentName: 'MyPropertiesPanel',
  props: {
    bpmnModeler: Object,
    prefix: {
      type: String,
      default: 'camunda'
    },
    width: {
      type: Number,
      default: 480
    },
    idEditDisabled: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      prefix: this.prefix,
      width: this.width
    }
  },
  data() {
    return {
      hide: false,
      activeTab: ['base', 'condition', 'form', 'task'],
      elementId: '',
      elementType: '',
      elementBusinessObject: {}, // 元素 businessObject 镜像，提供给需要做判断的组件使用
      conditionFormVisible: false, // 流转条件设置
      formVisible: false // 表单配置
    }
  },
  watch: {
    elementId: {
      handler() {
        this.activeTab = ['base', 'condition', 'form', 'task']
      }
    }
  },
  created() {
    this.initModels()
  },
  methods: {
    changePanelHide() {
      this.hide = !this.hide
    },
    initModels() {
      // 初始化 modeler 以及其他 moddle
      if (!this.bpmnModeler) {
        // 避免加载时 流程图 并未加载完成
        // eslint-disable-next-line vue/no-undef-properties
        this.timer = setTimeout(() => this.initModels(), 10)
        return
      }
      if (this.timer) clearTimeout(this.timer)
      window.bpmnInstances = {
        modeler: this.bpmnModeler,
        modeling: this.bpmnModeler.get('modeling'),
        moddle: this.bpmnModeler.get('moddle'),
        eventBus: this.bpmnModeler.get('eventBus'),
        bpmnFactory: this.bpmnModeler.get('bpmnFactory'),
        elementFactory: this.bpmnModeler.get('elementFactory'),
        elementRegistry: this.bpmnModeler.get('elementRegistry'),
        replace: this.bpmnModeler.get('replace'),
        selection: this.bpmnModeler.get('selection')
      }
      this.getActiveElement()
    },
    getActiveElement() {
      // 初始第一个选中元素 bpmn:Process
      this.initFormOnChanged(null)
      this.bpmnModeler.on('import.done', () => {
        this.initFormOnChanged(null)
      })
      // 监听选择事件，修改当前激活的元素以及表单
      this.bpmnModeler.on('selection.changed', ({ newSelection }) => {
        this.initFormOnChanged(newSelection[0] || null)
      })
      this.bpmnModeler.on('element.changed', ({ element }) => {
        // 保证 修改 "默认流转路径" 类似需要修改多个元素的事件发生的时候，更新表单的元素与原选中元素不一致。
        if (element && element.id === this.elementId) {
          this.initFormOnChanged(element)
        }
      })
    },
    // 初始化数据
    initFormOnChanged(element) {
      let activatedElement = element
      if (!activatedElement) {
        activatedElement =
          window.bpmnInstances.elementRegistry.find((el) => el.type === 'bpmn:Process') ??
          window.bpmnInstances.elementRegistry.find((el) => el.type === 'bpmn:Collaboration')
      }
      if (!activatedElement) return
      Log.printBack(
        `select element changed: id: ${activatedElement.id} , type: ${activatedElement.businessObject.$type}`
      )
      Log.prettyInfo('businessObject', activatedElement.businessObject)
      window.bpmnInstances.bpmnElement = activatedElement
      // eslint-disable-next-line vue/no-undef-properties
      this.bpmnElement = activatedElement
      this.elementId = activatedElement.id
      this.elementType = activatedElement.type.split(':')[1] || ''
      this.elementBusinessObject = activatedElement.businessObject
      this.conditionFormVisible = Boolean(
        this.elementType === 'SequenceFlow' &&
          activatedElement.source &&
          activatedElement.source.type.indexOf('StartEvent') === -1
      )
      this.formVisible = this.elementType === 'UserTask' || this.elementType === 'StartEvent'
    },
    beforeUnmount() {
      window.bpmnInstances = null
    }
  }
}
</script>

<style lang="less" scoped>
.process-panel__container {
  box-sizing: border-box;
  box-shadow: 0 0 8px #cccccc;
  transition: all 0.3s;
  display: inline-grid;
  max-height: 100%;
  //overflow-y: hidden;
  //grid-template-columns: 20px auto;
  position: relative;
}

.process-panel__container.hide {
  transition: all 0.3s;
  margin-right: -480px;
  padding: 0;
  max-height: 100%;
}

.process-panel__container .process-panel-box {
  overflow-y: auto;
  max-height: 100vh;
  padding: 14px;
}

.process-panel__container .switch-btn {
  background: #fff;
  cursor: pointer;
  width: 24px;
  height: 24px;
  position: absolute;
  top: 50%;
  right: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  border: 1px solid rgb(239, 239, 245);
  box-shadow: 0 2px 4px 0px rgba(0, 0, 0, 0.06);
  color: rgb(51, 54, 57);
  transform: translateX(50%) translateY(-50%);
  z-index: 1;
  left: -23px;

  .show-btn:hover {
  }

  .hide-btn {
  }
}

.process-panel__container.hide .switch-btn {
  transition: all 0.3s;
  left: -40px;
}

.process-panel__container .switch-btn:hover {
  border-color: #1890ff;
}

.panel-tab__title {
  font-weight: 600;
  padding: 0 8px;
  font-size: 1.1em;
  line-height: 1.2em;

  i {
    margin-right: 8px;
    font-size: 1.2em;
  }
}

.panel-tab__content {
  width: 100%;
  box-sizing: border-box;
  border-top: 1px solid #eeeeee;
  padding: 8px 16px;

  .panel-tab__content--title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 8px;

    span {
      flex: 1;
      text-align: left;
    }
  }
}

//.element-property {
//  width: 100%;
//  display: flex;
//  align-items: flex-start;
//  margin: 8px 0;
//  .element-property__label {
//    display: block;
//    width: 90px;
//    text-align: right;
//    overflow: hidden;
//    padding-right: 12px;
//    line-height: 32px;
//    font-size: 14px;
//    box-sizing: border-box;
//  }
//  .element-property__value {
//    flex: 1;
//    line-height: 32px;
//  }
//  .a-form-item {
//    width: 100%;
//    margin-bottom: 0;
//    padding-bottom: 18px;
//  }
//}
.list-property {
  flex-direction: column;

  .element-listener-item {
    width: 100%;
    display: inline-grid;
    grid-template-columns: 16px auto 32px 32px;
    grid-column-gap: 8px;
  }

  .element-listener-item + .element-listener-item {
    margin-top: 8px;
  }
}

.listener-filed__title {
  display: inline-flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;

  span {
    // width: 200px;
    text-align: left;
    font-size: 14px;
  }

  i {
    margin-right: 8px;
  }
}

.element-drawer__button {
  margin-top: 8px;
  width: 100%;
  display: inline-flex;
  justify-content: space-around;
}

.element-drawer__button > .a-button {
  width: 100%;
}

.a-collapse-panel__content {
  padding-bottom: 0;
}

.a-input.is-disabled .a-input__inner {
  color: #999999;
}

.a-form-item.a-form-item--mini {
  margin-bottom: 0;

  & + .a-form-item {
    margin-top: 16px;
  }
}
</style>
