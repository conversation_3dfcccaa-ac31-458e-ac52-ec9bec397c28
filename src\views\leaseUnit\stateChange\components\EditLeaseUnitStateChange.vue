<template>
  <a-drawer
    v-model:open="visible"
    class="edit-lease-unit-state-change-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '新建'}租赁单元状态变更`"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">变更基础信息</h4>
      <a-form ref="basicFormRef" :model="formData" :label-col="{ style: { width: '140px' } }" :rules="rules">
        <a-form-item label="物业管理公司" name="manageCompany">
          <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司" disabled></company-select>
        </a-form-item>

        <a-form-item label="变更目标状态" name="destStatus">
          <dict-select
            v-model="formData.destStatus"
            placeholder="请选择目标状态"
            code="CT_BASE_ENUM_LeaseUnit_BizStatus"
          ></dict-select>
        </a-form-item>

        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>

        <a-form-item label="变更说明" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="变更说明" :maxlength="255" :rows="4" show-count />
        </a-form-item>
      </a-form>
      <div class="flex justify-between items-center mb-4">
        <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">租赁单元</h4>
        <a-button
          v-if="
            formData.leaseUnitStateChangeReqBillEntryList && formData.leaseUnitStateChangeReqBillEntryList.length > 0
          "
          type="primary"
          @click="handleAddLeaseUnit"
        >
          <template #icon><i class="a-icon-plus"></i></template>
          添加单元
        </a-button>
      </div>
      <!-- 当有数据时显示表格 -->
      <a-table
        v-if="formData.leaseUnitStateChangeReqBillEntryList && formData.leaseUnitStateChangeReqBillEntryList.length > 0"
        class="lease-unit-table"
        :columns="columns"
        :data-source="formData.leaseUnitStateChangeReqBillEntryList"
        :pagination="false"
        row-key="id"
        :scroll="{ x: 1400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'leaseUnit_dictText'">
            <i class="a-icon-remove cursor-pointer mr-2 text-red-500" @click="handleDeleteLeaseUnit(record)"></i>
            {{ record.leaseUnit_dictText }}
          </template>
          <template v-if="column.dataIndex === 'bizStatus'">
            <a-tag color="success">{{ record.bizStatus_dictText }}</a-tag>
          </template>
          <template v-if="column.dataIndex === 'destStatusBeginDate'">
            <a-date-picker
              v-model:value="record.destStatusBeginDate"
              value-format="YYYY-MM-DD"
              placeholder="请选择日期"
              style="width: 100%"
            />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <span class="primary-btn" @click="handleViewLeaseUnitDetail(record)">单元详情</span>
          </template>
        </template>
      </a-table>

      <!-- 当没有数据时显示添加按钮 -->
      <a-button v-else type="primary" @click="handleAddLeaseUnit" class="w-full">
        <template #icon><i class="a-icon-plus"></i></template>
        添加租赁单元
      </a-button>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- 租赁单元选择弹窗 -->
  <f7-modal ref="f7ModalRef" f7-type="leaseUnit" multiple @update-value="handleLeaseUnitChange" />

  <!-- 租赁单元详情抽屉 -->
  <lease-unit-detail ref="leaseUnitDetailRef" readonly />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  addLeaseUnitStateChangeReqBill,
  editLeaseUnitStateChangeReqBill,
  submitLeaseUnitStateChangeReqBill,
  getLeaseUnitStateChangeReqBillEntryByMainId
} from '../apis'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import { useUserStore } from '@/store/modules/user'

const emits = defineEmits(['refresh'])

const store = useUserStore()

const visible = ref(false)
const confirmLoading = ref(false)
const basicFormRef = ref()
const f7ModalRef = ref(null)
const leaseUnitDetailRef = ref(null)

const formDataDefault = {
  id: undefined,
  manageCompany: undefined,
  destStatus: '',
  bizDate: undefined,
  remark: undefined,
  leaseUnitStateChangeReqBillEntryList: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  destStatus: [{ required: true, message: '请选择变更目标状态', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  remark: [{ required: true, message: '请输入变更说明', trigger: 'blur' }]
}

const columns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit_dictText', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'leaseUnitAddress', width: 160, ellipsis: true },
  { title: '租赁归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '原业务状态', dataIndex: 'bizStatus', width: 120 },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '业务日期', dataIndex: 'expireDate', width: 120 },
  { title: '目标状态开始日期', dataIndex: 'destStatusBeginDate', width: 180, fixed: 'right' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

/**
 * 打开编辑抽屉
 * @param {Object} data - 编辑数据
 */
const open = async (data) => {
  // 重置编辑模式状态
  Object.assign(formData, formDataDefault)
  formData.manageCompany = store.userInfo.currentCompany
  formData.bizDate = dayjs().format('YYYY-MM-DD')

  if (data && data.id) {
    Object.assign(formData, data)
    const { result } = await getLeaseUnitStateChangeReqBillEntryByMainId({ id: data.id })
    if (result && result.length > 0) {
      formData.leaseUnitStateChangeReqBillEntryList = result
    }
  }

  visible.value = true
}

/**
 * 添加租赁单元
 */
const handleAddLeaseUnit = () => {
  // 打开 F7Modal 选择弹窗，传入当前已选择的租赁单元列表
  f7ModalRef.value.open(formData.leaseUnitStateChangeReqBillEntryList)
}

/**
 * 查看租赁单元详情
 * @param {Object} record - 租赁单元记录
 */
const handleViewLeaseUnitDetail = (record) => {
  leaseUnitDetailRef.value.open(record)
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  if (formData.leaseUnitStateChangeReqBillEntryList && formData.leaseUnitStateChangeReqBillEntryList.length === 0) {
    message.error('请至少选择一个租赁单元')
    return
  }

  // 只有在正式保存时才进行表单验证
  if (!isTemporary) {
    await basicFormRef.value.validateFields()

    // 验证租赁单元的目标状态开始日期
    if (formData.leaseUnitStateChangeReqBillEntryList && formData.leaseUnitStateChangeReqBillEntryList.length > 0) {
      const unitsWithoutDate = formData.leaseUnitStateChangeReqBillEntryList.filter((unit) => !unit.destStatusBeginDate)
      if (unitsWithoutDate.length > 0) {
        message.error('请为所有租赁单元选择目标状态开始日期')
        return
      }
    }
  }

  confirmLoading.value = true

  const api = !isTemporary
    ? submitLeaseUnitStateChangeReqBill
    : formData.id
      ? editLeaseUnitStateChangeReqBill
      : addLeaseUnitStateChangeReqBill
  try {
    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '新建'
    message.success(`${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 保存表单
 */
const handleSave = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 删除租赁单元
 * @param {Object} record - 要删除的租赁单元记录
 */
const handleDeleteLeaseUnit = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除租赁单元"${record.leaseUnit_dictText}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // 从表单数据中移除该租赁单元
      const index = formData.leaseUnitStateChangeReqBillEntryList.findIndex((unit) => unit.id === record.id)
      if (index !== -1) {
        formData.leaseUnitStateChangeReqBillEntryList.splice(index, 1)
        message.success(`已删除租赁单元"${record.leaseUnit}"`)
      }
    }
  })
}

/**
 * 租赁单元选择确认
 * @param {Array} selectedRowKeys - 选中的租赁单元ID列表
 * @param {Array} selectedUnits - 选中的租赁单元列表
 */
const handleLeaseUnitChange = (selectedRowKeys, selectedUnits) => {
  formData.leaseUnitStateChangeReqBillEntryList = selectedUnits.map((unit) => ({
    ...unit,
    leaseUnit_dictText: unit.name,
    leaseUnit: unit.id,
    leaseUnitAddress: unit.detailAddress,
    destStatusBeginDate: undefined
  }))
  message.success(`已选择 ${selectedUnits.length} 个租赁单元`)
}

/**
 * 取消编辑
 */
const handleCancel = () => {
  visible.value = false
  basicFormRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  emits('refresh')
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.edit-lease-unit-state-change-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-date-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>
