<template>
  <a-modal
    v-model:open="visible"
    title="物业管理公司转移"
    :confirm-loading="confirmLoading"
    width="500px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="py-4">
      <a-form ref="formRef" :model="form" :rules="rules">
        <a-form-item label="新物业管理公司" name="newManageCompany" required>
          <company-select v-model="form.newManageCompany" placeholder="请选择物业管理公司" type="company" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { changeManageCompany } from '../apis'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const selectedAssets = ref([])

const form = ref({
  newManageCompany: undefined,
  houseOwnerList: []
})

const rules = {
  newManageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }]
}

/**
 * 打开弹窗
 */
const open = (assets) => {
  if (!assets || assets.length === 0) {
    message.warning('请选择要转移的资产')
    return
  }
  selectedAssets.value = assets || []
  visible.value = true
}

/**
 * 确认转移
 */
const handleOk = async () => {
  try {
    await formRef.value.validate()

    confirmLoading.value = true
    form.value.houseOwnerList = selectedAssets.value

    const res = await changeManageCompany(form.value)

    message.success(res.message || '物业管理公司转移成功')
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 取消
 */
const handleCancel = () => {
  emit('refresh')
  form.value.newManageCompany = undefined
  form.value.houseOwnerList = []
  visible.value = false
  confirmLoading.value = false
  formRef.value?.resetFields()
}

defineExpose({
  open
})
</script>
