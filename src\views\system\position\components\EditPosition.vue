<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑职务' : '新增职务'"
    width="600px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '84px' } }" autocomplete="off">
      <a-form-item label="职务名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入职务名称" :maxlength="20" />
      </a-form-item>
      <a-form-item label="职务编码" name="code">
        <a-input v-model:value="form.code" placeholder="请输入职务编码" :maxlength="64" />
      </a-form-item>
      <a-form-item label="所属公司" name="company">
        <company-select v-model="form.company" type="all" @change="onCompanyChange"></company-select>
      </a-form-item>
      <a-form-item label="所属部门" name="depart">
        <depart-select v-model="form.depart" :company-id="form.company" :disabled="!form.company"></depart-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { add, edit } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  id: '',
  name: '',
  code: '',
  company: '',
  depart: ''
})

const rules = {
  name: [{ required: true, message: '请输入职务名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入职务编码', trigger: 'blur' }],
  company: [{ required: true, message: '请选择所属公司', trigger: 'change' }],
  depart: [{ required: true, message: '请选择所属部门', trigger: 'change' }]
}

const onCompanyChange = () => {
  form.depart = ''
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.id = ''
  form.name = ''
  form.code = ''
  form.company = ''
  form.depart = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
