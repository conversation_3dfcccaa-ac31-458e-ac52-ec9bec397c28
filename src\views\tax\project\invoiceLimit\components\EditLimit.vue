<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑项目发票限额信息' : '新建项目发票限额信息'"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '140px' } }" autocomplete="off">
        <a-form-item label="开票类型" name="invoiceForm">
          <dict-select
            v-model="form.invoiceForm"
            code="CT_BASE_ENUM_ReceiveItemPaymentTypeRate_InvoiceForm"
          ></dict-select>
        </a-form-item>
        <a-form-item label="发票限额" name="limitAmount">
          <a-input-number
            v-model:value="form.limitAmount"
            :min="0"
            addon-after="元"
            :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
            placeholder="请输入发票限额"
            class="!w-full"
          />
        </a-form-item>
        <a-form-item label="备注" name="remark" class="!w-full">
          <a-textarea
            v-model:value="form.remark"
            placeholder="请输入备注(选填)"
            show-count
            :maxlength="255"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { add, edit, detail } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (manageCompany, id) => {
  visible.value = true
  form.manageCompany = manageCompany
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.invoiceForm = result.invoiceForm
  form.limitAmount = result.limitAmount
  form.remark = result.remark
  loading.value = false
}

const form = reactive({
  id: '',
  invoiceForm: '',
  limitAmount: '',
  remark: ''
})

const rules = {
  invoiceForm: [{ required: true, message: '请选择开票类型', trigger: 'change' }],
  limitAmount: [{ required: true, message: '请输入发票限额', trigger: 'change' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    form.id ? await edit(params) : await add(params)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.invoiceForm = ''
  form.limitAmount = ''
  form.remark = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-payment-type-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    .ant-form-item {
      width: 50%;
    }
  }
}
</style>
