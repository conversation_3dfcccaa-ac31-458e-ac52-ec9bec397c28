<template>
  <div>
    <div class="!mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item class="!mb-[10px] !w-[280px]" label="客户">
          <a-form-item-rest>
            <f7-select v-model="search.customerId" f7-type="customer" placeholder="请选择客户"></f7-select>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item class="!mb-[10px]" label="公司">
          <company-select
            v-model="search.collectionCompanyId"
            placeholder="请选择公司"
            class="!w-[280px]"
            @select="collectionCompanyIdSelectChange"
          ></company-select>
        </a-form-item>
        <a-form-item class="!mb-[10px]" label="月份">
          <a-date-picker
            class="w-[280px]"
            v-model:value="search.incomeBelongYm"
            picker="month"
            value-format="YYYY-MM"
            format="YYYY-MM"
            placeholder="请选择月份"
          />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" @click="onTableChange">查询</a-button>
          <a-button @click="searchReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <div class="flex justify-center relative pb-[24px]">
      <div class="h-[32px] text-[16px] font-bold flex items-center">
        <span v-if="search.collectionCompanyId && search.incomeBelongYm">
          {{ tableTitle }} - {{ search.incomeBelongYm }}账单
        </span>
      </div>
      <div class="absolute top-0 right-0">
        <a-button type="primary" ghost :loading="exportLoading" @click="handleExport">导出账单</a-button>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="defaultColumns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight }"
      :pagination="false"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    ></a-table>
    <div class="flex flex-col items-end mt-[20px] p-[20px] bg-[#F5F5F5] font-bold text-[14px] rounded-[8px]">
      <div>
        <span class="w-[120px] inline-block">含税金额合计：</span>
        <span class="w-[120px] text-end inline-block">{{ renderMoney(totalData.totalReceiveAmt, 2) }}</span>
      </div>
      <div class="mt-[10px] mb-[10px]">
        <span class="w-[120px] inline-block">已核销金额合计：</span>
        <span class="w-[120px] text-end inline-block">{{ renderMoney(totalData.totalConsumedAmt, 2) }}</span>
      </div>
      <div>
        <span class="w-[120px] inline-block">未核销金额合计：</span>
        <span class="w-[120px] text-end inline-block">{{ renderMoney(totalData.totalNotConsumedAmt, 2) }}</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage, exportExcel } from './apis'
import { message } from 'ant-design-vue'
import { renderMoney } from '@/utils/render'
const search = ref({
  column: 'createTime',
  order: 'desc',
  customerId: '',
  incomeBelongYm: '',
  collectionCompanyId: ''
})
const defaultColumns = [
  { title: '客户', dataIndex: 'customerName', width: 150, fixed: true, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentTypeName' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 150 },
  { title: '合同编号', dataIndex: 'contractNum', ellipsis: true },
  { title: '含税金额', dataIndex: 'receiveAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '已核销金额', dataIndex: 'consumedAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '未核销金额', dataIndex: 'notConsumedAmt', customRender: ({ text }) => renderMoney(text, 2) }
]

const { list, tableLoading, onTableList, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = () => {
  if (search.value.collectionCompanyId && search.value.incomeBelongYm) {
    return onTableList(search.value)
  }
  message.warning('请先搜索公司和月份！')
}
const tableTitle = ref('')
// 选择公司的回调
const collectionCompanyIdSelectChange = (id, data) => {
  tableTitle.value = data.departName
}
const totalData = computed(() => {
  const obj = {
    totalReceiveAmt: 0,
    totalConsumedAmt: 0,
    totalNotConsumedAmt: 0
  }
  list.value.forEach((item) => {
    obj.totalReceiveAmt += item.receiveAmt
    obj.totalConsumedAmt += item.consumedAmt
    obj.totalNotConsumedAmt += item.notConsumedAmt
  })
  return obj
})
// 重置
const searchReset = () => {
  Object.assign(search.value, { customerId: '', incomeBelongYm: '', collectionCompanyId: '' })
  onTableChange()
}

// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('账单查询数据导出.xls', { ...search.value, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
