{"id": "1", "number": "Customer A", "name": "test-name", "wyProject": "wyProject", "wyBuilding": "wyBuilding", "wyFloor": "wyFloor", "status": "ENABLE", "receiveDate": "2025-06-30", "shareType": "water", "feeType": "SelfUse", "operator": "", "operatorDepart": "", "dtUseQuantity": 10, "dtUseTotalAmount": 20, "ullageQuantity": 30, "ullageAmount": 40, "ullagePrice": 10, "serviceCenter": "02", "incomeBelongYm": "2025-02", "shareExplain": "", "remark": "", "createBy": "", "createTime": "", "updateBy": "", "updateTime": "", "auditBy": "", "auditTime": "", "attachmentIds": "", "ctrlUnit": "", "sourceBillId": "", "sourceBillEntryId": "", "delFlag": 0, "entries": [{"id": "1", "customer": "customerA", "totalWaterEleTableNum": "T000001", "subWaterEleTableNum": "S0000001", "collectionCompany": "test-isCompany", "detailAddress": "厦门市集美区", "doubleRate": 10, "leaseUnit": "100", "leaseArea": 20, "shareCoefficient": 2, "operator": "", "operatorDepart": "", "lastMeterReadQuantity": 200, "thisMeterReadQuantity": 400, "thisMeterReadDate": "2025-06-30", "actualUseQuantity": 200, "ullageQuantity": 200, "price": 10, "selfAmount": 2000, "unitShare": 10, "shareAmount": 10, "remission": 100, "totalAmount": 1900, "taxRate": 1, "taxAmount": 19, "containTaxTotalAmount": 1919, "isCompanyExpense": true, "parent": "", "remark": "", "sourceBillId": "", "sourceBillEntryId": "", "delFlag": 0, "seq": 0}]}