<template>
  <a-drawer
    v-model:open="visible"
    class="edit-rent-scheme-drawer common-drawer"
    :title="form.id ? '编辑招租方案' : '新建招租方案'"
    placement="right"
    :mask-closable="false"
    width="1072px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <circle-steps
        :current="current"
        :step-list="['基础信息', '租赁单元', '承租方案']"
        width="600px"
        class="mx-auto mb-[40px]"
      ></circle-steps>
      <step-1 ref="step1Ref" :form="form" v-show="current === 1"></step-1>
      <step-2 ref="step2Ref" :form="form" v-show="current === 2"></step-2>
      <step-3 ref="step3Ref" :form="form" v-show="current === 3"></step-3>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        :loading="confirmLoading"
        @click="handleSubmit"
        v-auth="'biz.leasemanage:ct_biz_rent_scheme:submit'"
      >
        提交
      </a-button>
      <a-button @click="handleChangeStep('previous')" v-show="current > 1">上一步</a-button>
      <a-button type="primary" @click="handleChangeStep('next')" v-show="current < 3">下一步</a-button>
      <a-button
        type="primary"
        :loading="saveLoading"
        ghost
        @click="handleSave"
        v-if="['', 'TEMP'].includes(form.status)"
      >
        暂存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, submit, detail, save, queryLeaseUnit } from '../apis.js'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import userStore from '@/store/modules/user.js'
import Step1 from './Step1.vue'
import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
import Decimal from 'decimal.js'

const emit = defineEmits(['refresh'])
const { userInfo } = userStore()

const step1Ref = ref()
const step2Ref = ref()
const step3Ref = ref()

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  } else {
    form.manageCompany = userInfo.value.currentCompany // 物业管理公司，自动带入用户所属公司
    form.operator = userInfo.value.id
    form.operatorDepart = userInfo.value.currentDepart
    form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD') // 业务日期默认取当天
  }
}

const current = ref(1)

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    if (key !== 'rentSchemeEntryList') {
      form[key] = result[key]
    }
  }
  const { result: list } = await queryLeaseUnit({ id: result.id })
  form.rentSchemeEntryList = list && list.length ? list : []
  loading.value = false
}

const form = reactive({
  id: '',
  number: '',
  title: '',
  status: '',
  bizStatus: '',
  manageCompany: '',
  bizDate: '',
  operator: '',
  operatorDepart: '',
  auditDocumentNo: '',
  reviewDocumentNo: '',
  publicStartTime: '',
  publicEndTime: '',
  rentType: '',
  publicDate: '',
  bearerObject: '',
  totalArea: '',
  referencePrice: '',
  limitPrice: '',
  price: '',
  rentMonths: '',
  priceIncrease: '',
  managerange: '',
  environmental: '',
  supporting: '',
  advantage: '',
  redecorateReq: '',
  otherReq: '',
  remark: '',
  attachmentIds: '',
  sourceBillId: '',
  sourceBillEntryId: '',
  rentSchemeEntryList: []
})

/**
 * 上一步/下一步
 * @param {string} type previous | next
 */
const handleChangeStep = async (type) => {
  if (type === 'previous') {
    current.value--
  } else {
    if (current.value === 1) {
      await step1Ref.value.validate()
    } else {
      if (!step2Ref.value.validate()) return
      // 计算总面积
      form.totalArea = form.rentSchemeEntryList
        .reduce((total, item) => {
          return total.plus(new Decimal(item.leaseArea || 0))
        }, new Decimal(0))
        .toFixed(2)
    }
    current.value++
  }
}

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  if (current.value === 2) {
    if (!step2Ref.value.validate(true)) return
  } else if (current.value === 3) {
    await step3Ref.value.validateFields()
  }
  try {
    saveLoading.value = true
    if (form.id) {
      await edit(getParams())
    } else {
      const { result } = await save(getParams())
      form.id = result.id
      form.number = result.number
      form.status = result.status
    }
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const getParams = () => {
  return {
    id: form.id,
    number: form.number,
    title: form.title,
    bizStatus: form.bizStatus,
    manageCompany: form.manageCompany,
    bizDate: form.bizDate,
    operator: form.operator,
    operatorDepart: form.operatorDepart,
    auditDocumentNo: form.auditDocumentNo,
    reviewDocumentNo: form.reviewDocumentNo,
    publicStartTime: form.publicStartTime,
    publicEndTime: form.publicEndTime,
    rentType: form.rentType,
    publicDate: form.publicDate,
    bearerObject: form.bearerObject,
    totalArea: form.totalArea,
    referencePrice: form.referencePrice,
    limitPrice: form.limitPrice,
    price: form.price,
    rentMonths: form.rentMonths,
    priceIncrease: form.priceIncrease,
    managerange: form.managerange,
    environmental: form.environmental,
    supporting: form.supporting,
    advantage: form.advantage,
    redecorateReq: form.redecorateReq,
    otherReq: form.otherReq,
    remark: form.remark,
    rentSchemeEntryList: form.rentSchemeEntryList.map((item) => ({
      ...item,
      id: '',
      leaseUnit: item.id
    }))
  }
}

// 检查所有步骤是否正确填写完成
const validateAllSteps = async () => {
  try {
    await step1Ref.value.validate()
  } catch {
    current.value = 1
    message.warning('请检查基础信息是否正确填写完整')
    return
  }
  if (!step2Ref.value.validate()) {
    current.value = 2
    return
  }
  try {
    await step3Ref.value.validate()
  } catch {
    current.value = 3
    message.warning('请检查承租方案是否正确填写完整')
    return
  }
  return true
}
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  const isValid = await validateAllSteps()
  if (!isValid) return
  try {
    confirmLoading.value = true
    await submit(getParams())
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (key !== 'rentSchemeEntryList') {
      form[key] = ''
    } else {
      form[key] = []
    }
  }
  step1Ref.value.clearValidate()
  step3Ref.value.clearValidate()
  current.value = 1
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-rent-scheme-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
}
</style>
