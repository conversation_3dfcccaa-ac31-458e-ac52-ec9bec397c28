# 开发规范和目录结构说明

### 目录结构

#### 1.public目录

一些静态资源可以放这里面，比如favicon.icon，或者pdfjs这种插件，public目录里的东西不会被vite打包处理，所以如果一些需要优化或加密的资源，不要放在这里面

#### 2.env文件

.env.development文件对应本地开发环境 .env.test文件对应生产测试环境 .env.production文件对应生产正式环境根据需要在文件里修改对象配置

#### 3.eslintrc-auto-import.json文件

该文件是通过unplugin-auto-import插件实现自动引入vue，vue-router，pinia的api，避免多次书写import语句，勿删

#### 4.src目录

1.apis目录放置请求的封装文件，以及一些公共的请求封装，比如，上传文件，下载文件等

2.assets目录放置一些静态资源，比如图片，字体文件，svg这类，需要经过vite打包优化的静态文件，都放在这里

3.components目录一些可供全局使用的通用组件，都写在这里

4.layouts目录页面布局组件

5.router目录路由管理，只有一些非左侧菜单页面，才在这里添加，属于左侧菜单页面的，通过管理后台菜单管理配置即可

6.store目录pinia状态管理，index.js文件不做更改，需要什么模块，往modules里面添加

7.styles目录iconfont.less：阿里巴巴矢量图标库index.less：一些全局样式的封装reset.less：这个文件实际上是ant-design-vue/dist/reset.css，该项目为了做一些小变更，把reset.css文件复制到了这个目录下，开发过程中，不应该去动到这个reset.less文件tailwind.css：tailwindcss配置文件，必须用.css，不能用.less

8.views目录存放视图，一般来说，一个功能模块对应一个目录，比如，有一个新闻模块，有增删改查，那目录结构如下：news news/list: 新闻列表页 news/detail: 新闻详情页 news/components: 有关组件目录 news/apis: 新闻相关接口地址

9.utils用于封装一些常用的工具类

# 开发规范说明

## 1.文件和目录的命名规范

对于目录的命名，统一使用驼峰命名法

对于文件的命名，非vue文件，视情况而定，一般是驼峰命名法，其次是横杠命名对于vue文件，如果这个vue文件，将作为路由来进行渲染，比如，列表页list，详情页detail，那就是小驼峰命名法；如果这个vue文件是作为组件要被引入使用的，务必使用大驼峰命名法，如HelloWorld.vue

## 2.views里每个目录的规范

功能模块与功能模块之间，需要用目录具体区分开，每一个功能都有自己对应的，路由，组件，接口文档，以一个培训功能模块为例，培训功能包含培训班管理，培训需求管理，那总体目录结构应该如下:

- training
- class
- apis
- components
- list.vue
- detail.vue
- demand
- apis
- components
- list.vue
- detail.vue

不应该出现

- training
- apis(这个apis里，既包含了培训班的接口，又包含了培训需求管理的接口)
- components(这个components里，既放了培训班管理需要的组件，又放了培训需求管理需要的组件)
- classList.vue
- classDetail.vue
- demandList.vue
- demandDetail.vue

这样分配的缺点是，一个是功能模块之间没有明确区分，比较混乱；另一个是路由地址看着比较不直观

## 3.css的书写

本项目已集成了tailwindcss，因此不应该书写过多的自定义样式只有三种情况需要书写自定义样式

1.需要更改ant-design-vue组件的样式

2.如果只书写tailwindcss的类名，将会写超过两行的类样式，比如

```javascript
class="border border-primary border-solid px-[10px] transition-colors cursor-pointer bg-white text-primary..."
```

这种情况下，才应该封装一个自定义样式，单独提取出来

3.很多地方需要用到同样的样式，则应该单独封装一个自定义样式，放到index.less中

此外，在vue组件中，都应该使用scoped，避免样式影响。除非你需要更改ant-design-vue的某些组件的样式，才不使用scoped，但是也应该做好最外层的类样式封装，不可以直接在<style></style>里面直接更改ant-design-vue组件的样式，比如

```vue
<style>
错误 .ant-button {
  width: 100px;
}

正确 .my-class-name {
  .ant-button {
    width: 100px;
  }
}
</style>
```

## 4.vue组件的规范点

vue单文件组件的代码结构应该遵循如下组织顺序

```vue
<template></template>

<script setup>
// import 代码块

defineProps()

defineEmits()

// 引入各种 hooks
useRouter()
useStore()

// 各种业务逻辑
const logicFuc = () => {}

// 生命周期管理
watchEffect()

onMounted()

// 导出的内容
defineExpose({})
</script>

<style scoped lang="less"></style>
```

理论上，一个vue组件的代码不太应该超过500行，超过500行的话，尽量抽出来单独封装，便于阅读除非一些特殊的情况才允许超过500行，比如说，表单字段有上百个的组件

此外，因为已经使用了unplugin-auto-import插件，自动导入了vue，vue-router，pinia，所以页面中不应该再出现import { ref } from 'vue'这样的语句

## 5.图标引用

不要去专门下载图标的依赖，比如下载@ant-design/icons-vue统一使用阿里巴巴矢量图标库的图标即可，没有的话让UI去加，没有必要为了使用几个图标，去单独安装一个相关的依赖至于设计稿上的一些彩色图标，那个需要下载为svg文件，或者复制svg代码即可

本项目使用阿里巴巴图标时，只需使用类名a-icon-xxx即可，不需要再冗余写成iconfont icon-xxx

## 6.关于魔法数字

有时候，对于一些类型，后端会定义为数字表示，比如，1=一级菜单 2=子菜单 3=按钮/权限为了提高代码阅读性和可维护性，应该在src/store/modules/dict下，声明相关常量

```javascript
state: () => ({
  dict: {},
  menuType: {
    PARENT_MENU: 1,
    CHILD_MENU: 2,
    PERMISSION: 3
  }
}),
```

## 7.关于src/components这个目录下的组件的使用

因为已经在vite里配置了组件的自动导入，所以使用src/components这个目录中的组件时，直接在template模板中使用即可，不需要再写import语句来导入组件
