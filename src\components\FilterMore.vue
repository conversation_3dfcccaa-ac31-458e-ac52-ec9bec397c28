更多筛选组件
<template>
  <a-popover
    placement="bottomRight"
    :arrow="false"
    trigger="click"
    overlay-class-name="filter-more-overlay"
    v-model:open="open"
  >
    <template #content>
      <section :style="{ width }">
        <div class="max-h-[50vh] overflow-y-auto no-scrollbar">
          <div v-for="item in searchList" :key="item.name" class="flex items-center mb-[12px] last-of-type:mb-[0]">
            <span :style="{ width: labelWidth }" class="line-clamp-1 shrink-0">{{ item.label }}</span>
            <s-input
              v-model="paramsTemp[item.name]"
              :show-search-icon="false"
              :placeholder="item.placeholder || ''"
              v-if="item.type === 's-input'"
            ></s-input>
            <a-input
              v-model:value="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              v-else-if="item.type === 'input'"
              allow-clear
            ></a-input>
            <dict-select
              v-model="paramsTemp[item.name]"
              :code="item.code"
              :placeholder="item.placeholder || ''"
              v-else-if="item.type === 'dict-select'"
            ></dict-select>
            <a-select
              v-model:value="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :options="item.options"
              show-search
              allow-clear
              :filter-option="(input, option) => filterOption(input, option, item)"
              :field-names="item.fieldNames || { label: 'name', value: 'id' }"
              v-else-if="item.type === 'a-select'"
            ></a-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="item.asyncFn"
              :field-names="item.fieldNames || { label: 'name', value: 'id' }"
              v-else-if="item.type === 'api-select'"
              @change="valueChange(paramsTemp[item.name], item.name)"
            ></api-select>
            <!-- <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="getUserFn"
              :field-names="{ label: 'realname', value: 'id' }"
              v-else-if="item.type === 'user-select'"
            ></api-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="getCustomerFn"
              :field-names="{ label: 'name', value: 'id' }"
              v-else-if="item.type === 'customer-select'"
            ></api-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="getLeaseUnitFn"
              :field-names="{ label: 'name', value: 'id' }"
              v-else-if="item.type === 'lease-unit-select'"
            ></api-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="getContractFn"
              :field-names="{ label: 'contractNumber', value: 'id' }"
              v-else-if="item.type === 'contract-select'"
            ></api-select>
            <api-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="getWaterElectricityFn"
              :field-names="{ label: 'name', value: 'id' }"
              v-else-if="item.type === 'water-electricity-select'"
            ></api-select> -->
            <api-page-select
              v-else-if="
                [
                  'user-select',
                  'customer-select',
                  'lease-unit-select',
                  'contract-select',
                  'water-electricity-select'
                ].includes(item.type)
              "
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="requestObj[item.type]"
              :field-names="{
                label:
                  item.type === 'user-select'
                    ? 'realname'
                    : item.type === 'contract-select'
                      ? 'contractNumber'
                      : 'name',
                value: 'id'
              }"
              :mode="item.mode"
            ></api-page-select>

            <a-date-picker
              v-model:value="paramsTemp[item.name]"
              value-format="YYYY-MM-DD"
              v-bind="item"
              style="width: 100%"
              :placeholder="item.placeholder || ''"
              v-else-if="item.type === 'date'"
              allow-clear
            ></a-date-picker>
            <a-tree-select
              v-model:value="paramsTemp[item.name]"
              show-search
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :placeholder="item.placeholder || ''"
              allow-clear
              :tree-data="item.treeData || []"
              :field-names="item.fieldNames || { label: 'name', value: 'id', children: 'children' }"
              :tree-node-filter-prop="item.treeNodeFilterProp || 'name'"
              v-else-if="item.type === 'tree-select'"
            ></a-tree-select>
            <depart-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              allow-clear
              v-else-if="item.type === 'depart-select'"
            ></depart-select>
            <company-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              allow-clear
              :type="item.companyType || 'user'"
              v-else-if="item.type === 'company-select'"
            ></company-select>
            <api-tree-select
              v-model="paramsTemp[item.name]"
              :placeholder="item.placeholder || ''"
              :async-fn="item.asyncFn"
              :tree-node-filter-prop="item.treeNodeFilterProp || 'name'"
              v-else-if="item.type === 'api-tree-select'"
            ></api-tree-select>
            <a-cascader
              v-model:value="paramsTemp[item.name]"
              :options="item.options"
              :placeholder="item.placeholder || ''"
              :field-names="item.fieldNames || { label: 'name', value: 'id', children: 'children' }"
              v-else-if="item.type === 'cascader'"
              allow-clear
            ></a-cascader>
          </div>
        </div>
        <div class="flex items-center justify-between mt-[24px]">
          <span class="text-secondary cursor-pointer transition-colors hover:text-primary" @click="handleReset">
            <i class="a-icon-reset mr-[4px]"></i>
            清空
          </span>
          <div>
            <a-button class="w-[88px]" @click="handleCancel">取消</a-button>
            <a-button class="w-[88px]" type="primary" @click="handleSave">筛选</a-button>
          </div>
        </div>
      </section>
    </template>
    <a-button class="ml-[16px]">
      <i class="a-icon-filter"></i>
      <span>筛选({{ filterCount }})</span>
    </a-button>
  </a-popover>
</template>

<script setup>
import { f7List as getUserList } from '@/views/system/user/apis'
import { f7List as getCustomerList } from '@/views/customer/manage/apis'
import { getF7List as leaseUnitF7List } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { f7List as contractList } from '@/views/contract/management/apis'
import { f7List as waterElectricity } from '@/views/waterElectricity/manage/apis/waterElectricity'

const { params, clearIgnoreKeys, statIgnoreKeys } = defineProps({
  width: { type: String, default: '290px' },
  labelWidth: { type: String, default: '80px' },
  params: { required: true, type: Object },
  searchList: { required: true, type: Array },
  statIgnoreKeys: { type: Array, default: () => [] }, // 在统计时，忽略计算的键值
  clearIgnoreKeys: { type: Array, default: () => [] } // 在清空时，忽略清空的键值
})

const emit = defineEmits(['query', 'filterItemChange'])

// 当前账号所属的公司下的数据由后端来控制
// const getUserFn = () => getUserList({ pageNo: 1, pageSize: 1000 })
// const getCustomerFn = () => getCustomerList({ pageNo: 1, pageSize: 1000 })
// const getLeaseUnitFn = () => leaseUnitF7List({ pageNo: 1, pageSize: 1000 })
// const getContractFn = () => contractList({ pageNo: 1, pageSize: 1000 })
// const getWaterElectricityFn = () => waterElectricity({ pageNo: 1, pageSize: 1000 })
const requestObj = ref({
  'customer-select': getCustomerList,
  'user-select': getUserList,
  'lease-unit-select': leaseUnitF7List,
  'contract-select': contractList,
  'water-electricity-select': waterElectricity
})
const open = ref(false)
const paramsTemp = reactive({})

/**
 * 计算筛选条件数量
 */
const filterCount = computed(() => {
  let count = 0
  for (const key in params) {
    if (!['column', 'order'].concat(statIgnoreKeys).includes(key)) {
      if (typeof params[key] === 'boolean' || (params[key] && params[key].length)) {
        count++
      }
    }
  }
  return count
})

const handleReset = () => {
  for (const key in paramsTemp) {
    if (!clearIgnoreKeys.includes(key)) {
      paramsTemp[key] = ''
      params[key] = undefined
    }
  }
}

const handleCancel = () => {
  handleReset()
  open.value = false
  emit('query')
}

const handleSave = () => {
  for (const key in paramsTemp) {
    if (paramsTemp[key] && paramsTemp[key].length) {
      params[key] = paramsTemp[key]
    } else {
      params[key] = typeof paramsTemp[key] === 'boolean' ? paramsTemp[key] : undefined
    }
  }
  open.value = false
  emit('query')
}

const filterOption = (input, option, item) => {
  return option[(item.fieldNames && item.fieldNames.label) || 'name'].toLowerCase().indexOf(input.toLowerCase()) >= 0
}

watchEffect(() => {
  for (const key in params) {
    if (!['column', 'order'].includes(key)) {
      paramsTemp[key] = params[key] === undefined ? '' : params[key]
    }
  }
})

// api-select 值改变的回调
const valueChange = (id, name) => {
  emit('filterItemChange', id, name)
}
</script>

<style lang="less">
.filter-more-overlay {
  .ant-popover-inner {
    padding: 24px;
  }
  .ant-form-item {
    margin-bottom: 12px;
  }
}
</style>
