<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="`角色权限配置[${roleInfo.roleName}]`"
    placement="right"
    width="720px"
  >
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[20px]">
        <a-button size="medium" type="primary" @click="handleCheckedAll(true)">选择全部</a-button>
        <a-button size="medium" @click="handleCheckedAll(false)">取消全选</a-button>
        <span class="text-[18px] mx-[10px]">/</span>
        <a-button size="medium" type="primary" @click="handleExpandAll(true)">展开全部</a-button>
        <a-button size="medium" @click="handleExpandAll(false)">折叠全部</a-button>
        <span class="text-[18px] mx-[10px]">/</span>
        <a-button size="medium" type="primary" @click="setCheckStrictly(false)">层级关联</a-button>
        <a-button size="medium" @click="setCheckStrictly(true)">层级独立</a-button>
      </div>
      <a-tree
        v-model:expanded-keys="expandedKeys"
        v-model:checked-keys="checkedKeys"
        checkable
        :check-strictly="checkStrictly"
        :tree-data="treeData"
        :selectable="false"
        :field-names="{ title: 'slotTitle' }"
        :height="treeHeight"
      >
        <template #title="{ slotTitle, data }">
          <span>{{ slotTitle }}</span>
          <a-tooltip v-if="data.ruleFlag === 1">
            <template #title>数据规则配置</template>
            <span class="ml-[6px]" @click="handleConfigDataRule(data)">
              <i class="a-icon-key text-primary"></i>
            </span>
          </a-tooltip>
        </template>
      </a-tree>
    </a-spin>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSave(false)">仅保存</a-button>
      <a-button type="primary" :loading="loading" @click="handleSave(true)">保存并关闭</a-button>
    </template>
    <data-rule-config ref="dataRuleConfigRef"></data-rule-config>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { queryTreeList, queryRolePermission, saveRolePermission } from '../apis'
import DataRuleConfig from './DataRuleConfig.vue'

const visible = ref(false)

const roleInfo = reactive({})

const open = (data) => {
  Object.assign(roleInfo, data)
  visible.value = true
  loadTreeData()
}

const treeData = ref([])
let allIds = []
const loadTreeData = async () => {
  loading.value = true
  const { result } = await queryTreeList()
  treeData.value = result.treeList
  allIds = result.ids
  await loadPermissionByRole()
  loading.value = false
}

const lastPermissionIds = ref([])
const loadPermissionByRole = async () => {
  const { result } = await queryRolePermission({ roleId: roleInfo.id })
  checkedKeys.value = result
  lastPermissionIds.value = [...result]
}

const expandedKeys = ref([])
const checkedKeys = ref([])

const handleCheckedAll = (checked) => {
  if (checked) {
    if (checkStrictly.value) {
      checkedKeys.value = {
        checked: allIds,
        halfChecked: []
      }
    } else {
      checkedKeys.value = allIds
    }
  } else {
    if (checkStrictly.value) {
      checkedKeys.value = {
        checked: [],
        halfChecked: []
      }
    } else {
      checkedKeys.value = []
    }
  }
}

const handleExpandAll = (expand) => {
  expandedKeys.value = expand ? allIds : []
}

/**
 * checkStrictly=false时，父子节点互相关联，此时checkedKeys是一个普通的一维数组string[]
 * checkStrictly=true时，父子节点互相独立，此时checkedKeys = { checked: string[] }
 */
const checkStrictly = ref(false)
const setCheckStrictly = (value) => {
  checkStrictly.value = value
}

const loading = ref(false)
const handleSave = async (needClose) => {
  if (loading.value) return
  try {
    loading.value = true
    const ids = checkedKeys.value.checked ? checkedKeys.value.checked.join(',') : checkedKeys.value.join(',')
    await saveRolePermission({
      lastpermissionIds: lastPermissionIds.value.join(','),
      permissionIds: ids,
      roleId: roleInfo.id
    })
    loading.value = false
    message.success('保存成功')
    if (needClose) {
      handleCancel()
    }
  } catch {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
  checkStrictly.value = false
}

const dataRuleConfigRef = ref()
const handleConfigDataRule = (data) => {
  dataRuleConfigRef.value.open({
    roleId: roleInfo.id,
    roleName: roleInfo.roleName,
    ...data
  })
}

const treeHeight = ref(500)
onMounted(() => {
  treeHeight.value = document.documentElement.offsetHeight - 238
})

defineExpose({ open })
</script>
