import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/receiveItemInvoiceLimit/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/receiveItemInvoiceLimit/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemInvoiceLimit/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemInvoiceLimit/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemInvoiceLimit/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemInvoiceLimit/edit',
    data
  })
}
export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/receiveItemInvoiceLimit/deleteBatch',
    params
  })
}
