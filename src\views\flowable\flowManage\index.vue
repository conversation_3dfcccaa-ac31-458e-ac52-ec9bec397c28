<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.name"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'version'">
          <a-tag color="blue">V{{ record.version }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">详情</span>
          <a-dropdown v-if="record.taskId">
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleStop(record)">取消申请</a-menu-item>
                <a-menu-item @click="handleJump(record)">跳转</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <jump-flow ref="jumpFlowRef" @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })" />
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, stopProcess } from './apis.js'
import { Modal, message } from 'ant-design-vue'
import JumpFlow from './components/JumpFlow.vue'

const params = reactive({
  processInstanceId: undefined,
  name: undefined,
  title: undefined,
  bizBillName: undefined,
  category: undefined,
  todoUsers: undefined
})

const searchList = [
  { label: '流程实例编号', name: 'processInstanceId', type: 's-input' },
  { label: '标题', name: 'title', type: 's-input' },
  { label: '业务单据名称', name: 'bizBillName', type: 's-input' },
  { label: '流程公司', name: 'category', type: 's-input' },
  { label: '当前任务处理人', name: 'todoUsers', type: 's-input' }
]

const defaultColumns = [
  { title: '流程实例编号', dataIndex: 'processInstanceId', width: 120, fixed: 'left' },
  { title: '流程名称', dataIndex: 'name', width: 100 },
  { title: '标题', dataIndex: 'title', width: 180 },
  { title: '业务单据名称', dataIndex: 'bizBillName', width: 180 },
  { title: '流程公司', dataIndex: 'category_dictText', width: 180 },
  { title: '流程版本', dataIndex: 'version', width: 180 },
  { title: '当前节点', dataIndex: 'taskName', width: 180 },
  { title: '当前任务处理人', dataIndex: 'todoUsers', width: 180 },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

const handleStop = (data) => {
  if (!data.taskId) {
    message.warning('流程未启动或已执行完成，取消申请失败!')
    return
  }
  Modal.confirm({
    title: `确定取消申请${data.name}流程？`,
    content: '',
    centered: true,
    onOk: async () => {
      await stopProcess({ procInsId: data.processInstanceId })
      refresh()
    }
  })
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

const jumpFlowRef = ref()
const handleJump = (data) => {
  jumpFlowRef.value.open(data)
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

onMounted(() => {
  onTableChange()
})
</script>
