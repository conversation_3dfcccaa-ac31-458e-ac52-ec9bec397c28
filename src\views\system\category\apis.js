import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/sys/category/rootList',
    params
  })
}

export const tree = (params) => {
  return request({
    method: 'get',
    url: '/sys/category/loadTreeRoot',
    params
  })
}
export const add = (data) => {
  return request({
    method: 'post',
    url: '/sys/category/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/sys/category/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/sys/category/deleteBatch',
    params
  })
}
