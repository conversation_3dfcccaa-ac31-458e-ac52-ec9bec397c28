<template>
  <div class="tag-list-container">
    <router-link class="home-btn" to="/home">
      <i class="a-icon-home"></i>
    </router-link>
    <div class="h-full flex">
      <div class="direction-btn mr-[12px]" v-show="showArrowBtn" @click="handleScroll('left')">
        <i class="a-icon-arrow-left"></i>
      </div>
      <div class="flex h-full flex-1 overflow-hidden tag-list">
        <span
          v-for="(item, index) in tagList"
          :key="item.path"
          class="tag-item"
          :class="{ active: item.path === currentTag.path }"
          @click="toPath(item.path)"
          @contextmenu="handleContextMenu($event, item)"
        >
          <span>{{ item.meta.title }}</span>
          <i class="a-icon-close" @click.stop="handleRemove(item, index)"></i>
        </span>
      </div>
      <div class="direction-btn ml-[12px]" v-show="showArrowBtn" @click="handleScroll('right')">
        <i class="a-icon-arrow-right"></i>
      </div>
    </div>
    <div class="context-menu" :style="`left: ${contextMenuX}px; top: ${contextMenuY}px`" v-if="showContentMenu">
      <div class="context-item" @click="closeOther">关闭其他</div>
      <div class="context-item" @click="closeAll">全部关闭</div>
    </div>
  </div>
</template>

<script setup>
import { useTagStore } from '@/store/modules/tags'

const route = useRoute()
const router = useRouter()

const tagStore = useTagStore()

const currentTag = computed(() => tagStore.currentTag)

const tagList = computed(() => tagStore.tagList)

const toPath = (path) => {
  router.push({ path })
}

const handleRemove = (item, index) => {
  if (item.path === currentTag.value.path) {
    if (tagList.value.length === 1) {
      router.push({ path: '/home' })
    } else {
      router.push(tagList.value[index - 1] || tagList.value[index + 1])
    }
  }
  tagStore.closeTag(index)
}

const showArrowBtn = ref(false)
const showBtn = () => {
  const list = Array.from(tagListDom.querySelectorAll('.tag-item'))
  const width = list.reduce((total, item) => total + item.offsetWidth + 12, 0)
  // 132=主页按钮+左箭头按钮+右箭头按钮的宽度以及间距总和
  showArrowBtn.value = width - 12 > container.offsetWidth - 132
  judgeScroll()
}

// 判断是否需要将当前页签滚动到可视区域内
const judgeScroll = async () => {
  await nextTick()
  const { left, right } = tagListDom.getBoundingClientRect()
  const active = container.querySelector('.tag-item.active')
  if (active) {
    const rectInfo = active.getBoundingClientRect()
    // 不在可视区域内，才需要滚动
    if (!(rectInfo.left > left && rectInfo.right < right)) {
      active.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }
}

const handleScroll = (direction) => {
  if (direction === 'left') {
    if (tagListDom.scrollLeft > 0) {
      smoothScrollTo(tagListDom, tagListDom.scrollLeft - 100, 200)
    }
  } else {
    if (tagListDom.scrollLeft < tagListDom.scrollWidth - tagListDom.offsetWidth) {
      smoothScrollTo(tagListDom, tagListDom.scrollLeft + 100, 200)
    }
  }
}

// 让滚动圆滑一点，不那么僵硬
const smoothScrollTo = (element, targetScrollLeft, duration) => {
  const startScrollLeft = element.scrollLeft
  const distance = targetScrollLeft - startScrollLeft
  let startTime = null
  function animation(currentTime) {
    if (startTime === null) startTime = currentTime
    const elapsedTime = currentTime - startTime
    const progress = Math.min(elapsedTime / duration, 1)
    // 使用 easeInOutQuad 缓动函数
    const easedProgress = progress < 0.5 ? 2 * progress * progress : -1 + (4 - 2 * progress) * progress
    element.scrollLeft = startScrollLeft + distance * easedProgress
    if (elapsedTime < duration) {
      window.requestAnimationFrame(animation)
    }
  }
  window.requestAnimationFrame(animation)
}

watch(
  () => route,
  async () => {
    await nextTick()
    const active = container.querySelector('.tag-item.active')
    if (active) {
      active.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  },
  { deep: true }
)

watch(
  () => tagList,
  async () => {
    await nextTick()
    showBtn()
  },
  { deep: true }
)

const showContentMenu = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
let contextItem
const handleContextMenu = (e, item) => {
  if (tagList.value.length === 1) return
  contextItem = item
  e.preventDefault()
  e.stopPropagation()
  contextMenuX.value = e.clientX
  contextMenuY.value = e.clientY
  showContentMenu.value = true
}

const documentClick = (e) => {
  if (showContentMenu.value) {
    if (e.target.className !== 'context-item') {
      showContentMenu.value = false
    }
  }
}

const closeOther = () => {
  router.push({ path: contextItem.path })
  tagStore.closeOtherTag(contextItem)
  showContentMenu.value = false
}
const closeAll = () => {
  router.push({ path: '/home' })
  tagStore.closeAllTag()
  showContentMenu.value = false
}

let container
let tagListDom
let timer
const observer = new ResizeObserver(() => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    showBtn()
  }, 200)
})

onMounted(() => {
  container = document.querySelector('.tag-list-container')
  tagListDom = container.querySelector('.tag-list')
  observer.observe(container)
  document.addEventListener('click', documentClick)
})

onUnmounted(() => {
  document.removeEventListener('click', documentClick)
})
</script>

<style lang="less">
.tag-list-container {
  height: 32px;
  position: relative;
  background-color: #f7f8fa;
  padding-left: 44px;
  .home-btn {
    position: absolute;
    left: 0;
    top: 0;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--color-primary);
  }
  .tag-item {
    padding: 0 12px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
    background-color: #fff;
    border-radius: 8px;
    color: var(--color-secondary);
    position: relative;
    cursor: pointer;
    margin-right: 12px;
    transition:
      color 0.2s,
      background-color 0.2s;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      color: var(--color-primary);
    }
    &.active {
      color: #fff;
      background-color: var(--color-primary);
      .a-icon-close {
        color: #fff;
      }
    }
    .a-icon-close {
      font-weight: bold;
      color: var(--color-secondary);
      z-index: 10;
      cursor: pointer;
      margin-left: 4px;
    }
  }
  .direction-btn {
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 8px;
    color: var(--color-secondary);
    font-size: 14px;
    transition: color 0.2s;
    &:hover {
      color: var(--color-primary);
    }
  }
  .context-menu {
    position: fixed;
    z-index: 100;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
    background-color: #fff;
    border-radius: 8px;
  }
  .context-item {
    padding: 10px 15px;
    cursor: pointer;
    transition:
      background-color 0.2s,
      color 0.2s;
    &:first-child {
      border-bottom: 1px solid #eee;
      border-radius: 8px 8px 0 0;
    }
    &:last-child {
      border-radius: 0 0 8px 8px;
    }
    &:hover {
      color: var(--color-primary);
    }
  }
}
</style>
