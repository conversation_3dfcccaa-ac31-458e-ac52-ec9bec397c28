<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    title="水电计费单详情"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <a-dropdown>
        <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          <span>操作</span>
          <i class="a-icon-arrow-down ml-[4px]"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
              <div class="primary-btn" @click="handleEdit">编辑</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK'].includes(detail.status)">
              <div class="primary-btn" @click="handleDelete">删除</div>
            </a-menu-item>
            <a-menu-item v-if="detail.status === 'AUDITOK'">
              <div class="primary-btn" @click="handleUnAudit">反审核</div>
            </a-menu-item>
            <a-menu-item v-if="detail.status === 'AUDITING'">
              <div class="primary-btn" @click="handleAudit">审核</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">水电计费单</h2>
        <status-tag :dict-value="detail.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h4 class="text-[16px] font-bold mb-[12px]">基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-full">项目/楼栋/楼层: {{ projectInfo }}</span>
        <span class="w-[50%]">计费类型: {{ detail.feeType_dictText }}</span>
        <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-[50%]">分摊类别: {{ detail.shareType_dictText }}</span>
        <span class="w-[50%]">应收日期: {{ detail.receiveDate }}</span>
        <span class="w-[50%]">收入归属年月: {{ detail.incomeBelongYm }}</span>
        <span class="w-[50%]">经办人: {{ detail.operator_dictText }}</span>
        <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
        <span class="w-[50%]">服务处: {{ detail.serviceCenter_dictText }}</span>
        <span class="w-[50%]">待摊用量: {{ renderMoney(detail.dtUseQuantity, 4) }}</span>
        <span class="w-[50%]">待摊总额: {{ renderMoney(detail.dtUseTotalAmount) }}</span>
        <span class="w-[50%]">单价: {{ renderMoney(detail.price, 6) }}</span>
        <span class="w-full">分摊说明: {{ detail.shareExplain }}</span>
      </div>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[12px]">分摊明细</h4>
      <a-table
        :data-source="detailList"
        :columns="columns"
        row-key="id"
        :scroll="{ x: 3300, y: '45vh' }"
        :pagination="false"
      ></a-table>
    </a-spin>

    <template v-if="detail.status === 'AUDITING'" #footer>
      <a-button type="primary" @click="handleAudit">确认通过</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, queryWaterShareBillEntry } from '../apis.js'
import { renderMoney, renderBoolean } from '@/utils/render'

const emit = defineEmits(['edit', 'audit', 'remove', 'unAudit'])

const visible = ref(false)

const columns = [
  { title: '客户', dataIndex: 'customer_dictText', width: 160, fixed: 'left' },
  { title: '总表号', dataIndex: 'totalWaterEleTableNum' },
  { title: '分表号', dataIndex: 'subWaterEleTableNum_dictText' },
  { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '倍率', dataIndex: 'doubleRate', customRender: ({ text }) => renderMoney(text, 4) },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', ellipsis: true },
  {
    title: '面积(㎡)',
    dataIndex: 'leaseArea',
    width: 130,
    customRender: ({ text }) => renderMoney(text, 4)
  },
  { title: '分摊系数%', dataIndex: 'shareCoefficient', customRender: ({ text }) => renderMoney(text, 4) },
  { title: '业务员', dataIndex: 'operator' },
  { title: '部门', dataIndex: 'operatorDepart' },
  { title: '上次抄表数', dataIndex: 'lastMeterReadQuantity' },
  { title: '本次抄表数', dataIndex: 'thisMeterReadQuantity' },
  { title: '本次抄表日期', dataIndex: 'thisMeterReadDate' },
  { title: '实际用量', dataIndex: 'actualUseQuantity', customRender: ({ text }) => renderMoney(text, 4) },
  { title: '单价', dataIndex: 'price', customRender: ({ text }) => renderMoney(text, 6) },
  { title: '自用金额', dataIndex: 'selfAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '单位分摊', dataIndex: 'unitShare', customRender: ({ text }) => renderMoney(text) },
  { title: '公摊金额', dataIndex: 'shareAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text) },
  { title: '合计', dataIndex: 'totalAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '税率%', dataIndex: 'taxRate', customRender: ({ text }) => renderMoney(text, 4) },
  { title: '税金', dataIndex: 'taxAmount', customRender: ({ text }) => renderMoney(text) },
  {
    title: '含税合计',
    dataIndex: 'containTaxTotalAmount',
    width: 130,
    customRender: ({ text }) => renderMoney(text)
  },
  { title: '备注', dataIndex: 'remark', width: 160 },
  { title: '公司费用', dataIndex: 'isCompanyExpense', width: 160, customRender: ({ text }) => renderBoolean(text) }
]

const open = (id) => {
  visible.value = true
  loadData(id)
}

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([loadDetail(id), loadDetailList(id)])
  loading.value = false
}

const detail = reactive({})
const loadDetail = async (id) => {
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
}

const detailList = ref([])
const loadDetailList = async (id) => {
  const { result } = await queryWaterShareBillEntry({ id })
  detailList.value = result
}

const projectInfo = computed(() => {
  const building = detail.wyBuilding_dictText ? `/${detail.wyBuilding_dictText}` : ''
  const floor = detail.wyFloor_dictText ? `/${detail.wyFloor_dictText}` : ''
  return `${detail.wyProject_dictText}${building}${floor}`
})

const handleEdit = () => {
  handleClose()
  emit('edit', detail)
}

const handleAudit = () => {
  emit('audit', detail)
}

const handleUnAudit = () => {
  emit('unAudit', detail)
}

const handleDelete = () => {
  emit('remove', detail)
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open, visible, loadData, handleClose })
</script>
