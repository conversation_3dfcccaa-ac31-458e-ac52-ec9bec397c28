<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange()">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <!-- <s-input
          v-model="params.invoiceName"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input> -->
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange()"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <edit-rate ref="editRateRef" @refresh="refresh"></edit-rate>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('从租税率.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange()"
    ></common-import>
  </div>
</template>

<script setup>
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel } from './apis.js'
import EditRate from './components/EditRate.vue'
import { Modal, message } from 'ant-design-vue'

const props = defineProps({
  id: { type: String, default: '' }
})

const params = reactive({
  column: 'createTime',
  order: 'desc',
  id: undefined,
  customerType: undefined,
  houseType: undefined,
  pricedType: undefined,
  policyCode: undefined,
  monthRate: undefined,
  remark: undefined,
  createTime: undefined
})

const searchList = [
  {
    label: '客户类型',
    name: 'customerType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_CustomerType'
  },
  {
    label: '房产类型',
    name: 'houseType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_HouseOwner_HouseType'
  },
  {
    label: '定价类型',
    name: 'pricedType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Contract_PricedType'
  },
  { label: '月税率', name: 'monthRate', type: 's-input' },
  { label: '备注', name: 'remark', type: 's-input' },
  { label: '创建时间', name: 'createTime', type: 'date' }
]

const defaultColumns = [
  { title: '客户类型', dataIndex: 'customerType_dictText', width: 120 },
  { title: '房产类型', dataIndex: 'houseType_dictText', width: 160 },
  { title: '定价类型', dataIndex: 'pricedType_dictText', width: 180 },
  { title: '月税率', dataIndex: 'monthRate', width: 120, customRender: ({ text }) => text && `${text}%` },
  { title: '备注', dataIndex: 'remark', width: 120 },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const list = ref([])
const tableLoading = ref(false)

const onTableChange = async () => {
  params.id = props.id
  const { result } = await page(params)
  list.value = result
}

watch(
  () => props.id,
  (value) => {
    params.id = value
    if (!value) {
      return
    }
    onTableChange()
  },
  {
    immediate: true
  }
)

// let timer
// const handleInput = () => {
//   clearTimeout(timer)
//   timer = setTimeout(() => {
//     onTableChange()
//   }, 600)
// }

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRateRef = ref()
const handleAdd = () => {
  editRateRef.value.open({ parent: props.id })
}
const handleEdit = (data) => {
  editRateRef.value.open(data)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除款项税率信息？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

const refresh = () => {
  onTableChange()
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('从租税率数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
