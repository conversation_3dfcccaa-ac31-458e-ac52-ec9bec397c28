import js from '@eslint/js'
import globals from 'globals'
import pluginVue from 'eslint-plugin-vue'
import css from '@eslint/css'
import { defineConfig } from 'eslint/config'
import fs from 'node:fs'
import path from 'node:path'

const autoImportGlobals = JSON.parse(fs.readFileSync(path.resolve('./.eslintrc-auto-import.json'), 'utf-8'))

const noEmptyCatch = {
  meta: {
    type: 'problem',
    docs: {
      description: '禁止空的 catch 块或只包含 console 的 catch 块'
    },
    messages: {
      emptyCatch: 'catch 块为空或只包含 console 语句，应进行更有意义的处理。'
    },
    schema: [] // 无需额外配置
  },
  create(context) {
    return {
      CatchClause(node) {
        const body = node.body.body
        if (body.length === 0) {
          context.report({ node, messageId: 'emptyCatch' })
          return
        }
        // 只包含 console.log/console.error 等的情况
        const onlyConsoleStatements = body.every((statement) => {
          return (
            statement.type === 'ExpressionStatement' &&
            statement.expression.type === 'CallExpression' &&
            statement.expression.callee.type === 'MemberExpression' &&
            statement.expression.callee.object.name === 'console'
          )
        })
        if (onlyConsoleStatements) {
          context.report({ node, messageId: 'emptyCatch' })
        }
      }
    }
  }
}

export default defineConfig([
  { ignores: ['public', 'node_modules', 'dist'] },
  { files: ['**/*.{js,mjs,cjs,vue}'], plugins: { js }, extends: ['js/recommended'] },
  {
    files: ['**/*.{js,mjs,cjs,vue}'],
    languageOptions: { globals: { ...globals.browser, ...globals.node, ...autoImportGlobals.globals } }
  },
  pluginVue.configs['flat/essential'],
  { files: ['**/*.css'], plugins: { css }, language: 'css/css', extends: ['css/recommended'] },
  {
    files: ['**/*.{js,vue}'],
    plugins: {
      custom: {
        rules: {
          'no-empty-catch': noEmptyCatch
        }
      }
    },
    rules: {
      'custom/no-empty-catch': 'error'
    }
  },
  {
    rules: {
      semi: ['error', 'never'], // 不要分号
      quotes: ['error', 'single'], // 必须使用单引号，不允许双引号
      indent: ['error', 2, { SwitchCase: 1 }], // 缩进设为2个空格, switch中的case子句，缩进一个级别
      eqeqeq: 'error', // 强制使用===和!==
      'no-empty': 'error', // 禁止出现空语句块
      'arrow-spacing': 'error', // 箭头函数 => 前后需要有空格
      'no-duplicate-imports': 'error', // 禁止重复导入
      'no-useless-computed-key': 'error', // 禁止不必要的计算属性，如obj3={['a']: 1},其中['a']是不必要的，直接写'a'
      'no-var': 'error', // 要求使用let或const，而不是var
      'no-console': 'warn', // 使用console语句报警告
      'no-else-return': 'error', // 禁止在else前有return，return和else不能同时存在
      'no-multi-spaces': 'error', // 禁止出现多个空格，如===前后可以有一个空格，但是不能有多个空格
      'no-multi-str': 'error', // 禁止出现多行字符串，可以使用模板字符串换行
      'no-self-compare': 'error', // 禁止变量自身进行比较
      'object-shorthand': ['error', 'always'], // 要求对象字面量简写语法，如{ fun: function () {} }要写成{ fun () {} }
      'key-spacing': 'error', // 强制对象键值冒号后面有一个空格
      'comma-spacing': 'error', // 要求在逗号后面加个空格，禁止在逗号前面加一个空格
      'no-useless-concat': 'error', // 禁止没有必要的字符串拼接，如'a'+'b'应该写成'ab'
      'brace-style': ['error', '1tbs', { allowSingleLine: true }], // if/elseif/else左花括号要跟if..同行，右花括号要换行；或者全部同一行
      'comma-dangle': 'error', // 数组或对象的最后一个，不得加逗号
      'no-multiple-empty-lines': ['error', { max: 1 }], // 限制最多出现一个空行
      'no-whitespace-before-property': 'error', // 禁止属性前有空白，如console. log(obj['a'])，log前面的空白有问题
      'semi-spacing': 'error', // 强制分号后面有空格，如for (let i = 0; i <span 20; i++)
      'space-before-blocks': 'error', // 强制块（for循环/if/函数等）前面有一个空格，如for(...){}是错的，花括号前面要空格：for(...) {}
      'object-curly-spacing': ['error', 'always'], // 强制对象/解构赋值/import等花括号前后有空格
      'spaced-comment': 'error', // 强制注释（//或/*）后面要有一个空格
      'space-infix-ops': 'error', // 强制操作符（+-/*）前后有一个空格
      'operator-assignment': 'error', // 尽可能的简化赋值操作，如x=x+1 应简化为x+=1
      'object-property-newline': ['error', { allowAllPropertiesOnSameLine: true }], // 强制对象的属性在同一行或全换行
      'no-unneeded-ternary': 'error', // 禁止多余的三元表达式，如a === 1 ? true : false应缩写为a === 1
      'no-trailing-spaces': 'error', // 该规则禁止使用行尾空白（空格、tab 和其它 Unicode 空白字符）
      'multiline-comment-style': 'off', // 强制对多行注释使用特定风格
      // TODO function-paren-newline这个规则会和prettier有冲突，以prettier为准，因为prettier针对这个问题也能进行良好的格式化
      // 'function-paren-newline': 'error', // 强制函数括号内的参数一致换行或一致不换行
      'func-call-spacing': 'off', // 函数调用时，禁止函数名和括号之间有个空格，如sayHi ()是错误的
      'eol-last': 'error', // 强制文件的末尾有一个空行
      'computed-property-spacing': 'error', // 禁止在计算属性中出现空格，如obj[ 'a' ]是错的，obj['a']是对的
      'comma-style': 'error', // 要求逗号放在数组元素、对象属性或变量声明之后，且在同一行
      'block-spacing': 'error', // 强制函数/循环等块级作用域中的花括号内前后有一个空格（对象除外）
      'space-in-parens': 'error', // 禁止圆括号内的空格，如( 1 + 2 )是错的
      'require-await': 'error', // 禁止使用不带await的async表达式
      'no-case-declarations': 'off', // 关闭禁用在switch中使用let  const Function
      'no-extra-parens': [
        'error',
        'all',
        {
          ignoreJSX: 'all',
          conditionalAssign: false,
          returnAssign: false,
          nestedBinaryExpressions: false,
          enforceForArrowConditionals: false
        }
      ], // 禁止不必要的括号
      'no-await-in-loop': 'error', // 循环里不要有await
      'keyword-spacing': ['error', { before: true, after: true }], // 关键字前后都需要有空格，如if和else的前后都要加空格
      'dot-notation': 'error', // 要求对象访问使用使用点号，如obj.name不能写成obj['name]
      'no-empty-function': 'off', // 禁止出现空函数
      'no-extend-native': 'error', // 禁止扩展原生类型，如重写Object.prototype.hasOwnProperty = () => {}
      'no-floating-decimal': 'error', // 禁止浮点小数，const num = 0.5，不能写成const num = .5
      'no-implicit-coercion': 'error', // 禁止使用较短的符号实现类型转换，如num = 5，将num转为字符串，需写成num = String(5)，不能写成num = '' + 5
      'no-lone-blocks': 'error', // 禁用不必要的嵌套块
      'no-useless-return': 'error', // 禁止多余的return语句
      'array-bracket-spacing': ['error', 'never'], // 数组禁止使用空格，如[ { name: '' } ]，[]前后的空格是错的
      'func-name-matching': 'error', // 要求函数名与赋值给它们的变量名或属性名相匹配，如const say = function say () {}，两个say必须一致
      'space-unary-ops': 'error', // 一元操作符前面不能有空格，如num ++是错的，-- num是错的
      'prefer-const': ['error', { destructuring: 'all' }], // 要求使用 const 声明那些声明后不再被修改的变量
      'no-useless-rename': 'error', // 禁止在 import 和 export 和解构赋值时将引用重命名为相同的名字，如const { aa: aa } = person，是错的
      'prefer-arrow-callback': 'error', // 要求回调函数使用箭头函数
      'rest-spread-spacing': 'error', // 要求强制剩余和扩展运算符及其表达式之间有空格，如const obj2 = { ... obj }，...后不能有空格
      'template-curly-spacing': 'error', // 禁止模板字符串中空格的使用，如`姓名:${name}`不能写成`姓名:${ name }`
      'prefer-template': 'error', // 要求使用模板字符串而不是字符串拼接
      'no-restricted-imports': [
        'error',
        {
          paths: [
            {
              name: 'vue',
              importNames: ['ref', 'reactive', 'computed', 'watch', 'watchEffect', 'onMounted', 'onUnmounted'],
              message: '请使用自动导入，无需从 vue 手动导入这些 API。'
            },
            {
              name: 'vue-router',
              importNames: ['useRouter', 'useRoute'],
              message: '请使用自动导入，无需从 vue-router 手动导入这些 API。'
            },
            {
              name: 'pinia',
              importNames: ['defineStore', 'storeToRefs'],
              message: '请使用自动导入，无需从 pinia 手动导入这些 API。'
            }
          ]
        }
      ],
      /**
       * eslint-plugin-vue规则
       */
      'vue/no-multi-spaces': 'error', // vue组件模板里不要有多个空格，如<div id="aa"  class="div"></div>，id和class中间多了个空格
      'vue/html-indent': ['error', 2, { baseIndent: 1, attribute: 1 }], // html合法缩进
      'vue/html-closing-bracket-spacing': ['error', { startTag: 'never', endTag: 'never' }], // html标签不要有空格，如<div ></div>
      'vue/multi-word-component-names': 'off', // 组件需要由多个单词构成
      'vue/html-closing-bracket-newline': 'error', // html标签，闭合标签不能换行，如</span>,最后一个 > 不能换行
      'vue/no-mutating-props': 'off', // 禁止改变props的值，连改变对象里的属性值也不行
      'vue/no-undef-properties': 'error', // 禁止在template模板中，使用未定义的变量
      'vue/component-name-in-template-casing': ['error', 'kebab-case'], // 组件在template中，只允许使用kebab-case命名，如hello-world
      'vue/attribute-hyphenation': 'error' // 在组件中传递的属性值，只允许使用kebab-case命名，如label-position="xx"而不是labelPosition="xx"
    }
  }
])
