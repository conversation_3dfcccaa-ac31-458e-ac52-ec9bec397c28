<template>
  <a-drawer
    v-model:open="visible"
    :loading="loading"
    class="common-detail-drawer"
    title="租赁单元信息变更详情"
    placement="right"
    width="1072px"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)"
          class="primary-btn"
          @click="handleEdit"
          v-auth="'bas:ct_bas_lease_unit_change_record:edit'"
        >
          编辑
        </span>
        <a-dropdown>
          <span class="primary-btn" :class="{ 'cursor-not-allowed': loading }">
            更多
            <i class="a-icon-arrow-down ml-1"></i>
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item key="submit" v-if="['BACK', 'AUDITNO'].includes(detailData.status)">
                <div class="primary-btn" @click="handleSubmit">提交</div>
              </a-menu-item>
              <a-menu-item key="back" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleBack">撤回</div>
              </a-menu-item>
              <a-menu-item key="audit" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleAudit">审核</div>
              </a-menu-item>
              <a-menu-item key="unAudit" v-if="detailData.status === 'AUDITOK'">
                <div class="primary-btn" @click="handleUnAudit">反审核</div>
              </a-menu-item>
              <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(detailData.status)">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <div class="flex items-center mb-[12px]">
      <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">租赁单元信息变更</h2>
      <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_HouseOwner_AuditStatus"></status-tag>
    </div>
    <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
      单据编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
    </div>

    <anchor-tabs :tab-list="tabList" class="h-[calc(100vh-284px)]">
      <template #baseInfo>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">管理公司: {{ detailData.manageCompany_dictText || '-' }}</span>
          <span class="w-[50%]">变更租赁单元: {{ detailData.leaseUnit_dictText || '-' }}</span>
          <span class="w-[50%]">业务日期: {{ detailData.bizDate }}</span>
          <span class="w-[100%] break-words whitespace-pre-wrap">变更说明: {{ detailData.remark || '-' }}</span>
        </div>
      </template>
      <template #leaseUnit>
        <a-table
          v-if="leaseUnitChangeRecordEntry && leaseUnitChangeRecordEntry.length > 0"
          class="lease-unit-table"
          :columns="columns"
          :data-source="leaseUnitChangeRecordEntry"
          :pagination="false"
          row-key="id"
          :scroll="{ x: 800 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'beforeValue'">
              <span class="text-gray-600" :title="`${record.beforeValue || '-'}`">{{ record.beforeValue || '-' }}</span>
            </template>
            <template v-if="column.dataIndex === 'afterValue'">
              <span class="text-blue-600 font-medium" :title="`${record.afterValue || '-'}`">
                {{ record.afterValue || '-' }}
              </span>
            </template>
          </template>
        </a-table>
        <div v-else class="flex flex-col items-center py-[40px]">
          <img src="@/assets/imgs/no-data.png" class="w-[80px] h-[80px]" />
          <span class="text-tertiary mt-[8px]">暂无数据</span>
        </div>
      </template>
    </anchor-tabs>
  </a-drawer>
  <!-- 编辑租赁单元变更记录抽屉 -->
  <edit-lease-unit-change-record ref="editLeaseUnitChangeRecordRef" @refresh="handleRefresh" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import EditLeaseUnitChangeRecord from './EditLeaseUnitChangeRecord.vue'
import {
  getLeaseUnitChangeRecordById,
  queryLeaseUnitChangeRecordEntryByMainId,
  submitLeaseUnitChangeRecord,
  backLeaseUnitChangeRecord,
  auditLeaseUnitChangeRecord,
  unAuditLeaseUnitChangeRecord,
  deleteLeaseUnitChangeRecord
} from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const activeSection = ref('baseInfo')
const detailData = ref({})
const leaseUnitChangeRecordEntry = ref([])
const editLeaseUnitChangeRecordRef = ref()

const tabList = [
  { title: '基础信息', name: 'baseInfo' },
  { title: '变更内容', name: 'leaseUnit' }
]

const columns = [
  { title: '属性', dataIndex: 'property', width: 200, ellipsis: true },
  { title: '属性名', dataIndex: 'propertyName', width: 150, ellipsis: true },
  { title: '变更前值', dataIndex: 'beforeValue', width: 200, ellipsis: true },
  { title: '变更后值', dataIndex: 'afterValue', width: 200, ellipsis: true }
]

/**
 * 打开详情抽屉
 */
const open = async (record) => {
  if (!record?.id) {
    message.error('缺少必要参数')
    return
  }
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 获取详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  const res = await getLeaseUnitChangeRecordById({ id })
  if (res.success) {
    detailData.value = res.result || {}
    const { result } = await queryLeaseUnitChangeRecordEntryByMainId({ id })
    leaseUnitChangeRecordEntry.value = result?.length ? result : []
  } else {
    message.error(res.message || '获取详情失败')
  }
  loading.value = false
}

/**
 * 编辑信息变更单
 */
const handleEdit = () => {
  // 准备编辑数据，包含变更条目列表
  const editData = {
    ...detailData.value,
    leaseUnitChangeRecordEntryList: leaseUnitChangeRecordEntry.value
  }
  editLeaseUnitChangeRecordRef.value?.open(editData)
}

/**
 * 处理编辑组件的刷新事件
 */
const handleRefresh = async () => {
  await loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 审核信息变更单
 */
const handleAudit = () => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:audit')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认审核',
    content: `确定要审核通过单据编号为 "${detailData.value.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await auditLeaseUnitChangeRecord({ id: detailData.value.id })
        message.success('审核成功')
        await loadDetail(detailData.value.id)
      } finally {
        loading.value = false
        handleRefresh()
      }
    }
  })
}

/**
 * 反审核信息变更单
 */
const handleUnAudit = () => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:unAudit')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认反审核',
    content: `确定要反审核单据编号为 "${detailData.value.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await unAuditLeaseUnitChangeRecord({ id: detailData.value.id })
        message.success('反审核成功')
        await loadDetail(detailData.value.id)
      } finally {
        loading.value = false
        handleRefresh()
      }
    }
  })
}

/**
 * 删除信息变更单
 */
const handleDelete = () => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:delete')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除单据编号为 "${detailData.value.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await deleteLeaseUnitChangeRecord({ id: detailData.value.id })
        message.success('删除成功')
        handleClose()
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 提交状态变更单
 */
const handleSubmit = () => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:submit')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认提交',
    content: `确定要提交单据编号为 "${detailData.value.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        detailData.value.leaseUnitChangeRecordEntryList = leaseUnitChangeRecordEntry
        await submitLeaseUnitChangeRecord(detailData.value)
        message.success('提交成功')
        await loadDetail(detailData.value.id)
      } finally {
        loading.value = false
        handleRefresh()
      }
    }
  })
}

/**
 * 撤回状态变更单
 */
const handleBack = () => {
  if (!hasPermission('bas:ct_bas_lease_unit_change_record:edit')) return
  if (loading.value) return
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回单据编号为 "${detailData.value.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await backLeaseUnitChangeRecord({ id: detailData.value.id })
        message.success('撤回成功')
        await loadDetail(detailData.value.id)
      } finally {
        loading.value = false
        handleRefresh()
      }
    }
  })
}

/**
 * 关闭抽屉
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
  activeSection.value = 'baseInfo'
  emits('refresh')
}

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.lease-unit-table {
  margin-top: 16px;

  :deep(.ant-table-thead > tr > th) {
    background-color: #f5f5f5;
    font-weight: 500;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f0f7ff;
  }

  :deep(.ant-tag) {
    border-radius: 4px;
  }
}
</style>
