<template>
  <a-drawer
    v-model:open="visible"
    class="edit-money-adjust-drawer common-drawer"
    title="合同账单调整申请"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '74px' } }"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="业务日期" name="bizDate" style="width: 50%">
          <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
        </a-form-item>
        <a-form-item label="合同" name="contract">
          <a-form-item-rest>
            <f7-select
              v-model="form.contract"
              f7-type="contract"
              @change="loadContractDetail"
              width="422px"
            ></f7-select>
          </a-form-item-rest>
        </a-form-item>
        <div class="flex flex-wrap gap-[12px] mt-[20px] text-secondary pl-[74px]" v-if="contractDetail.id">
          <span class="w-[calc(50%-6px)]">业务日期: {{ contractDetail.bizDate }}</span>
          <span class="w-[calc(50%-6px)]">合同编号: {{ contractDetail.contractNumber }}</span>
          <span class="w-[calc(50%-6px)]">签约客户: {{ contractDetail.customer_dictText }}</span>
          <span class="w-[calc(50%-6px)]">签约日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">物业管理公司: {{ contractDetail.manageCompany_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同类型: {{ contractDetail.contractType_dictText }}</span>
          <span class="w-[calc(50%-6px)]">业务员: {{ contractDetail.operator_dictText }}</span>
          <span class="w-[calc(50%-6px)]">业务部门: {{ contractDetail.operatorDepart_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同开始日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">合同结束日期: {{ contractDetail.expireDate }}</span>
        </div>
      </a-form>
      <div class="flex items-center justify-between mt-[40px] mb-[16px]">
        <h4 class="text-[16px] font-bold">调整明细</h4>
        <a-button type="primary" size="medium" @click="handleAdd">
          <i class="a-icon-plus"></i>
          添加明细
        </a-button>
      </div>
      <a-table
        :data-source="form.receiveAmountAdjustDetailBillList"
        :columns="columns"
        :pagination="false"
        bordered
        :scroll="{ x: 1860 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'number'">
            <div class="flex">
              <a-popconfirm title="是否确认移除？" @confirm="handleRemoveBill(index)">
                <span class="cursor-pointer mr-[6px] text-error">
                  <i class="a-icon-remove"></i>
                </span>
              </a-popconfirm>
              <span>{{ record.number }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'addReduceAmount'">
            <div class="relative money-div" :class="{ 'money-error-div': record.errorInfo }">
              <a-input-group compact style="display: flex">
                <dict-select
                  v-model="record.addReduceType"
                  code="CT_BASE_ENUM_ReceiveAmountAdjustDetailBill_AddReduceType"
                  :allow-clear="false"
                  :show-search="false"
                  @change="onAddReduceTypeChange"
                ></dict-select>
                <a-input v-model:value="record.addReduceAmount" :maxlength="8" @blur="handleBlur(record, index)">
                  <template #suffix>元</template>
                </a-input>
              </a-input-group>
              <small
                class="absolute text-[12px] leading-[1] left-[0] bottom-[-14px] text-error"
                v-if="record.errorInfo"
              >
                {{ record.errorInfo }}
              </small>
            </div>
          </template>
          <template v-if="column.dataIndex === 'adjustRemark'">
            <div class="relative" :class="{ 'reason-error-div': record.reasonErrorInfo }">
              <a-input v-model:value="record.adjustRemark"></a-input>
              <small
                class="absolute text-[12px] leading-[1] left-[0] bottom-[-14px] text-error"
                v-if="record.reasonErrorInfo"
              >
                {{ record.reasonErrorInfo }}
              </small>
            </div>
          </template>
        </template>
      </a-table>
      <div class="mt-[20px]" v-if="showComputedResult">
        <strong class="text-[18px]">{{ computedResult.title }}</strong>
        <div class="mt-[16px] text-secondary">{{ computedResult.description }}</div>
      </div>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[16px]">附件</h4>
      <files-upload v-model="form.attachmentIds" :biz-id="form.id"></files-upload>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        :loading="confirmLoading"
        @click="handleSubmit"
        v-auth="'biz.contractmanage:ct_biz_receive_amount_adjust:submit'"
      >
        提交
      </a-button>
      <a-button
        type="primary"
        :loading="saveLoading"
        ghost
        @click="handleSave"
        v-if="['', 'TEMP'].includes(form.status)"
      >
        暂存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <bill-details-modal ref="chooseBillRef" @update-list="updateBillList"></bill-details-modal>
</template>

<script setup>
import { edit, submit, detail, save, adjustDetail } from '../apis.js'
import { detail as getContractDetail, queryContractLeaseUnits } from '@/views/contract/management/apis'
import { message } from 'ant-design-vue'
import { moneyRegexp } from '@/utils/validate'
import Decimal from 'decimal.js'
import dayjs from 'dayjs'
import { renderMoney, renderBoolean } from '@/utils/render'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  } else {
    form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.bizDate = result.bizDate
  form.contract = result.contract
  form.status = result.status
  if (result.contract) {
    await loadContractDetail(result.contract)
  }
  const { result: data } = await adjustDetail({ id })
  form.receiveAmountAdjustDetailBillList = data.map((item) => {
    item.addReduceAmount = String(item.addReduceAmount)
    return item
  })
  loading.value = false
}

const form = reactive({
  id: '',
  manageCompany: '',
  bizDate: '',
  contract: '',
  signDate: '',
  customer: '',
  contractType: '',
  operator: '',
  operatorDepart: '',
  startDate: '',
  expireDate: '',
  leaseUnit: '',
  status: '',
  actualDealAmount: '',
  remark: '',
  attachmentIds: '',
  receiveAmountAdjustDetailBillList: []
})

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  contract: [{ required: true, message: '请选择合同', trigger: 'change' }]
}

const contractDetail = reactive({
  id: '',
  customer: ''
})
const loadContractDetail = async (id) => {
  form.receiveAmountAdjustDetailBillList = []
  if (!id) {
    contractDetail.id = ''
    return
  }
  const { result } = await getContractDetail({ id })
  Object.assign(contractDetail, result)
  form.contractType = result.contractType
  form.customer = result.customer
  form.expireDate = result.expireDate
  form.startDate = result.startDate
  form.manageCompany = result.manageCompany
  form.signDate = result.signDate
  form.operator = result.operator
  form.operatorDepart = result.operatorDepart
  const { result: data } = await queryContractLeaseUnits({ id })
  form.leaseUnit = data.map((item) => item.leaseUnit).join(',')
}

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text) },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  {
    title: '增减金额',
    dataIndex: 'addReduceAmount',
    width: 240,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text)
  },
  { title: '调整原因', dataIndex: 'adjustRemark', width: 200, fixed: 'right' }
]

const chooseBillRef = ref()
const handleAdd = () => {
  if (!contractDetail.id) {
    message.warning('请先选择合同')
    return
  }
  chooseBillRef.value.open(
    form.receiveAmountAdjustDetailBillList.map((item) => ({
      ...item,
      id: item.detailBillEntry,
      parent: item.detailBill
    })),
    { contract: contractDetail.id }
  )
}

const handleRemoveBill = (index) => {
  form.receiveAmountAdjustDetailBillList.splice(index, 1)
}

const updateBillList = (list) => {
  list.forEach((item) => {
    const data = form.receiveAmountAdjustDetailBillList.find((i) => i.detailBillEntry === item.id)
    if (data) return
    form.receiveAmountAdjustDetailBillList.push({
      ...item,
      detailBill: item.parent,
      detailBillEntry: item.id,
      id: '',
      addReduceType: 'Reduce', // 默认设为减免
      addReduceAmount: '',
      adjustRemark: '',
      errorInfo: '', // 前端自定义字段，错误信息
      reasonErrorInfo: '' // 前端自定义字段，调整原因错误信息
    })
  })
}

const handleBlur = (record, index) => {
  if (record.addReduceAmount && !moneyRegexp.test(record.addReduceAmount)) {
    record.errorInfo = '金额填写不正确'
    message.warning(`第${index + 1}条明细，请填写正确的金额`)
    return
  }
  if (record.addReduceType !== 'Add') {
    if (Number(record.addReduceAmount) > record.paymentAmount) {
      record.errorInfo = '减免金额不得大于应收金额'
      message.warning(`第${index + 1}条明细，减免金额不得大于应收金额`)
      return
    }
  }
  record.errorInfo = ''
}

const onAddReduceTypeChange = () => {
  const list = form.receiveAmountAdjustDetailBillList.filter((i) => i.addReduceType === 'OffDifference')
  if (list.length > 1) {
    message.warning('同一个单据中，只允许存在一条尾差处理')
    return
  }
  if (list.length && form.receiveAmountAdjustDetailBillList.length > 1) {
    message.warning('尾差处理时，不允许存在其他增减处理')
  }
}

// 底部计算结果
const computedResult = reactive({
  title: '', // 显示应退: xx元/调整后待核销合计: xx元
  description: '' // [已收金额] 6500.00 + [本次减免金额] 1092.24 - [实际应收金额] 7300.00 = 292.24
})

const showComputedResult = computed(() => {
  // 存在尾差处理，就不展示计算结果
  const data = form.receiveAmountAdjustDetailBillList.find((i) => i.addReduceType === 'OffDifference')
  if (data) return false
  return Boolean(computedResult.title)
})

watch(
  () => form.receiveAmountAdjustDetailBillList,
  (list) => {
    const isValid = list.every((item) => item.addReduceType && moneyRegexp.test(item.addReduceAmount))
    if (!list.length || !isValid) {
      computedResult.title = ''
      form.actualDealAmount = ''
      return
    }
    let total = new Decimal(0) // 实际应收金额
    let reduce = new Decimal(0) // 本次减免金额
    let add = new Decimal(0) // 本次增加金额
    let receive = new Decimal(0) // 已收金额
    list.forEach((item) => {
      total = total.plus(new Decimal(item.paymentAmount))
      receive = receive.plus(new Decimal(item.paid))
      if (item.addReduceType === 'Add') {
        add = add.plus(new Decimal(Number(item.addReduceAmount)))
      } else {
        reduce = reduce.plus(new Decimal(Number(item.addReduceAmount)))
      }
    })
    let result
    if (reduce.toNumber() >= add.toNumber()) {
      // 显示: 实际应收金额-已收金额-本次减免金额
      result = total.sub(receive).sub(reduce).toNumber()
      computedResult.description = `[实际应收金额]${total.toNumber()} - [已收金额]${receive.toNumber()} - [本次减免金额]${reduce.toNumber()} = ${result}`
    } else {
      // 显示: 已收金额-本次增加金额-实际应收金额
      result = receive.sub(add).sub(total).toNumber()
      computedResult.description = `[已收金额]${receive.toNumber()} - [本次增加金额]${add.toNumber()} - [实际应收金额]${total.toNumber()} = ${result}`
    }
    form.actualDealAmount = result
    computedResult.title = result > 0 ? `应退: ${result}` : `调整后应收款合计: ${Math.abs(result)}`
  },
  { deep: true }
)

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  const fields = []
  for (const key in rules) {
    if (form[key]) {
      fields.push(key)
    }
  }
  await formRef.value.validateFields(fields)
  if (!validateBillList()) return
  try {
    saveLoading.value = true
    if (form.id) {
      const data = await edit(form)
      message.success(data.message)
    } else {
      const { result, message: msg } = await save(form)
      message.success(msg)
      form.id = result.id
      form.number = result.number
      form.status = result.status
    }

    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

/**
 * 校验调整明细列表
 * @param {boolean} require 暂存时，可不必填，但如果有数据，则要校验；提交时，必填
 */
const validateBillList = (require) => {
  if (require) {
    if (!form.receiveAmountAdjustDetailBillList.length) {
      message.warning('请选择调整明细')
      return false
    }
  }
  const list = form.receiveAmountAdjustDetailBillList.filter((i) => i.addReduceType === 'OffDifference')
  if (list.length > 1) {
    message.warning('同一个单据中，只允许存在一条尾差处理')
    return
  }
  if (list.length && form.receiveAmountAdjustDetailBillList.length > 1) {
    message.warning('尾差处理时，不允许存在其他增减处理')
    return
  }
  const illegal = form.receiveAmountAdjustDetailBillList.some((item, index) => {
    if (!item.addReduceAmount) {
      message.warning(`第${index + 1}条明细，请输入金额`)
      item.errorInfo = '金额不可为空'
      return true
    }
    if (!moneyRegexp.test(item.addReduceAmount)) {
      message.warning(`第${index + 1}条明细，金额输入错误`)
      item.errorInfo = '金额输入错误'
      return true
    }
    if (item.addReduceType !== 'Add') {
      if (Number(item.addReduceAmount) > item.paymentAmount) {
        message.warning(`第${index + 1}条明细，减免金额不得大于应收金额`)
        item.errorInfo = '减免金额不得大于应收金额'
        return true
      }
    }
    if (item.adjustRemark.trim() === '') {
      message.warning(`第${index + 1}条明细，调整原因不可为空`)
      item.reasonErrorInfo = '调整原因不可为空'
      return true
    }
    item.errorInfo = ''
    item.reasonErrorInfo = ''
    return false
  })
  return !illegal
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  if (!validateBillList(true)) return
  try {
    confirmLoading.value = true
    const data = await submit(form)
    confirmLoading.value = false
    handleCancel()
    message.success(data.message)
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (Array.isArray(form[key])) {
      form[key] = []
    } else {
      form[key] = ''
    }
  }
  formRef.value.clearValidate()
  computedResult.title = ''
  computedResult.description = ''
  contractDetail.id = ''
  visible.value = false
}

onMounted(() => {
  const contractId = sessionStorage.getItem('contractId')
  if (contractId) {
    open()
    form.contract = contractId
    loadContractDetail(contractId)
    sessionStorage.removeItem('contractId')
  }
})

defineExpose({ open })
</script>

<style lang="less">
.edit-money-adjust-drawer {
  .money-div {
    border: 1px solid transparent;
    border-radius: 8px;
  }
  .money-error-div {
    border-color: var(--color-error);
    .ant-input-affix-wrapper {
      border-color: #d9d9d9 !important;
    }
    .ant-select-selector {
      border-color: #d9d9d9 !important;
    }
  }
  .reason-error-div {
    .ant-input {
      border-color: var(--color-error);
    }
  }
}
</style>
