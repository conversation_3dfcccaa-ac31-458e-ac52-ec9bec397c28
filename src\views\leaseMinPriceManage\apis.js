import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseBasePriceManagement/list',
    params
  })
}
// 提交
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseBasePriceManagement/submit',
    data
  })
}
// 添加
export const stash = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseBasePriceManagement/add',
    data
  })
}
// 编辑
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseBasePriceManagement/edit',
    data
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseBasePriceManagement/updateEnableDisableStatus',
    data
  })
}
// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/bas/leaseBasePriceManagement/queryById?id=${id}`
  })
}

// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/bas/leaseBasePriceManagement/delete?id=${id}`
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/bas/leaseBasePriceManagement/deleteBatch?ids=${ids}`
  })
}

// 租赁底价管理-包含项目主表ID查询
export const queryLeaseBasePriceManagementWyProjectByMainId = (id) => {
  return request({
    method: 'get',
    url: `/bas/leaseBasePriceManagement/queryLeaseBasePriceManagementWyProjectByMainId?id=${id}`
  })
}

// 租赁底价管理-包含租赁单元主表ID查询
export const queryLeaseBasePriceManagementLeaseUnitByMainId = (id) => {
  return request({
    method: 'get',
    url: `/bas/leaseBasePriceManagement/queryLeaseBasePriceManagementLeaseUnitByMainId?id=${id}`
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/leaseBasePriceManagement/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/leaseBasePriceManagement/importExcel', data, controller)
}
