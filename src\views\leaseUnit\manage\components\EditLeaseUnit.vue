<template>
  <a-drawer
    v-model:open="visible"
    class="edit-lease-unit-drawer common-drawer"
    :title="`${formData.id ? '编辑' : '新建'}租赁单元`"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <circle-steps
        :current="currentStep + 1"
        :step-list="stepList"
        width="960px"
        class="mx-auto mb-[40px]"
      ></circle-steps>
      <basic-info-step
        v-show="currentStep === 0"
        ref="basicFormRef"
        :is-assets-source="isAssetsSource"
        :form-data="formData"
      />

      <lease-info-step v-show="currentStep === 1" ref="leaseFormRef" :form-data="formData" />

      <building-info-step v-show="currentStep === 2" ref="landFormRef" :form-data="formData" />

      <water-electric-step v-show="currentStep === 3" ref="waterElectricRef" :form-data="formData" />

      <attachment-step v-show="currentStep === 4" :form-data="formData" />
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button :disabled="currentStep <= 0" @click="handlePrevStep">上一步</a-button>
      <a-button :disabled="currentStep >= stepList.length - 1" @click="handleNextStep">下一步</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage" v-if="!splitMergeFlag">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import userStore from '@/store/modules/user.js'
import { addLeaseUnit, submitLeaseUnit, editLeaseUnit, getLeaseUnitDetail } from '../apis/leaseUnit'
import BasicInfoStep from './BasicInfoStep.vue'
import LeaseInfoStep from './LeaseInfoStep.vue'
import BuildingInfoStep from './BuildingInfoStep.vue'
import WaterElectricStep from './WaterElectricStep.vue'
import AttachmentStep from './AttachmentStep.vue'
import { queryWaterElectricityById } from '@/views/waterElectricity/manage/apis/waterElectricity'

const { splitMergeFlag } = defineProps({
  splitMergeFlag: { type: Boolean, default: false }
})

const emits = defineEmits(['refresh'])

const { userInfo } = userStore()

const basicFormRef = ref()
const leaseFormRef = ref()
const landFormRef = ref()
const waterElectricRef = ref()

const visible = ref(false)
const currentStep = ref(0)
const confirmLoading = ref(false)

const stepList = ['基础信息', '租赁信息', '土地及建筑物信息', '水电费用', '附件信息']

const formDataDefault = {
  id: undefined,
  name: undefined,
  virtualLeaseUnit: false,
  houseOwner: undefined,
  houseOwner_dictText: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  wyBuilding: undefined,
  wyFloor: undefined,
  wyProjectArray: [],
  province: undefined,
  city: undefined,
  area: undefined,
  pcaCode: [],
  detailAddress: undefined,
  assetType: undefined,
  ownerCompany: undefined,
  collectionCompany: undefined,
  manageCompany: undefined,
  landNature: undefined,
  treePath: [],
  treeId: undefined,
  supportFacility: undefined,
  remark: undefined,
  useType: undefined,
  leaseArea: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  effectDate: undefined,
  expireDate: undefined,
  houseType: undefined,
  structureArea: undefined,
  floorArea: undefined,
  buildStructrue: undefined,
  buildYear: undefined,
  layerNum: undefined,
  layerHight: undefined,
  houseModel: undefined,
  firefightingRate: undefined,
  houseSafeRate: undefined,
  houseTaxOrgValue: undefined,
  addTaxRate: undefined,
  invoiceAddress: undefined,
  waterShareFormulas: [],
  attachmentIds: undefined,
  currentLayer: undefined,
  totalLayer: undefined
}

const formData = reactive({ ...formDataDefault })

const isAssetsSource = ref(false)

/**
 * 打开编辑弹窗，初始化表单数据
 * @param {Object} data - 租赁单元数据，包含id等字段
 * @param {boolean} isMerge - 是否为拆合单模式
 */
const open = async (data, isMerge = false) => {
  isAssetsSource.value = isMerge
  visible.value = true

  if (data?.id && !isMerge) {
    // 正常编辑模式：从服务器获取完整数据
    const res = await getLeaseUnitDetail({ id: data.id })
    Object.assign(formData, res.result)
  } else {
    // 新建模式或拆合单模式：使用传入的数据
    Object.assign(formData, { ...formDataDefault, ...data })
    formData.manageCompany = userInfo.value.currentCompany
  }

  // 处理数据格式化（编辑模式或拆合单模式都需要）
  if (data?.id || isMerge) {
    formData.propertyUse = formData.propertyUse || undefined

    // 处理资产数据
    if (formData.virtualLeaseUnit !== true) {
      formData.houseOwner = undefined
      formData.houseOwner_dictText = undefined
    }

    // 处理层数数据
    if (formData.layerNum) {
      const [currentLayer = '', totalLayer = ''] = formData.layerNum.split('/')
      Object.assign(formData, { currentLayer, totalLayer })
    }

    if (formData.buildYear) {
      formData.buildYear = String(formData.buildYear)
    }

    // 处理省市区数据
    if (formData.pcaCode && typeof formData.pcaCode === 'string') {
      formData.pcaCode = formData.pcaCode.split(';')
    }

    if (formData.treePath && formData.treePath.length) {
      formData.treePath = formData.treePath.split(';')
    }

    formData.wyProjectArray = [formData.wyProject, formData.wyBuilding, formData.wyFloor].filter(Boolean)

    // 如果有水电表，拉取详情
    if (formData.waterShareFormulas && formData.waterShareFormulas.length) {
      await fetchWaterElectricityDetails()
    }
  }

  if (!formData.effectDate) {
    formData.effectDate = dayjs().format('YYYY-MM-DD')
  }
  if (!formData.expireDate) {
    formData.expireDate = '2099-12-31'
  }
}

/**
 * 取消操作并重置表单数据
 */
const handleCancel = () => {
  basicFormRef.value?.resetFields()
  leaseFormRef.value?.resetFields()
  landFormRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  formData.files = []
  formData.waterShareFormulas = []
  currentStep.value = 0
  visible.value = false
}

/**
 * 上一步
 */
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/**
 * 统一的表单验证函数
 * @param {number|null} stepIndex - 步骤索引，为null时验证所有步骤
 */
const validateSteps = async (stepIndex = null) => {
  const stepValidations = [
    () => basicFormRef.value?.validate(),
    () => leaseFormRef.value?.validate(),
    () => landFormRef.value?.validate(),
    () => waterElectricRef?.value.validate(),
    () => Promise.resolve() // 附件信息步骤无需验证
  ]

  // 验证单个步骤
  if (stepIndex !== null) {
    try {
      if (stepValidations[stepIndex]) {
        await stepValidations[stepIndex]()
      }
      return true
    } catch {
      return false
    }
  }

  // 验证所有步骤
  const results = await Promise.allSettled(stepValidations.map((validate) => validate()))
  return results.findIndex((result) => result.status === 'rejected')
}

/**
 * 下一步，验证当前步骤表单后进入下一步
 */
const handleNextStep = async () => {
  if (currentStep.value >= stepList.length - 1) return

  const isValid = await validateSteps(currentStep.value)
  if (!isValid) {
    message.error('请填写完必填项后再进入下一步')
    return
  }

  currentStep.value++
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  // 提交时需要验证所有必填表单
  if (!isTemporary) {
    // 验证所有表单，如果有未通过的，切换到第一个未通过的步骤
    const firstInvalidStep = await validateSteps()
    if (firstInvalidStep !== -1) {
      currentStep.value = firstInvalidStep
      message.error(`请检查并完善${stepList[firstInvalidStep]}的必填信息`)
      confirmLoading.value = false
      return
    }
  }
  formData.pcaCode = Array.isArray(formData.pcaCode) ? formData.pcaCode.join(';') : formData.pcaCode
  formData.treePath = Array.isArray(formData.treePath) ? formData.treePath.join(';') : formData.treePath

  if (splitMergeFlag) {
    emits('refresh', formData)
    confirmLoading.value = false
    handleCancel()
    return
  }

  // 根据操作类型选择对应的API
  const api = !isTemporary ? submitLeaseUnit : formData.id ? editLeaseUnit : addLeaseUnit

  try {
    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '新建'
    message.success(`租赁单元${action}成功`)
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 保存并提交租赁单元数据
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存租赁单元数据
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 获取水电表详情数据
 */
const fetchWaterElectricityDetails = async () => {
  if (!formData.waterShareFormulas || !formData.waterShareFormulas.length) return
  const promises = formData.waterShareFormulas.map((item) => queryWaterElectricityById({ id: item.waterEleTableNum }))
  const results = await Promise.all(promises)
  formData.waterShareFormulas.forEach((item, idx) => {
    const res = results[idx]
    if (res && res.success && res.result) {
      Object.assign(item, res.result)
    }
  })
}

defineExpose({ open })
</script>
<style lang="less">
.edit-lease-unit-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }

  .site-input-split {
    background-color: #fff;
  }
  .site-input-right {
    border-left-width: 0;
    &:hover,
    &:focus {
      border-left-width: 1px;
    }
  }

  .ant-input-rtl.site-input-right {
    border-right-width: 0;
    &:hover,
    &:focus {
      border-right-width: 1px;
    }
  }
}
</style>
