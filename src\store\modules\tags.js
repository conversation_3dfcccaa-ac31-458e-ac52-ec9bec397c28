import { useUserStore } from './user'
import router from '@/router'

// 根据当前路由，查找祖先节点，以便设置页面左侧的当前主菜单
const findAncestors = (tree, targetPath, targetTitle, ancestors = []) => {
  for (const node of tree) {
    if (node.path === targetPath && node.meta.title === targetTitle) {
      return ancestors
    }
    if (node.children && node.children.length > 0) {
      const result = findAncestors(node.children, targetPath, targetTitle, [...ancestors, node])
      if (result !== null) {
        return result
      }
    }
  }
  return null
}

export const useTagStore = defineStore('tagStore', {
  persist: {
    key: `${import.meta.env.VITE_STORE_KEY}:tag`,
    storage: sessionStorage
  },
  state: () => ({
    currentTag: {},
    tagList: []
  }),
  actions: {
    setCurrentTag(data) {
      const userStore = useUserStore()
      const menuTree = userStore.permission.menu
      const list = findAncestors(menuTree, data.path, data.meta.title)
      if (list && list.length) {
        userStore.setCurrentMenus({
          title: list[0].meta.title,
          list: list[0].children
        })
      }
      this.currentTag = data
    },
    addTag(to) {
      const index = this.tagList.findIndex((i) => i.path === to.path)
      if (index === -1 && to.meta.title) {
        this.tagList.push(to)
      }
    },
    closeTag(index) {
      this.tagList.splice(index, 1)
    },
    closeAllTag() {
      this.tagList = []
    },
    closeOtherTag(to) {
      const data = this.tagList.find((i) => i.path === to.path)
      this.tagList = [data]
    },
    closeCurTab() {
      if (this.tagList.length === 1) {
        router.push({ path: '/home' })
        return
      }
      const index = this.tagList.findIndex((i) => i.path === this.currentTag.path)
      if (index !== -1) {
        router.push(this.tagList[index - 1] || this.tagList[index + 1])
        this.tagList.splice(index, 1)
      }
    }
  }
})

export default function tagStore() {
  const store = useTagStore()
  return storeToRefs(store)
}
