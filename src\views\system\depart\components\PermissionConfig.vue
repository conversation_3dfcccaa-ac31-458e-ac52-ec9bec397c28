<template>
  <div class="max-h-[50vh] pt-[16px] overflow-y-auto">
    <a-tree
      v-model:expanded-keys="expandedKeys"
      v-model:checked-keys="checkedKeys"
      checkable
      :check-strictly="checkStrictly"
      :tree-data="treeData"
      :field-names="{ title: 'slotTitle' }"
    >
      <template #title="{ slotTitle }">
        <span>{{ slotTitle }}</span>
      </template>
    </a-tree>
  </div>
  <div class="flex justify-between mt-[20px] pt-[16px] border-0 border-t border-solid border-[#e0e0e0]">
    <a-dropdown>
      <a-button @click.prevent>树操作</a-button>
      <template #overlay>
        <a-menu>
          <a-menu-item><div class="primary-btn" @click="handleCheckedAll(true)">选择全部</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="handleCheckedAll(false)">取消全选</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="handleExpandAll(true)">展开全部</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="handleExpandAll(false)">折叠全部</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="setCheckStrictly(false)">层级关联</div></a-menu-item>
          <a-menu-item><div class="primary-btn" @click="setCheckStrictly(true)">层级独立</div></a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-button type="primary" :loading="loading" @click="handleSave" v-auth="'system:permission:saveDepart'">
      保存
    </a-button>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { queryTreeList } from '@/views/system/role/apis'
import { queryDepartPermission, saveDepartPermission } from '../apis'

const { departId } = defineProps({
  departId: { required: true, type: String }
})

const treeData = ref([])
const allIds = ref([])
const loadTreeData = async () => {
  const { result } = await queryTreeList()
  treeData.value = result.treeList
  allIds.value = result.ids
  loadPermissionByDept()
}

const lastPermissionIds = ref([])
const loadPermissionByDept = async () => {
  const { result } = await queryDepartPermission({ departId })
  checkedKeys.value = result
  lastPermissionIds.value = [...result]
}

const expandedKeys = ref([])
const checkedKeys = ref([])

const handleCheckedAll = (checked) => {
  checkedKeys.value = checked ? allIds.value : []
}

const handleExpandAll = (expand) => {
  expandedKeys.value = expand ? allIds.value : []
}

const checkStrictly = ref(false)
const setCheckStrictly = (value) => {
  if (checkStrictly.value === value) return
  checkStrictly.value = value
  if (value) {
    if (!checkedKeys.value.checked) {
      checkedKeys.value = {
        checked: checkedKeys.value
      }
    }
  } else {
    if (checkedKeys.value.checked) {
      checkedKeys.value = checkedKeys.value.checked
    }
  }
}

const loading = ref(false)
const handleSave = async () => {
  if (loading.value) return
  try {
    loading.value = true
    await saveDepartPermission({
      lastpermissionIds: lastPermissionIds.value.join(','),
      permissionIds: checkedKeys.value.checked ? checkedKeys.value.checked.join(',') : checkedKeys.value.join(','),
      departId
    })
    message.success('保存成功')
  } finally {
    loading.value = false
  }
}

watch(
  () => departId,
  (val) => {
    expandedKeys.value = []
    if (val) {
      loadTreeData()
    } else {
      treeData.value = []
    }
  }
)
</script>
