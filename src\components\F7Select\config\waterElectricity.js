import { f7List } from '@/views/waterElectricity/manage/apis/waterElectricity'
import { renderDictTag } from '@/utils/render'

export default {
  modalTitle: '选择水电表',
  request: f7List,
  params: {
    column: 'number',
    order: 'desc',
    name: undefined,
    number: undefined,
    type: undefined,
    property: undefined,
    doubleRate: undefined,
    status: 'ENABLE',
    ownerCompany: undefined,
    collectionCompany: undefined,
    manageCompany: undefined,
    price: undefined,
    remark: undefined,
    treeId: undefined
  },
  rowKey: 'id',
  displayKey: 'name',
  keywordKey: 'name',
  keywordPlaceholder: '搜索名称',
  scrollX: 1800,
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  filterLabelWidth: '100px',
  columns: [
    { title: '名称', dataIndex: 'name', fixed: 'left', width: 200 },
    { title: '编码(表号)', dataIndex: 'number' },
    { title: '类型', dataIndex: 'type_dictText' },
    { title: '属性', dataIndex: 'property_dictText' },
    { title: '倍率', dataIndex: 'doubleRate' },
    {
      title: '状态',
      dataIndex: 'status',
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_BaseStatus', 'dot')
    },
    { title: '资产权属公司', dataIndex: 'ownerCompany_dictText', ellipsis: true },
    { title: '租金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
    { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
    { title: '组别', dataIndex: 'treeId_dictText' },
    { title: '单价', dataIndex: 'price', customRender: ({ text }) => (text ? `${text}元` : '') },
    { title: '备注', dataIndex: 'remark', ellipsis: true }
  ],
  searchList: [
    { label: '编码(表号)', name: 'number', type: 's-input', placeholder: '请输入编码(表号)' },
    {
      label: '类型',
      name: 'type',
      type: 'dict-select',
      code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Type'
    },
    {
      label: '属性',
      name: 'property',
      type: 'dict-select',
      code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Property'
    },
    { label: '倍率', name: 'doubleRate', type: 'input' },
    { label: '资产权属公司', name: 'ownerCompany', type: 'company-select', companyType: 'all' },
    { label: '租金归集公司', name: 'collectionCompany', type: 'company-select', companyType: 'all' },
    { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
    { label: '单价', name: 'price', type: 'input' },
    { label: '备注', name: 'remark', type: 's-input' }
  ]
}
