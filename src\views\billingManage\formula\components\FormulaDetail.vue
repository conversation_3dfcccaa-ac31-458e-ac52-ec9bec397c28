<template>
  <a-modal v-model:open="visible" :title="title" width="900px" class="common-modal" :mask-closable="false">
    <FormulaCodemirror v-model="formula" disabled beautify />
    <template #footer>
      <a-button type="primary" @click="mock">模拟</a-button>
    </template>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['mock'])

const visible = ref(false)

const formula = ref('')
const title = ref('')

const open = (text, _title = '查看脚本') => {
  visible.value = true
  formula.value = text
  title.value = _title
}

const mock = () => {
  emit('mock')
}

defineExpose({ open })
</script>
