<!-- 筛选功能 -->
<template>
  <a-popover
    :placement="placement"
    :arrow="false"
    trigger="click"
    overlay-class-name="filter-more-overlay"
    v-model:open="dropdownVisible"
  >
    <a-button>
      <span class="a-icon-filter mr-[8px]"></span>
      <span>{{ btnText }}</span>
      <span>({{ filterNum }})</span>
    </a-button>
    <template #content>
      <div class="search-content overflow-auto no-scrollbar">
        <div class="!mb-[12px] flex items-center" v-for="(item, index) in searchList" :key="index">
          <span class="whitespace-nowrap w-[150px]" v-if="item.label">{{ item.label }}</span>
          <template v-if="item.type === 'number'">
            <a-input-number
              v-model:value="localSearch[item.name]"
              :min="item.min"
              :precision="item.precision"
              :placeholder="item.placeholder || '请输入'"
              style="width: 100%"
            />
          </template>
          <template v-else-if="item.type === 'radio'">
            <a-radio-group v-model:value="localSearch[item.name]">
              <a-radio v-for="i in item.list" :key="i.value" :value="i.value" @click="handleRadioClick(item, i.value)">
                {{ i.label }}
              </a-radio>
            </a-radio-group>
          </template>
          <template v-else-if="item.type === 'check'">
            <a-checkbox v-model:checked="localSearch[item.name]"></a-checkbox>
          </template>
          <template v-else-if="item.type === 'checkbox'">
            <a-checkbox-group v-model:value="localSearch[item.name]" :options="item.list" />
          </template>
          <template v-else-if="item.type === 'input'">
            <a-input
              v-model:value="localSearch[item.name]"
              :placeholder="item.placeholder || '请输入'"
              allow-clear
            ></a-input>
          </template>
          <template v-else-if="item.type === 'cascader'">
            <a-cascader
              class="!w-[100%]"
              ref="cascaderRef"
              v-model:value="localSearch[item.name]"
              :options="item.list"
              :placeholder="item.placeholder || '请选择'"
              allow-clear
            />
          </template>
          <template v-else-if="item.type === 'select'">
            <a-select
              class="!w-[100%]"
              v-model:value="localSearch[item.name]"
              :placeholder="item.placeholder || '请选择'"
              allow-clear
              :options="item.list"
            ></a-select>
          </template>
          <template v-else-if="['date', 'year', 'month', 'week'].includes(item.type)">
            <a-date-picker
              :picker="item.type"
              :value-format="item.format || 'YYYY-MM-DD'"
              :format="item.format || 'YYYY-MM-DD'"
              v-model:value="localSearch[item.name]"
              :placeholder="item.placeholder || '请选择'"
              style="width: 100%"
            />
          </template>
          <template v-else-if="['month-range'].includes(item.type)">
            <a-range-picker
              v-model:value="localSearch[item.name]"
              :picker="item.type.split('-')[0]"
              :value-format="item.format || 'YYYY-MM-DD'"
              :format="item.format || 'YYYY-MM-DD'"
              style="width: 100%"
            />
          </template>
          <template v-else-if="item.type === 's-input'">
            <s-input v-model="localSearch[item.name]" :placeholder="item.placeholder || '请输入'"></s-input>
          </template>
          <template v-else-if="item.type === 'dic'">
            <dict-select
              v-model="localSearch[item.name]"
              :placeholder="item.placeholder || '请选择'"
              :code="item.code"
            ></dict-select>
          </template>
          <template v-else-if="['api'].includes(item.type)">
            <api-select
              v-model="localSearch[item.name]"
              :async-fn="item.listFunc"
              :field-names="item.fieldNames || { label: item.type === 'userSelect' ? 'realname' : 'name', value: 'id' }"
              :placeholder="item.placeholder || '请选择'"
              @change="valueChange(localSearch[item.name], item.name)"
            ></api-select>
          </template>
          <template
            v-else-if="
              ['customerSelect', 'userSelect', 'leaseUnitSelect', 'projectSelect', 'paymentTypeSelect'].includes(
                item.type
              )
            "
          >
            <api-page-select
              v-model="localSearch[item.name]"
              :async-fn="requestObj[item.type]"
              :field-names="
                item.fieldNames || {
                  label: item.type === 'userSelect' ? 'realname' : 'name',
                  value: 'id'
                }
              "
              :placeholder="item.placeholder || '请选择'"
              :mode="item.mode"
              @change="valueChange(localSearch[item.name], item.name)"
            ></api-page-select>
          </template>
          <template v-else-if="item.type === 'companySelect'">
            <company-select
              v-model="localSearch[item.name]"
              :placeholder="item.placeholder || '请选择'"
              :type="item.companyType || 'user'"
            ></company-select>
          </template>
          <template v-else-if="item.type === 'departSelect'">
            <depart-select v-model="localSearch[item.name]" :placeholder="item.placeholder || '请选择'"></depart-select>
          </template>

          <!-- 动态报表筛选所需 -->
          <template v-else-if="item.type === 'map'">
            <div class="flex flex-col">
              <div class="flex justify-between gap-2 mb-[10px]">
                <div class="w-[50px] text-center">选择</div>
                <div class="w-[150px] text-center">组合项目</div>
                <div class="w-[300px] text-center">项目</div>
                <!-- <div class="w-[150px] text-center">到</div> -->
                <div class="w-[150px] text-center">分组统计</div>
                <div class="w-[150px] text-center">排序</div>
              </div>
              <div class="h-[400px] overflow-auto no-scrollbar">
                <div
                  class="flex justify-between items-center gap-2 mb-[10px]"
                  v-for="item in reportList"
                  :key="item.item"
                >
                  <div class="!w-[50px] text-center">
                    <a-checkbox v-model:checked="item.check" @change="rowCheckChange(item)"></a-checkbox>
                  </div>
                  <div class="w-[150px] text-center">{{ item.label }}</div>
                  <div class="!w-[300px] text-center">
                    <company-select
                      v-if="['collectionCompany', 'manageCompany'].includes(item.item)"
                      v-model="item.value"
                      :placeholder="item.placeholder || '请选择'"
                      mode="multiple"
                      :type="item.companyType || 'user'"
                      :disabled="!item.check"
                    ></company-select>

                    <depart-select
                      v-else-if="['salesdepartment'].includes(item.item)"
                      v-model="item.value"
                      :placeholder="item.placeholder || '请选择'"
                      mode="multiple"
                      :disabled="!item.check"
                    ></depart-select>
                    <api-page-select
                      v-else-if="
                        [
                          'customer',
                          'salesman',
                          'leaseUnit',
                          'paymentType',
                          'contractNum',
                          'waterEleTableNum',
                          'carportNum'
                        ].includes(item.item)
                      "
                      v-model="item.value"
                      :async-fn="reportRequestObj[item.item]"
                      :field-names="
                        item.fieldNames || { label: item.item === 'salesman' ? 'realname' : 'name', value: 'id' }
                      "
                      :placeholder="item.placeholder || '请选择'"
                      mode="multiple"
                      :disabled="!item.check"
                      @change="valueChange(localSearch[item.name], item.name)"
                    ></api-page-select>

                    <dict-select
                      v-else-if="['serviceCenter', 'park'].includes(item.item)"
                      v-model="item.value"
                      :placeholder="item.placeholder || '请选择'"
                      :code="item.code"
                      mode="multiple"
                      :disabled="!item.check"
                    ></dict-select>
                  </div>
                  <div class="!w-[150px] text-center">
                    <a-checkbox v-model:checked="item.grouped" :disabled="!item.check"></a-checkbox>
                  </div>
                  <a-input-number
                    class="!w-[150px] text-center"
                    v-model:value="item.seq"
                    :min="1"
                    :precision="0"
                    :placeholder="item.placeholder || '请输入'"
                    :disabled="!item.check"
                  />
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="flex justify-between items-center pt-[12px]">
        <div class="cursor-pointer clear-btn active:text-primary select-none" @click="clearSearch">
          <span class="a-icon-reset mr-[8px]"></span>
          <span>清空</span>
        </div>
        <div>
          <a-button @click="dropdownVisible = false">取消</a-button>
          <a-button type="primary" @click="updateSearch">筛选</a-button>
        </div>
      </div>
    </template>
  </a-popover>
</template>
<script setup>
import { getUserList } from '@/views/system/user/apis'
import { getCustomerList } from '@/views/customer/manage/apis'
import { getF7List as leaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { f7List as paymentTypeF7 } from '@/views/paymentType/apis'
import { f7List as contactF7 } from '@/views/contract/management/apis'
import { f7List as waterElectricityF7 } from '@/views/waterElectricity/manage/apis/waterElectricity'
import { f7List as parkingNumberF7 } from '@/views/parkingNumber/apis'
import { projectPage } from '@/views/projects/apis.js'
const emits = defineEmits(['searchChange', 'filterItemChange', 'update:modelValue'])
const { modelValue, searchList } = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  searchList: { type: Array, default: () => [] },
  btnText: {
    type: String,
    default: '筛选'
  },
  placement: {
    type: String,
    default: 'bottomRight'
  }
})

const reportList = ref([
  {
    seq: 1,
    label: '租金归集公司',
    item: 'collectionCompany',
    value: [],
    grouped: false,
    placeholder: '请选择租金归集公司',
    companyType: 'all'
  },
  {
    seq: 2,
    label: '物业管理公司',
    item: 'manageCompany',
    value: [],
    grouped: false,
    placeholder: '请选择物业管理公司'
  },
  {
    seq: 3,
    label: '客户',
    item: 'customer',
    value: [],
    grouped: false,
    placeholder: '请选择客户'
  },
  {
    seq: 4,
    label: '款项类型',
    item: 'paymentType',
    value: [],
    grouped: false,
    placeholder: '请选择款项类型'
  },
  {
    seq: 5,
    label: '租赁单元',
    item: 'leaseUnit',
    value: [],
    grouped: false,
    placeholder: '请选择租赁单元'
  },
  {
    seq: 6,
    label: '服务处',
    item: 'serviceCenter',
    value: [],
    grouped: false,
    code: 'CT_BAS_ServiceCenter',
    placeholder: '请选择服务处'
  },
  {
    seq: 7,
    label: '业务部门',
    item: 'salesdepartment',
    value: [],
    grouped: false,
    placeholder: '请选择业务部门'
  },
  {
    seq: 8,
    label: '业务员',
    item: 'salesman',
    value: [],
    grouped: false,
    placeholder: '请选择业务员'
  },
  {
    seq: 9,
    label: '合同编号',
    item: 'contractNum',
    value: [],
    grouped: false,
    fieldNames: { label: 'number', value: 'id' },
    placeholder: '请选择合同编号'
  },
  {
    seq: 10,
    label: '水电表号',
    item: 'waterEleTableNum',
    value: [],
    grouped: false,
    placeholder: '请选择水电表号'
  },
  {
    seq: 11,
    label: '停车场',
    item: 'park',
    value: [],
    grouped: false,
    code: 'CT_BAS_Park',
    placeholder: '请选择停车场'
  },
  {
    seq: 12,
    label: '车位号',
    item: 'carportNum',
    value: [],
    grouped: false,
    placeholder: '请选择车位号'
  }
])

// 当前账号所属的公司下的数据由后端来控制
const requestObj = ref({
  customerSelect: getCustomerList,
  userSelect: getUserList,
  leaseUnitSelect: leaseUnitList,
  projectSelect: projectPage,
  paymentTypeSelect: paymentTypeF7
})
const reportRequestObj = ref({
  customer: getCustomerList,
  salesman: getUserList,
  leaseUnit: leaseUnitList,
  paymentType: paymentTypeF7,
  contractNum: contactF7,
  waterEleTableNum: waterElectricityF7,
  carportNum: parkingNumberF7
})

const localSearch = ref({})
const dropdownVisible = ref(false)
// 搜索的参数数量
const filterNum = computed(() => {
  let num = 0
  for (const key in modelValue) {
    if (['number', 'string', 'boolean'].includes(typeof modelValue[key]) && modelValue[key]) {
      num++
    }
    if (Array.isArray(modelValue[key]) && modelValue[key].length) {
      num++
    }
    const obj = searchList.find((item) => item.type === 'map')
    if (obj) {
      const arr = reportList.value.filter((item) => item.check)
      if (arr.length) {
        num++
      }
    }
  }
  return num
})

const handleRadioClick = (item, value) => {
  // 当前选中值等于点击的选项值时，取消勾选（设为 null）
  if (localSearch.value[item.name] === value) {
    localSearch.value[item.name] = ''
  }
}
const updateSearch = () => {
  // 动态报表选择搜索项map
  const obj = searchList.find((item) => item.type === 'map')
  if (obj) {
    const recAccMap = {}
    reportList.value
      .filter((item) => item.check)
      .forEach((item, index) => {
        recAccMap[index + 1] = {
          seq: item.seq,
          label: item.label,
          item: item.item,
          value: item.value,
          grouped: item.grouped
        }
      })
    emits('update:modelValue', {
      ...localSearch.value,
      recAccMap
    })
  } else {
    emits('update:modelValue', localSearch.value)
  }
  emits('searchChange')
  dropdownVisible.value = false
}
const clearSearch = () => {
  for (const key in localSearch.value) {
    if (['number', 'string'].includes(typeof localSearch.value[key])) {
      localSearch.value[key] = ''
    }
    if (['boolean'].includes(typeof localSearch.value[key])) {
      localSearch.value[key] = undefined
    }
    if (Array.isArray(localSearch.value[key])) {
      localSearch.value[key] = []
    }
  }
  reportList.value.forEach((item, index) => {
    item.check = false
    item.value = []
    item.grouped = false
    item.seq = index + 1
  })
}
// api-select 值改变的回调
const valueChange = (id, name) => {
  emits('filterItemChange', id, name)
}
const rowCheckChange = (item) => {
  if (!item.check) {
    item.value = []
    item.grouped = false
  }
}
watch(
  () => modelValue,
  (newVal) => {
    localSearch.value = JSON.parse(JSON.stringify(newVal))
    if (localSearch.value.recAccMap) {
      const selectArr = Object.values(localSearch.value.recAccMap)
      const selectKeyArr = selectArr.map((item) => item.item)
      reportList.value.forEach((item) => {
        if (selectKeyArr.includes(item.item)) {
          const obj = selectArr.find((i) => i.item === item.item)
          item.check = true
          item.value = obj.value
          item.grouped = obj.grouped
          item.seq = obj.seq
        }
      })
    }
  },
  { immediate: true, deep: true }
)
</script>
<style lang="less" scoped>
.search-content {
  max-height: 600px;
}
</style>
