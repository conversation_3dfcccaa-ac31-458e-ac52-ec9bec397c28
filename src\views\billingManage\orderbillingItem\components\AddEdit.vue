<template>
  <a-modal
    v-model:open="visible"
    :title="title"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '100px' } }"
      autocomplete="off"
    >
      <a-form-item label="计费项名称" name="name">
        <a-input v-model:value="ruleForm.name" placeholder="请输入方案名称" :maxlength="50" show-count />
      </a-form-item>
      <a-form-item label="计费单位" name="measureUnit">
        <dict-select
          v-model="ruleForm.measureUnit"
          placeholder="请选择计费单位"
          code="CT_BAS_MeasureUnit"
        ></dict-select>
      </a-form-item>
      <a-form-item label="所属款项类型" name="paymentType">
        <api-select
          v-model="ruleForm.paymentType"
          :async-fn="page"
          :field-names="{ label: 'name', value: 'id' }"
          placeholder="请选择所属款项类型"
        ></api-select>
      </a-form-item>

      <a-form-item label="阶梯定价" name="isLadder">
        <div class="flex justify-between">
          <a-switch v-model:checked="ruleForm.isLadder" @change="isLadderChange" />
          <div class="cursor-pointer" @click="addUnitPrice" v-if="ruleForm.isLadder">
            <span class="a-icon-plus text-[14px] font-bold text-primary mr-[5px]"></span>
            <span>添加阶梯</span>
          </div>
        </div>
      </a-form-item>

      <div
        class="flex justify-between"
        v-for="(item, index) in ruleForm.orderChargeProjectLadderDetailList"
        :key="index"
      >
        <a-form-item
          class="!w-[40%] !ml-[100px]"
          label=""
          :name="['orderChargeProjectLadderDetailList', index, 'count']"
          :rules="[{ required: true, message: '请输入件数', trigger: 'blur' }]"
        >
          <div class="flex items-center gap-x-[12px]">
            <span
              v-if="ruleForm.orderChargeProjectLadderDetailList.length > 1"
              class="a-icon-remove cursor-pointer text-[16px] font-bold text-error"
              @click="delUnitPrice(item, index)"
            ></span>
            <a-input-number
              class="!w-[100%]"
              v-model:value="item.count"
              :min="0"
              :precision="0"
              prefix="第"
              :addon-after="`${ruleForm.measureUnit ? renderDict(ruleForm.measureUnit, 'CT_BAS_MeasureUnit') : '件'}起`"
            ></a-input-number>
          </div>
        </a-form-item>
        <a-form-item
          class="!w-[40%]"
          label=""
          :name="['orderChargeProjectLadderDetailList', index, 'amount']"
          :rules="[{ required: true, message: '请输入价格', trigger: 'blur' }]"
        >
          <a-input-number
            class="!w-[100%]"
            v-model:value="item.amount"
            :min="0"
            :precision="2"
            addon-after="元"
          ></a-input-number>
        </a-form-item>
      </div>

      <a-form-item label="开启封顶" name="isCapRule">
        <a-switch v-model:checked="ruleForm.isCapRule" @change="isCapRuleChange" />
      </a-form-item>
      <div class="flex justify-between" v-if="ruleForm.isCapRule">
        <a-form-item
          class="!w-[40%] !ml-[100px]"
          label=""
          name="innerCapCount"
          :rules="[{ required: ruleForm.isCapRule, message: '请输入件数', trigger: 'blur' }]"
        >
          <a-input-number
            class="!w-[100%]"
            v-model:value="ruleForm.innerCapCount"
            :min="0"
            :precision="0"
            :addon-after="`${ruleForm.measureUnit ? renderDict(ruleForm.measureUnit, 'CT_BAS_MeasureUnit') : '件'}内封顶`"
          ></a-input-number>
        </a-form-item>
        <a-form-item
          class="!w-[40%]"
          label=""
          name="innerCapAmount"
          :rules="[{ required: ruleForm.isCapRule, message: '请输入价格', trigger: 'blur' }]"
        >
          <a-input-number
            class="!w-[100%]"
            v-model:value="ruleForm.innerCapAmount"
            :min="getInnerCapAmountMin"
            :precision="2"
            addon-after="元"
          ></a-input-number>
        </a-form-item>
      </div>

      <a-form-item label="税率" name="taxRate">
        <a-input-number
          class="!w-[100%]"
          v-model:value="ruleForm.taxRate"
          placeholder="请输入税率"
          :min="0"
          :precision="2"
          addon-after="%"
        ></a-input-number>
      </a-form-item>
      <a-form-item label="计费说明" name="remark">
        <a-textarea
          v-model:value="ruleForm.remark"
          placeholder="请输入计费说明"
          :maxlength="255"
          :rows="4"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { renderDict } from '@/utils/render'
import { add, edit, queryOrderChargeProjectLadderDetailByMainId } from '../apis'
import { message, Modal } from 'ant-design-vue'
import { page } from '@/views/paymentType/apis'
const emits = defineEmits('loadData')
// 弹窗可见性
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  Object.assign(ruleForm, data)
  if (data.id) {
    getQueryOrderChargeProjectLadderDetailByMainId(data.id)
  }
}
defineExpose({ open })
const getQueryOrderChargeProjectLadderDetailByMainId = async (id) => {
  const { result } = await queryOrderChargeProjectLadderDetailByMainId({ id })
  ruleForm.orderChargeProjectLadderDetailList = result.map((item) => {
    return {
      id: item.id,
      count: item.count,
      amount: item.amount
    }
  })
}
const title = computed(() => {
  return ruleForm.id ? '编辑订单计费项' : '新建订单计费项'
})
const ruleForm = reactive({
  id: '',
  name: '',
  measureUnit: '',
  paymentType: '',
  isLadder: true,
  isCapRule: true,
  innerCapCount: undefined,
  innerCapAmount: undefined,
  taxRate: undefined,
  remark: '',
  serviceType: '',
  orderChargeProjectLadderDetailList: [{ id: '', count: undefined, amount: undefined }]
})
const rules = {
  name: [{ required: true, message: '请输入计费项名称', trigger: ['blur'] }],
  measureUnit: [{ required: true, message: '请输入选择计费单位', trigger: ['blur'] }],
  paymentType: [{ required: true, message: '请选择所属款项类型', trigger: ['blur'] }],
  taxRate: [{ required: true, message: '请输入税率', trigger: ['blur'] }],
  remark: [{ required: true, message: '请输入计费说明', trigger: ['blur'] }]
}

const getInnerCapAmountMin = computed(() => {
  let count = 0
  ruleForm.orderChargeProjectLadderDetailList.forEach((item, index, self) => {
    count += index ? (item.count - self[index - 1].count) * item.amount : item.count * item.amount
  })
  return count
})

const addUnitPrice = () => {
  ruleForm.orderChargeProjectLadderDetailList.push({ id: '', count: '', amount: '' })
}
const delUnitPrice = (item, index) => {
  Modal.confirm({
    title: '确定删除当前阶梯定价吗？',
    content: '',
    centered: true,
    onOk: () => {
      ruleForm.orderChargeProjectLadderDetailList.splice(index, 1)
    }
  })
}

const isLadderChange = (e) => {
  if (!e) {
    ruleForm.orderChargeProjectLadderDetailList = []
    return
  }
  ruleForm.orderChargeProjectLadderDetailList.push({ id: '', count: undefined, amount: undefined })
}
const isCapRuleChange = (e) => {
  if (!e) {
    ruleForm.innerCapCount = undefined
    ruleForm.innerCapAmount = undefined
  }
}
// 提交
const confirmLoading = ref(false)
const formRef = ref()
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const data = await (ruleForm.id ? edit(ruleForm) : add(ruleForm))
    emits('loadData')
    handleCancel()
    message.success(data.message)
  } finally {
    confirmLoading.value = false
  }
}
// 取消
const handleCancel = () => {
  Object.assign(ruleForm, {
    id: '',
    name: '',
    measureUnit: '',
    paymentType: '',
    isLadder: true,
    isCapRule: true,
    innerCapCount: undefined,
    innerCapAmount: undefined,
    taxRate: undefined,
    remark: '',
    orderChargeProjectLadderDetailList: [{ id: '', count: '', amount: '' }]
  })
  visible.value = false
}
</script>
