import { useDictStore } from '@/store/modules/dict'
import dayjs from 'dayjs'

/**
 * 获取字典配置-合同账单生成参数
 * @param {String} date YYYY-MM-DD 需要比较的日期(应收日期)
 * @param {String} companyCode 公司编码
 */
export const getDefaultBelongingMonth = (date, companyCode) => {
  if (!date) return ''
  const store = useDictStore()
  const dealDate = dayjs(date).format('YYYY-MM-DD')
  if (!store.dict) return `${dealDate.split('-')[0]}-${dealDate.split('-')[1]}`
  const nextMonth = Number(dealDate.split('-')[1]) + 1
  const day = Number(dealDate.split('-')[2])
  const list = store.dict.CT_BASE_ENUM_Contract_CommonParam
  const dicItem = list.find((item) =>
    companyCode ? item.label === `CT_CON_BelongYmCritical:${companyCode}` : item.label === 'CT_CON_BelongYmCritical'
  )
  if (day >= Number(dicItem?.value)) {
    return `${dealDate.split('-')[0]}-${nextMonth > 9 ? `${nextMonth}` : `0${nextMonth}`}`
  }
  return `${dealDate.split('-')[0]}-${dealDate.split('-')[1]}`
}

/**
 * 简单实现防抖方法
 *
 * 防抖(debounce)函数在第一次触发给定的函数时，不立即执行函数，而是给出一个期限值(delay)，比如100ms。
 * 如果100ms内再次执行函数，就重新开始计时，直到计时结束后再真正执行函数。
 * 这样做的好处是如果短时间内大量触发同一事件，只会执行一次函数。
 *
 * @param fn 要防抖的函数
 * @param delay 防抖的毫秒数
 * @returns {Function}
 */
export function simpleDebounce(fn, delay = 100) {
  let timer = null
  return function () {
    const args = arguments
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}
/**
 * 日期格式化
 * @param date 日期
 * @param block 格式化字符串
 */
export const dateFormat = (date, block) => {
  if (!date) {
    return ''
  }
  let format = block || 'yyyy-MM-dd'
  date = new Date(date)
  const map = {
    M: date.getMonth() + 1, // 月份
    d: date.getDate(), // 日
    h: date.getHours(), // 小时
    m: date.getMinutes(), // 分
    s: date.getSeconds(), // 秒
    q: Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  }
  format = format.replace(/([yMdhmsqS])+/g, (all, t) => {
    let v = map[t]
    if (v !== undefined) {
      if (all.length > 1) {
        v = `0${v}`
        v = v.substr(v.length - 2)
      }
      return v
    } else if (t === 'y') {
      return date
        .getFullYear()
        .toString()
        .substr(4 - all.length)
    }
    return all
  })
  return format
}
