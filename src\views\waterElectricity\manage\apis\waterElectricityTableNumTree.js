import request from '@/apis/http'

// 编辑
export const editWaterElectricityTableNumTree = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricityTableNumTree/edit',
    data
  })
}

// 添加
export const addWaterElectricityTableNumTree = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricityTableNumTree/add',
    data
  })
}

// 树形结构查询
export const getWaterElectricityTableNumTree = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricityTableNumTree/queryTreeList',
    params
  })
}

// 通过 id 查询
export const queryWaterElectricityTableNumTreeById = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricityTableNumTree/queryById',
    params
  })
}

// 分页查询
export const getWaterElectricityTableNumTreeList = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricityTableNumTree/list',
    params
  })
}

// 通过 id 删除
export const deleteWaterElectricityTableNumTreeById = (params) => {
  return request({
    method: 'delete',
    url: '/bas/waterElectricityTableNumTree/delete',
    params
  })
}

// 批量删除
export const batchDeleteWaterElectricityTableNumTree = (params) => {
  return request({
    method: 'delete',
    url: '/bas/waterElectricityTableNumTree/deleteBatch',
    params
  })
}
