<template>
  <div>
    <a-form layout="inline" class="!mb-[16px]">
      <a-form-item>
        <a-button type="primary" @click="handleCompare">
          <i class="a-icon-plus"></i>
          数据比较
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
      </a-form-item>
      <a-form-item label="表名" class="!ml-[40px]">
        <a-input v-model:value="params.dataTable" placeholder="搜索表名" @input="handleInput"></a-input>
      </a-form-item>
      <a-form-item label="数据ID">
        <a-input v-model:value="params.dataId" placeholder="搜索数据ID" @input="handleInput"></a-input>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'dataContent'">
          <div class="line-clamp-2" :title="record.dataContent">{{ record.dataContent }}</div>
        </template>
      </template>
    </a-table>
    <compare-data ref="compareDataRef"></compare-data>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page } from './apis'
import { message } from 'ant-design-vue'
import CompareData from './components/CompareData.vue'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  dataTable: '',
  dataId: ''
})

const columns = [
  { title: '表名', dataIndex: 'dataTable', width: 140, fixed: 'left' },
  { title: '数据ID', dataIndex: 'dataId', width: 160 },
  { title: '版本号', dataIndex: 'dataVersion', width: 80 },
  { title: '数据内容', dataIndex: 'dataContent' },
  { title: '创建人', dataIndex: 'createName', width: 100, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const { selectedRowKeys, selectedRows, onSelectChange } = useTableSelection(list, 'id')

const compareDataRef = ref()
const handleCompare = () => {
  if (selectedRowKeys.value.length !== 2) {
    message.warning('请选择两条数据进行比较')
    return
  }
  const data1 = selectedRows.value[0]
  const data2 = selectedRows.value[1]
  if (!(data1.dataTable === data2.dataTable && data1.dataId === data2.dataId)) {
    message.warning('请选择相同的数据库表和数据ID进行比较!')
    return
  }
  compareDataRef.value.open({
    dataTable: data1.dataTable,
    dataId: data1.dataId
  })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

onMounted(() => {
  onTableChange()
})
</script>
