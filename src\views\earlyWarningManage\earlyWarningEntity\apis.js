import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/alter/alterBizBill/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/alter/alterBizBill/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/alter/alterBizBill/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterBizBill/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterBizBill/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterBizBill/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterBizBill/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/alter/alterBizBill/deleteBatch',
    params
  })
}

export const bizBillTreeSelectList = (params) => {
  return request({
    method: 'get',
    url: '/alter/alterBizBill/bizBillTreeSelectList',
    params
  })
}
export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterBizBill/updateEnableDisableStatus',
    data
  })
}
