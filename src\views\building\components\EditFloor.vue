<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑楼层' : '添加楼层'"
    width="560px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    centered
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '84px' } }" autocomplete="off">
      <a-form-item label="楼层名称" name="name">
        <a-input
          v-model:value="form.name"
          placeholder="请输入楼层名称"
          :maxlength="20"
          show-count
          @keyup.enter="handleConfirm"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { editFloor, addFloor } from '../apis/floor.js'

const { buildingId } = defineProps({
  buildingId: { type: String, required: true }
})

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  id: '',
  name: ''
})

const rules = {
  name: [{ required: true, message: '请输入楼层名称', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form, wyBuilding: buildingId }
    const data = await (form.id ? editFloor(params) : addFloor(params))
    message.success(data.message)
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.id = ''
  form.name = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
