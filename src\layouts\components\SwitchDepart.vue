<template>
  <a-modal
    v-model:open="visible"
    title="切换公司"
    width="1000px"
    wrap-class-name="common-modal switch-depart-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <div class="flex items-center mb-[12px]">
      <i class="a-icon-zuzhigoujia text-[18px] mr-[8px]"></i>
      <strong class="text-[18px]">组织架构信息</strong>
    </div>
    <div class="border border-[#E6E9F0] rounded-[8px] p-[24px]">
      <div class="flex items-center mb-[8px] px-[24px]">
        <span class="flex-1 text-tertiary">公司名称</span>
        <span class="flex-1 text-tertiary px-[16px]">部门名称</span>
        <span class="text-tertiary w-[130px]">操作</span>
      </div>
      <ul>
        <li
          v-for="item in departList"
          :key="item.id"
          class="depart-li"
          :class="{ active: currentDepart.id === item.id }"
        >
          <span class="text-[16px] line-clamp-2 flex-1 text-secondary">{{ item.company_dictText }}</span>
          <span class="text-[16px] line-clamp-2 flex-1 px-[16px] text-secondary">{{ item.departName }}</span>
          <span class="text-[16px] text-[#6EC21B] w-[130px]" v-if="currentDepart.id === item.id">
            <i class="a-icon-selected-circle"></i>
            当前身份
          </span>
          <span class="primary-btn text-[16px] w-[130px]" v-else @click="handleSwitch(item)">切换到该身份</span>
        </li>
      </ul>
    </div>
  </a-modal>
</template>

<script setup>
import { selectDepart } from '@/views/system/user/apis'
import { useUserStore } from '@/store/modules/user'

const { departList } = defineProps({
  departList: { required: true, type: Array }
})

const userStore = useUserStore()

const visible = ref(false)

// 当前部门信息
const currentDepart = reactive({
  id: '',
  name: ''
})

const departId = ref('') // 当前部门id

const open = (deptId) => {
  visible.value = true
  departId.value = deptId
  const data = departList.find((i) => i.id === deptId)
  if (data) {
    currentDepart.id = data.id
    currentDepart.name = data.displayName
  }
}

const handleSwitch = (data) => {
  currentDepart.id = data.id
  currentDepart.name = data.displayName
}

const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  if (departId.value === currentDepart.id) {
    visible.value = false
    return
  }
  try {
    confirmLoading.value = true
    const { result } = await selectDepart({ currentDepart: currentDepart.id, username: userStore.userInfo.username })
    userStore.userInfo = result.userInfo
    window.location.reload()
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.switch-depart-modal {
  .depart-li {
    height: 56px;
    border-top: 1px solid #e6e9f0;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    padding: 0 24px;
    &.active {
      background-color: #edfbe2;
      border-radius: 8px;
      border-top: none;
    }
  }
}
</style>
