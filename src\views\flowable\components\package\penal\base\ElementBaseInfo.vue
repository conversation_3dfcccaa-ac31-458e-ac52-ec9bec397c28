<template>
  <div class="panel-tab__content">
    <a-form :model="elementBaseInfo" size="small" label-width="90px" @submit.prevent>
      <a-form-item label="ID" :rules="[{ required: true, message: '请输入节点ID' }]">
        <a-input
          v-model:value="elementBaseInfo.id"
          :disabled="idEditDisabled"
          clearable
          @change="updateBaseInfo('id')"
        />
      </a-form-item>
      <a-form-item
        label="名称"
        name="name"
        :rules="[
          { required: ['bpmn:Process', 'bpmn:UserTask'].includes(elementBaseInfo.$type), message: '请输入名称' }
        ]"
      >
        <a-input v-model:value="elementBaseInfo.name" clearable @change="updateBaseInfo('name')" />
      </a-form-item>
      <!--流程的基础属性-->
      <template v-if="elementBaseInfo.$type === 'bpmn:Process'">
        <a-form-item
          label="流程使用公司"
          name="processCategory"
          :rules="[{ required: true, message: '请选择流程使用公司' }]"
        >
          <company-select
            v-model="elementBaseInfo.processCategory"
            placeholder="请选择流程使用公司"
            :allow-clear="false"
            @change="updateBaseInfo('processCategory')"
          ></company-select>
        </a-form-item>
        <a-form-item
          label="流程业务单据"
          name="classNameKey"
          :rules="[{ required: true, message: '请选择流程业务单据' }]"
        >
          <a-select
            v-model:value="elementBaseInfo.classNameKey"
            placeholder="请选择流程业务单据"
            @change="updateBaseInfo('classNameKey')"
          >
            <a-select-option v-for="f in flowBizBillList" :key="f.classNameKey" :value="f.classNameKey">
              {{ f.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <!-- <a-form-item label="版本标签">
          <a-input v-model:value="elementBaseInfo.versionTag" clearable @change="updateBaseInfo('versionTag')" />
        </a-form-item> -->
        <a-form-item label="可执行">
          <a-switch
            v-model:checked="elementBaseInfo.isExecutable"
            active-text="是"
            inactive-text="否"
            @change="updateBaseInfo('isExecutable')"
          />
        </a-form-item>
      </template>
      <a-form-item v-if="elementBaseInfo.$type === 'bpmn:SubProcess'" label="状态">
        <a-switch
          v-model:checked="elementBaseInfo.isExpanded"
          active-text="展开"
          inactive-text="折叠"
          @change="updateBaseInfo('isExpanded')"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { bizBillTreeSelectList } from '@/views/flowable/flowBizBill/apis'

export default {
  name: 'ElementBaseInfo',
  props: {
    businessObject: Object,
    type: String,
    idEditDisabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      elementBaseInfo: { id: '', name: '', processCategory: '', classNameKey: '', versionTag: '', isExecutable: true },
      flowBizBillList: []
    }
  },
  watch: {
    businessObject: {
      immediate: false,
      handler(val) {
        if (val) {
          this.$nextTick(() => this.resetBaseInfo())
        }
      }
    }
  },
  mounted() {
    this.loadFlowBizList()
  },
  methods: {
    async loadFlowBizList() {
      const { result } = await bizBillTreeSelectList({})
      this.flowBizBillList = result
    },
    resetBaseInfo() {
      // eslint-disable-next-line vue/no-undef-properties
      this.bpmnElement = window?.bpmnInstances?.bpmnElement || {}
      this.elementBaseInfo = JSON.parse(JSON.stringify(this.bpmnElement.businessObject))
      // eslint-disable-next-line vue/no-undef-properties
      if (this.elementBaseInfo && this.elementBaseInfo.$type === 'bpmn:SubProcess') {
        // eslint-disable-next-line vue/no-undef-properties
        this.elementBaseInfo.isExpanded = this.elementBaseInfo.di?.isExpanded
      }
    },
    updateBaseInfo(key) {
      if (key === 'id') {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          id: this.elementBaseInfo[key],
          di: { id: `${this.elementBaseInfo[key]}_di` }
        })
        return
      }
      if (key === 'isExpanded') {
        window?.bpmnInstances?.modeling.toggleCollapse(this.bpmnElement)
        return
      }
      const attrObj = Object.create(null)
      attrObj[key] = this.elementBaseInfo[key]
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, attrObj)
    }
  },
  beforeUnmount() {
    this.bpmnElement = null
  }
}
</script>
