<template>
  <a-modal
    v-model:open="visible"
    :title="title"
    width="450px"
    class="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '100px' } }"
      autocomplete="off"
    >
      <a-form-item label="方案编码" name="schemeNumber">
        <a-input v-model:value="ruleForm.schemeNumber" placeholder="请输入方案编码" :maxlength="50" show-count />
      </a-form-item>
      <a-form-item label="方案名称" name="schemeName">
        <a-input v-model:value="ruleForm.schemeName" placeholder="请输入方案名称" :maxlength="50" show-count />
      </a-form-item>
      <a-form-item label="物业管理公司" name="manageCompany">
        <company-select v-model="ruleForm.manageCompany" placeholder="请选择公司" disabled></company-select>
      </a-form-item>
      <a-form-item label="核销条件">
        <div class="flex flex-wrap">
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.collectionCompanySame">归集公司相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.customerSame">客户相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.paymentTypeSame">款项类型相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.leaseUnitSame">租赁单元相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.contractNumberSame">合同编号相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.operatorDepartSame">业务部门相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.operatorSame">业务员相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.serviceCenterSame">服务处相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.parkSame">停车场相同</a-checkbox>
          <a-checkbox class="w-[50%]" v-model:checked="ruleForm.carportNumSame">车位号相同</a-checkbox>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { add, edit } from '../apis'
import { useUserStore } from '@/store/modules/user'
// 弹窗可见性
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    Object.assign(ruleForm, data)
    return
  }
  ruleForm.manageCompany = userInfo.value.currentCompany || ''
}
defineExpose({ open })
const store = useUserStore()
const userInfo = computed(() => store.userInfo)
const emit = defineEmits(['loadData'])
const title = computed(() => {
  return ruleForm.id ? '编辑核销方案' : '新建核销方案'
})
const ruleForm = reactive({
  id: '',
  manageCompany: '',
  schemeName: '',
  schemeNumber: '',
  collectionCompanySame: false,
  customerSame: false,
  paymentTypeSame: false,
  leaseUnitSame: false,
  contractNumberSame: false,
  operatorDepartSame: false,
  operatorSame: false,
  serviceCenterSame: false,
  parkSame: false,
  carportNumSame: false
  // remark: '',
  // createBy: '',
  // createTime: '',
  // updateBy: '',
  // updateTime: '',
  // auditBy: '',
  // auditTime: '',
  // attachmentIds: '',
  // sourceBillId: '',
  // sourceBillEntryId: '',
  // ctrlUnit: ''
})
const rules = {
  schemeNumber: [{ required: true, message: '请输入方案编码', trigger: ['blur'] }],
  schemeName: [{ required: true, message: '请方案名称', trigger: ['blur'] }],
  manageCompany: [{ required: true, message: '请选择公司', trigger: ['change'] }]
}

const confirmLoading = ref(false)
// 提交
const handleConfirm = async () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    const data = await (ruleForm.id ? edit(ruleForm) : add(ruleForm))
    message.success(data.message)
    confirmLoading.value = false
    handleCancel()
    emit('loadData')
  } catch {
    confirmLoading.value = false
  }
}
// 取消
const handleCancel = () => {
  Object.assign(ruleForm, {
    id: '',
    manageCompany: '',
    schemeName: '',
    schemeNumber: '',
    collectionCompanySame: false,
    customerSame: false,
    paymentTypeSame: false,
    leaseUnitSame: false,
    contractNumberSame: false,
    operatorDepartSame: false,
    operatorSame: false,
    serviceCenterSame: false,
    parkSame: false,
    carportNumSame: false
  })
  visible.value = false
}
</script>
