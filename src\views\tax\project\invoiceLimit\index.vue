<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <!-- <s-input
          v-model="params.remark"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input> -->
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <edit-limit ref="editLimitRef" @refresh="refresh"></edit-limit>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('项目发票限额.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel } from './apis.js'
import EditLimit from './components/EditLimit.vue'
import { formatCurrency } from '@/utils/number'
import { Modal, message } from 'ant-design-vue'

const props = defineProps({
  manageCompany: { type: String, default: '' }
})

const params = reactive({
  column: 'createTime',
  order: 'desc',
  remark: undefined,
  invoiceForm: undefined,
  limitAmount: undefined
})

const searchList = [
  {
    label: '开票类型',
    name: 'invoiceForm',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_ReceiveItemPaymentTypeRate_InvoiceForm'
  },
  { label: '限额', name: 'limitAmount', type: 's-input' },
  { label: '备注', name: 'remark', type: 's-input' }
]

const defaultColumns = [
  { title: '开票类型', dataIndex: 'invoiceForm_dictText' },
  { title: '限额', dataIndex: 'limitAmount', customRender: ({ text }) => formatCurrency(text) },
  { title: '备注', dataIndex: 'remark' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

watch(
  () => props.manageCompany,
  (value) => {
    params.manageCompany = value
    if (!value) {
      return
    }
    onTableChange()
  },
  {
    immediate: true
  }
)

// let timer
// const handleInput = () => {
//   clearTimeout(timer)
//   timer = setTimeout(() => {
//     onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
//   }, 600)
// }

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editLimitRef = ref()
const handleAdd = () => {
  editLimitRef.value.open(props.manageCompany)
}
const handleEdit = (data) => {
  editLimitRef.value.open(props.manageCompany, data.id)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除项目发票限额信息？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('项目发票限额数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
