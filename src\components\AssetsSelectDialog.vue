<!-- 资产选择弹窗 -->
<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="70%"
    title="选择资产"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="mb-[12px] flex justify-between items-center">
      <a-form autocomplete="off" layout="inline">
        <a-form-item class="!mb-[12px]" label="类别">
          <assets-category-tree-filter class="!w-[280px]" @tree-node-change="handleTreeNodeChange" />
        </a-form-item>
        <a-form-item class="!mb-[12px]" label="">
          <s-input
            class="!w-[280px]"
            v-model:value="search.name"
            placeholder="请输入资产名称"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <a-button class="!mb-[12px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <search-more
          class="!mb-[12px]"
          v-model="searchFilter"
          :search-list="searchList"
          @filterItemChange="filterItemChange"
          @searchChange="onTableChange"
        ></search-more>
      </a-form>

      <div class="mb-[12px]">
        <a-checkbox v-model:checked="viewSelected">只看已选</a-checkbox>
      </div>
    </div>
    <a-table
      ref="tableRef"
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: 600, x: 2200 }"
      :pagination="viewSelected ? false : pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        type: multiple ? 'checkbox' : 'radio',
        onChange: onSelectChange
      }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'detailAddress'">
          <div>{{ renderRegion(record.pcaCode) }}{{ record.detailAddress || '-' }}</div>
        </template>
        <template v-if="column.dataIndex === 'layerNum'">
          <div>
            <span>{{ record.layerNum }}</span>
            <span v-if="record.layerNum && record.totalLayerNum">/</span>
            <span>{{ record.totalLayerNum }}</span>
          </div>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import AssetsCategoryTreeFilter from '@/views/assets/manage/components/AssetsCategoryTreeFilter.vue'
import { renderDict, renderDictTag, renderRegion } from '@/utils/render'
import areaList from '@/json/region.json'
import {
  getF7List,
  getQueryWyBuildingByMainId,
  getQueryWyFloorByMainId
  // getAssetsCategoryTree
  // updateEnableDisableStatus
} from '@/views/assets/manage/apis.js'

import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { isOrNotDic } from '@/store/modules/dict.js'
import { message } from 'ant-design-vue'
// import { getCustomerList } from '@/views/customer/manage/apis'
const viewSelected = ref(false) // 只看已选
const { multiple, asyncFunc, params } = defineProps({
  multiple: { default: false, type: Boolean },
  asyncFunc: { default: getF7List, type: Function },
  params: {
    default: () => {
      return {}
    },
    type: Object
  }
})
const visible = ref(false)
const search = ref({
  column: 'number',
  order: 'desc',
  treeId: '',
  name: ''
})
const searchFilter = ref({})
const wyBuildingPar = reactive({ id: '' })
const getQueryWyBuildingByMainIdFunc = computed(() => {
  wyBuildingPar.id
  return () => getQueryWyBuildingByMainId(wyBuildingPar)
})
const wyFloorPar = reactive({ id: '' })
const getQueryWyFloorByMainIdFunc = computed(() => {
  wyFloorPar.id
  return () => getQueryWyFloorByMainId(wyFloorPar)
})
const searchList = reactive([
  {
    label: '单据审核状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择单据审核状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  { label: '资产编号', name: 'number', type: 'input', placeholder: '请输入资产编号' },
  { label: '权证号', name: 'ownerNumber', type: 'input', placeholder: '请输入权证号' },
  {
    label: '关联项目',
    name: 'wyProject',
    type: 'projectSelect',
    placeholder: '请选择关联项目'
    // listFunc: projectPage
  },
  {
    label: '楼栋',
    name: 'wyBuilding',
    type: 'api',
    placeholder: '请选择楼栋',
    listFunc: getQueryWyBuildingByMainIdFunc
  },
  {
    label: '楼层',
    name: 'wyFloor',
    type: 'api',
    placeholder: '请选择楼层',
    listFunc: getQueryWyFloorByMainIdFunc
  },
  { label: '区域', name: 'pcaCode', type: 'cascader', placeholder: '请选择区域', list: areaList },
  { label: '详细地址', name: 'detailAddress', type: 'input', placeholder: '请输入详细地址' },
  {
    label: '租金归集公司',
    name: 'collectionCompany',
    type: 'companySelect',
    companyType: 'all',
    placeholder: '请选择租金归集公司'
  },
  {
    label: '资产权属公司',
    name: 'ownerCompany',
    type: 'companySelect',
    companyType: 'all',
    placeholder: '请选择资产权属公司'
  },
  { label: '物业管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择物业管理公司' },
  { label: '资产类型', name: 'assetsType', type: 'dic', placeholder: '请选择资产类型', code: 'CT_BAS_AssetsType' },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dic',
    placeholder: '请选择业务状态',
    code: 'CT_BASE_ENUM_HouseOwner_BizStatus'
  },

  {
    label: '取得来源',
    name: 'acquisitionMethod',
    type: 'dic',
    placeholder: '请选择取得来源',
    code: 'CT_BAS_AcquisitionMethod'
  },
  {
    label: '产权情况',
    name: 'propertyRightStatus',
    type: 'dic',
    placeholder: '请选择产权情况',
    code: 'CT_BASE_ENUM_HouseOwner_PropertyRightStatus'
  },
  { label: '产权用途', name: 'propertyUse', type: 'dic', placeholder: '请选择产权用途', code: 'CT_BAS_PropertyUse' },
  { label: '使用权类型', name: 'landNature', type: 'dic', placeholder: '请选择使用权类型', code: 'CT_BAS_LandNature' },
  {
    label: '土地建设情况',
    name: 'landConstructionSituation',
    type: 'dic',
    placeholder: '请选择土地建设情况',
    code: 'CT_BAS_LandCS'
  },
  {
    label: '房产类型',
    name: 'houseType',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BASE_ENUM_HouseOwner_HouseType'
  },
  {
    label: '建筑结构',
    name: 'buildStructrue',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BAS_BuildStructrue'
  },
  { label: '户型', name: 'houseModel', type: 'input', placeholder: '请输入户型' },
  {
    label: '消防等级',
    name: 'firefightingRate',
    type: 'dic',
    placeholder: '请选择消防等级',
    code: 'CT_BAS_FirefightingRate'
  },
  {
    label: '房屋安全等级',
    name: 'houseSafeRate',
    type: 'dic',
    placeholder: '请选择房屋安全等级',
    code: 'CT_BAS_HouseSafeRate'
  },
  { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
  {
    label: '创建人',
    name: 'createBy',
    type: 'userSelect',
    placeholder: '请选择创建人'
  },
  { label: '最近修改时间', name: 'updateTime', type: 'date', placeholder: '请选择最近修改时间' },
  {
    label: '最近修改人',
    name: 'updateBy',
    type: 'userSelect',
    placeholder: '请选择最近修改人'
  },
  { label: '审核时间', name: 'auditTime', type: 'date', placeholder: '请选择审核时间' },
  {
    label: '审核人',
    name: 'auditBy',
    type: 'userSelect',
    placeholder: '请选择审核人'
  },

  { label: '权证获得日期', name: 'warrantsDate', type: 'date', placeholder: '请选择权证获得日期' },
  {
    label: '房地权证合一',
    name: 'isUnionCertificate',
    type: 'select',
    placeholder: '请选择房地权证合一',
    list: isOrNotDic
  },
  { label: '建筑年份', name: 'buildYear', type: 'year', format: 'YYYY', placeholder: '请选择建筑年份' }
])
const columns = [
  { title: '资产名称', dataIndex: 'name', width: 180, fixed: true },
  {
    title: '单据审核状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '资产编号', dataIndex: 'number' },
  { title: '权证号', dataIndex: 'ownerNumber', ellipsis: true },
  { title: '产权用途', dataIndex: 'propertyUse' },
  {
    title: '使用权类型',
    dataIndex: 'landNature',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  },
  { title: '地址', dataIndex: 'allAddress', ellipsis: true },

  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_BizStatus')
  },
  { title: '建筑面积', dataIndex: 'structureArea' },
  { title: '宗地面积', dataIndex: 'floorArea' },
  {
    title: '房产类型',
    dataIndex: 'houseType',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_HouseType')
  },
  {
    title: '资产类型',
    dataIndex: 'assetsType',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_AssetsType')
  },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', ellipsis: true },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '备注', dataIndex: 'remark', ellipsis: true }
]
const open = (selectedIds = []) => {
  Object.assign(searchFilter.value, params)
  onTableChange()
  if (selectedIds && selectedIds.length > 0) {
    selectedRowKeys.value = selectedIds
    // 根据 id 从 list 中获取完整的对象数据
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  visible.value = true
}
defineExpose({ open })

/**
 * 处理树节点变更
 */
const handleTreeNodeChange = (nodeId) => {
  search.value.treeId = nodeId
  onTableChange({ current: 1 })
}
const { list, pagination, tableLoading, onTableFetch } = usePageTable(asyncFunc, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
    item.allAddress = `${renderRegion(item.pcaCode)}${item.detailAddress}`
  })
  return list
})
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  // 处于只看已选模式
  if (viewSelected.value) {
    const id = (selectedRowKeys.value || []).join(',')
    onTableFetch({
      pageNo: 1,
      pageSize: 200,
      ...search.value,
      ...searchFilter.value,
      pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(';') : '',
      id: id || '0'
    })
    return
  }
  onTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...search.value,
    ...searchFilter.value,
    pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(';') : ''
  })
}
watch(viewSelected, (val) => {
  if (!visible.value) return
  if (val) {
    const id = (selectedRowKeys.value || []).join(',')
    onTableFetch({
      pageNo: 1,
      pageSize: 200,
      ...search.value,
      ...searchFilter.value,
      pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(';') : '',
      id: id || '0'
    })
  } else {
    onTableChange({
      pageNo: 1,
      pageSize: 10,
      ...search.value,
      ...searchFilter.value,
      pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(';') : ''
    })
  }
})

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}
// 筛选项字段和对应的值
const filterItemChange = (id, name) => {
  // 关联项目
  if (name === 'wyProject') {
    searchList.forEach((item) => {
      if (item.name === 'wyBuilding') {
        wyBuildingPar.id = id
      }
    })
  }
  // 楼栋
  if (name === 'wyBuilding') {
    searchList.forEach((item) => {
      if (item.name === 'wyFloor') {
        wyFloorPar.id = id
      }
    })
  }
}
// const handleStatusChange = async (data, val) => {
//   if (data.loading) return
//   try {
//     data.loading = true
//     await updateEnableDisableStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
//     data.loading = false
//     message.success('保存成功')
//     data.status = val ? 'ENABLE' : 'DISABLE'
//   } catch {
//     data.loading = false
//     data.checked = !val
//   }
// }

const emits = defineEmits(['selectChange'])
const handleOk = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  visible.value = false
}
/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}
</script>
