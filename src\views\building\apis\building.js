import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const buildingPage = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyBuilding/list',
    params
  })
}

export const buildingDetail = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyBuilding/queryById',
    params
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyBuilding/updateWyBuildingEnableDisableStatus',
    data
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/wyBuilding/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

export const importExcel = (data, controller) => {
  return advanceUpload('/bas/wyBuilding/importExcel', data, controller)
}

export const addBuilding = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyBuilding/add',
    data
  })
}

export const editBuilding = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyBuilding/edit',
    data
  })
}

export const deleteBuilding = (params) => {
  return request({
    method: 'delete',
    url: '/bas/wyBuilding/deleteBatch',
    params
  })
}

export const queryFloor = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyBuilding/queryWyFloorByMainId',
    params
  })
}

// 校验楼栋名称是否重复
export const verifyName = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyBuilding/verifyNameExists',
    data
  })
}

export const getWater = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterShareFormulaRelations/list',
    params
  })
}

export const checkBeUsed = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterShareFormulaRelations/verifyWaterEleTableNumExists',
    data
  })
}
