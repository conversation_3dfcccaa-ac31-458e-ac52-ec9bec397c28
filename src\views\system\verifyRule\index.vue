<template>
  <div>
    <a-form layout="inline" class="!mb-[16px]">
      <a-form-item>
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新增
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
      </a-form-item>
      <a-form-item label="规则名称" class="!ml-[40px]">
        <s-input
          v-model="params.ruleName"
          placeholder="搜索规则名称"
          :show-search-icon="false"
          @input="handleInput"
        ></s-input>
      </a-form-item>
      <a-form-item label="规则编码">
        <s-input
          v-model="params.ruleCode"
          placeholder="搜索规则编码"
          :show-search-icon="false"
          @input="handleInput"
        ></s-input>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleTest(record)">功能测试</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <rule-test ref="ruleTestRef"></rule-test>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch } from './apis'
import { message, Modal } from 'ant-design-vue'
import Edit from './components/Edit.vue'
import RuleTest from './components/RuleTest.vue'

const params = reactive({
  column: 'createTime',
  order: 'desc',
  ruleName: '',
  ruleCode: ''
})

const columns = [
  { title: '规则名称', dataIndex: 'ruleName', width: 200, fixed: 'left' },
  { title: '规则编码', dataIndex: 'ruleCode', width: 200 },
  { title: '规则描述', dataIndex: 'ruleDescription', ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 180, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open({
    id: data.id,
    ruleCode: data.ruleCode,
    ruleName: data.ruleName,
    ruleDescription: data.ruleDescription,
    ruleJson: data.ruleJson
  })
}

const ruleTestRef = ref()
const handleTest = (data) => {
  ruleTestRef.value.open(data.ruleCode)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除该项？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

onMounted(() => {
  onTableChange()
})
</script>
