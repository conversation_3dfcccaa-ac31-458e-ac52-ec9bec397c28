<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}退款申请`"
    class="edit-refund-drawer common-drawer"
    placement="right"
    width="1072px"
    :confirm-loading="confirmLoading"
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-align="left"
        :label-col="{ style: { width: '100px' } }"
      >
        <a-form-item label="业务日期" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="退款客户" name="customer">
          <a-form-item-rest>
            <f7-select v-model="formData.customer" f7-type="customer" placeholder="请选择退款客户" />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="物业管理公司" name="manageCompany">
          <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司" disabled></company-select>
        </a-form-item>

        <a-form-item label="业务部门" name="operatorDepart">
          <depart-select v-model="formData.operatorDepart" @change="onDepartChange" />
        </a-form-item>

        <a-form-item label="经办人" name="operator">
          <a-form-item-rest>
            <f7-select
              v-model="formData.operator"
              f7-type="user"
              :depart-id="formData.operatorDepart"
              placeholder="请选择经办人"
            />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="单据来源" name="billSource">
          <dict-select
            v-model="formData.billSource"
            placeholder="请选择单据来源"
            code="CT_BASE_ENUM_RefundReqBill_BillSource"
          ></dict-select>
        </a-form-item>

        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :maxlength="255"
            :rows="4"
            show-count
          />
        </a-form-item>
      </a-form>
      <div class="flex items-center justify-between mt-[40px] mb-[12px]">
        <strong class="text-[16px]">退款明细</strong>
        <a-button type="primary" size="medium" @click="handleAddRefundItem">
          <i class="a-icon-plus"></i>
          添加明细
        </a-button>
      </div>
      <a-table
        :columns="refundColumns"
        :data-source="formData.refundReqBillEntryList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'number'">
            <i class="a-icon-remove cursor-pointer mr-2 text-red-500" @click="handleRemoveRefundItem(index)"></i>
            {{ record.number || record.detailBill_dictText }}
          </template>
          <template v-else-if="column.dataIndex === 'thisRefundAmt'">
            <a-input-number
              v-model:value="record.thisRefundAmt"
              :min="0"
              :max="record.balance"
              :precision="2"
              placeholder="请输入退款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
        </template>
      </a-table>
      <div class="p-[16px] bg-[#f7f8fa] rounded-[8px] mt-[40px] border border-[#e6e9f0]">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-[24px] text-[#1d335c]">
              <span v-if="formData.refundReqAmount < 0">应退：</span>
              <span v-else>调整后待核销合计：</span>
              <span class="text-[#f03a1d] font-bold">{{ renderMoney(formData.refundReqAmount, 2, '元') }}</span>
            </p>
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- 退款明细选择器 -->
  <refund-details-modal ref="refundDetailSelectorRef" @update-list="handleRefundDetailConfirm" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/modules/user'
import { renderMoney } from '@/utils/render'
import { addRefundReqBill, editRefundReqBill, submitRefundReqBill, queryRefundReqBillEntries } from '../apis'
import RefundDetailsModal from './RefundDetailsModal.vue'

const emits = defineEmits(['refresh'])

const store = useUserStore()

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const refundDetailSelectorRef = ref()

const formDataDefault = {
  bizDate: undefined,
  customer: undefined,
  operator: undefined,
  operatorDepart: undefined,
  billSource: undefined,
  manageCompany: undefined,
  remark: undefined,
  refundReqAmount: 0,
  refundReqBillEntryList: []
}

const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期' }],
  customer: [{ required: true, message: '请选择退款客户' }],
  operator: [{ required: true, message: '请选择经办人' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }],
  billSource: [{ required: true, message: '请选择单据来源' }]
}

/**
 * 退款明细表格列配置
 */
const refundColumns = [
  { title: '账单编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 160 },
  { title: '合同单据', dataIndex: 'contract_dictText', width: 160, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '结束日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '应收金额', dataIndex: 'paymentAmount', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '减免金额', dataIndex: 'remission', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '实际应收',
    dataIndex: 'actualReceiveAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已收金额', dataIndex: 'paid', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '未收金额', dataIndex: 'residual', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已转款抵扣',
    dataIndex: 'transferDeduction',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已退金额', dataIndex: 'refunded', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '尾差已处理',
    dataIndex: 'offDifference',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '剩余可退',
    dataIndex: 'residueRefundAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '本次退款金额',
    dataIndex: 'thisRefundAmt',
    width: 150,
    fixed: 'right'
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

/**
 * 打开抽屉并初始化表单数据
 * @param {Object} record - 编辑时的记录数据，新建时为空
 */
const open = async (record, detailList = []) => {
  visible.value = true
  formData.refundReqBillEntryList = detailList

  formData.bizDate = dayjs().format('YYYY-MM-DD')
  // 设置当前用户为默认经办人和经办部门
  formData.operator = store.userInfo.id
  formData.operatorDepart = store.userInfo.currentDepart
  formData.manageCompany = store.userInfo.currentCompany

  if (record?.id) {
    Object.assign(formData, record)
    // 加载退款申请单分录列表
    await loadRefundEntries(record.id)
  }
}

const onDepartChange = () => {
  formData.operator = ''
}

/**
 * 加载退款申请单分录列表
 * @param {string} mainId - 主表ID
 */
const loadRefundEntries = async (mainId) => {
  try {
    confirmLoading.value = true
    const response = await queryRefundReqBillEntries({ id: mainId })
    if (response.success && response.result) {
      formData.refundReqBillEntryList = response.result
    }
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 取消操作，关闭抽屉并重置表单
 */
const handleCancel = () => {
  Object.assign(formData, formDataDefault)
  formData.refundReqBillEntryList = []
  emits('refresh')
  visible.value = false
}

/**
 * 打开退款明细选择器
 */
const handleAddRefundItem = () => {
  if (!formData.customer) {
    message.warning('请先选择退款客户')
    return
  }
  // 获取已选择的明细，构造为Modal所需的格式
  const selectedRefundDetails = formData.refundReqBillEntryList.map((item) => ({
    id: item.detailBillEntry
  }))

  const params = {
    customer: formData.customer
  }

  refundDetailSelectorRef.value?.open(selectedRefundDetails, params)
}

/**
 * 退款明细选择确认回调
 * @param {Array} selectedRows - 选中的明细数据
 */
const handleRefundDetailConfirm = (selectedRows) => {
  // 重新组装选中的明细数据到 refundReqBillEntryList
  selectedRows.map((item) => {
    const data = formData.refundReqBillEntryList.find((i) => i.detailBillEntry === item.id)
    if (data) return
    formData.refundReqBillEntryList.push({
      ...item,
      id: undefined,
      detailBill: item.parent,
      detailBillEntry: item.id,
      residueRefundAmount: item.balance,
      thisRefundAmt: 0,
      remark: ''
    })
  })

  if (selectedRows && selectedRows.length > 0) {
    message.success(`已选择 ${selectedRows.length} 条退款明细`)
  } else {
    message.info('已清空退款明细')
  }
}

/**
 * 删除退款明细项
 * @param {number} index - 要删除的行索引
 */
const handleRemoveRefundItem = (index) => {
  formData.refundReqBillEntryList.splice(index, 1)
}

/**
 * 验证明细数据
 */
const validateDetails = () => {
  // 验证退款明细
  for (let i = 0; i < formData.refundReqBillEntryList.length; i++) {
    const item = formData.refundReqBillEntryList[i]
    if (!item.thisRefundAmt || item.thisRefundAmt < 0) {
      message.error(`退款明细第 ${i + 1} 行的本次退款金额必须大于0`)
      return false
    }

    // 校验本次退款金额不能大于可抵退转金额
    if (item.thisRefundAmt > item.residueRefundAmount) {
      message.error(`退款明细第 ${i + 1} 行的本次退款金额不能大于可抵退转金额`)
      return false
    }
  }

  return true
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    await formRef.value?.validate()

    // 验证明细数据
    if (!validateDetails()) {
      return
    }

    // 根据操作类型选择对应的API
    const api = !isTemporary ? submitRefundReqBill : formData.id ? editRefundReqBill : addRefundReqBill

    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '新建'
    message.success(`退款申请${action}成功`)

    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 提交退款申请
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存退款申请
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 监听退款明细列表变化，自动更新退款总金额
 */
watch(
  () => formData.refundReqBillEntryList,
  (newList) => {
    formData.refundReqAmount = newList.reduce((total, item) => {
      return total + (item.thisRefundAmt || 0)
    }, 0)
  },
  { deep: true }
)

onMounted(() => {
  // 从合同-退租清算，点击“申请退款”跳转过来
  const infoStr = sessionStorage.getItem('refundFromClearing')
  if (!infoStr) return
  const info = JSON.parse(infoStr)
  formData.customer = info.customer
  formData.billSource = info.billSource
  open(
    null,
    info.list.map((item) => ({
      ...item,
      id: '',
      detailBill: item.parent,
      detailBillEntry: item.id,
      residueRefundAmount: item.balance,
      thisRefundAmt: 0,
      remark: ''
    }))
  )
  sessionStorage.removeItem('refundFromClearing')
})

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.edit-refund-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>
