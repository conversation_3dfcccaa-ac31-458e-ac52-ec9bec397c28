import { useDictStore } from '@/store/modules/dict'
import StatusTag from '@/components/StatusTag.vue'
import areaList from '@/json/region.json'
import Decimal from 'decimal.js'

const getDictItem = (value, code) => {
  if (typeof value === 'number') {
    value = String(value)
  }
  if (!value || !code) return ''
  const store = useDictStore()
  if (!store.dict) return ''
  if (!Object.keys(store.dict).length) return ''
  const list = store.dict[code]
  if (!(list && list.length)) return ''
  return list.find((i) => i.value === String(value))
}

/**
 * 渲染字典键值
 * @param {String} value 字典键值
 * @param {String} code 字典编码
 */
export const renderDict = (value, code) => {
  const data = getDictItem(value, code)
  return data ? data.label : ''
}

/**
 * 在a-table中以tag样式渲染字典值
 * @param {String} value 字典值
 * @param {String} code 字典编码
 * @param {String} type tag=经典标签样式 dot=前面有一个圆点的样式
 * @param {String} color 自定义颜色值
 */
export const renderDictTag = (value, code, type, color) => {
  const data = getDictItem(value, code)
  if (!data) return ''
  return h(
    StatusTag,
    {
      dictValue: value,
      dictCode: code,
      color: color || (data && data.color),
      type
    },
    () => data.label
  )
}

/**
 * 渲染布尔值
 * @param {boolean || undefined} value
 */
export const renderBoolean = (value) => {
  return value ? '是' : value === false ? '否' : ''
}

/**
 * 根据省市区逗号拼接字符串，查找对应的省市区名称，并拼接返回
 * @param {string} code
 */
export const renderRegion = (code) => {
  if (!code) return ''
  const codeArr = code.split(';')
  const [province, city, district] = codeArr
  const provinceData = areaList.find((i) => i.value === province)
  if (!provinceData) return ''
  const cityData = provinceData.children.find((i) => i.value === city)
  if (!cityData) return provinceData.label
  const districtData = cityData.children.find((i) => i.value === district)
  if (!districtData) return `${provinceData.label}${cityData.label}`
  return `${provinceData.label}${cityData.label}${districtData.label}`
}

/**
 * 渲染金额，金额保留2位小数 单价6位小数 税率（面积，数量）4位小数，金额要有千分位处理
 * @param {number | string | undefined} value 金额或税率
 * @param {2 | 4 | 6} digit 保留的位数
 * @param {string} unit 是否需要单位，如传入元，则返回20元，如传入m²，则返回20m²
 */
export const renderMoney = (value, digit = 2, unit) => {
  if ([undefined, null, ''].includes(value)) return ''
  if (!(!isNaN(parseFloat(value)) && isFinite(value))) throw new Error('传入的value不是合法数字')
  if (![2, 4, 6].includes(digit)) throw new Error('传入的位数有误，只能传入2,4,6')
  if (value === 0) return `${new Decimal(0).toFixed(digit, Decimal.ROUND_HALF_UP)}${unit || ''}`
  const result = new Decimal(Number(value)).toFixed(digit, Decimal.ROUND_HALF_UP) // 四舍五入的结果
  const [integer, decimal] = result.split('.')
  const thousand = parseInt(integer).toLocaleString('en-US') // 整数部分添加千分位
  return decimal ? `${thousand}.${decimal}${unit || ''}` : `${thousand}${unit || ''}`
}
