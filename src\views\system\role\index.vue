<template>
  <div>
    <div class="flex items-center mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新增
      </a-button>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <a-input
        v-model:value="params.roleName"
        placeholder="搜索角色名称"
        @input="handleInput"
        class="ml-[40px] !w-[240px]"
      >
        <template #prefix>
          <i class="a-icon-search text-primary"></i>
        </template>
      </a-input>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'remark'">
          <div class="line-clamp-2" :title="record.remark">{{ record.remark }}</div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleViewUser(record)">用户</span>
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleAuth(record)">授权</span>
          <a-popconfirm title="是否确认删除？" ok-text="确认" cancel-text="取消" @confirm="handleRemove(record)">
            <span class="primary-btn">删除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <edit-role ref="editRoleRef" @refresh="refreshData"></edit-role>
    <permission-config ref="permissionRef"></permission-config>
    <user-list ref="userListRef"></user-list>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message } from 'ant-design-vue'
import { getRoleList, deleteRole } from './apis'
import EditRole from './components/EditRole.vue'
import PermissionConfig from './components/PermissionConfig.vue'
import UserList from './components/UserList.vue'

const params = reactive({
  column: 'id',
  order: 'desc',
  roleName: '',
  roleCode: ''
})

const columns = [
  { title: '角色名称', dataIndex: 'roleName', width: 200, fixed: 'left' },
  { title: '角色编码', dataIndex: 'roleCode' },
  { title: '描述', dataIndex: 'remark' },
  { title: '创建时间', dataIndex: 'createTime', width: 200 },
  { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getRoleList)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const editRoleRef = ref()
const handleAdd = () => {
  editRoleRef.value.open()
}
const handleEdit = (row) => {
  editRoleRef.value.open({
    id: row.id,
    roleName: row.roleName,
    roleCode: row.roleCode,
    remark: row.remark
  })
}

const userListRef = ref()
const handleViewUser = (data) => {
  userListRef.value.open({
    roleId: data.id,
    roleName: data.roleName
  })
}

const refreshData = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const handleRemove = async (data) => {
  await deleteRole({ id: data.id })
  message.success('删除成功')
  let pageNo = pagination.value.current
  if (pagination.value.pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

const permissionRef = ref()
const handleAuth = (data) => {
  permissionRef.value.open(data)
}

onMounted(() => {
  onTableChange()
})
</script>
