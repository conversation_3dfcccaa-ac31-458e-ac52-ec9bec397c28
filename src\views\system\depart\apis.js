import request from '@/apis/http'

// 懒加载获取部门树
export const getDepartTreeLazy = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/queryDepartTreeSync',
    params
  })
}

export const getDepartTree = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/queryTreeList',
    params
  })
}

export const queryDepartTreeByCompanyId = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/queryDepartTreeByCompanyId',
    params
  })
}

export const searchDepart = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/searchBy',
    params
  })
}

export const addDept = (data) => {
  return request({
    method: 'post',
    url: '/sys/sysDepart/add',
    data
  })
}

export const editDept = (data) => {
  return request({
    method: 'post',
    url: '/sys/sysDepart/edit',
    data
  })
}

export const deleteDept = (params) => {
  return request({
    method: 'delete',
    url: '/sys/sysDepart/deleteBatch',
    params
  })
}

export const queryDepartPermission = (params) => {
  return request({
    method: 'get',
    url: '/sys/permission/queryDepartPermission',
    params
  })
}

export const saveDepartPermission = (data) => {
  return request({
    method: 'post',
    url: '/sys/permission/saveDepartPermission',
    data
  })
}

// 获取公司树
export const getCompanyTree = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/queryCompanyTreeList',
    params
  })
}

// 获取当前用户所属公司列表
export const getCurrentUserCompanies = (params) => {
  return request({
    method: 'get',
    url: '/sys/user/getCurrentUserCompanies',
    params
  })
}
