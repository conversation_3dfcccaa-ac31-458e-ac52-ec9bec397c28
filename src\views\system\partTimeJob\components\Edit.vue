<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '70px' } }"
        label-align="left"
        autocomplete="off"
        :disabled="viewMode"
      >
        <a-form-item label="用户" name="user">
          <a-form-item-rest>
            <f7-select v-model="form.user" f7-type="user"></f7-select>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="兼职公司" name="partTimeCompany">
          <company-select v-model="form.partTimeCompany" type="all" @change="onCompanyChange"></company-select>
        </a-form-item>
        <a-form-item label="兼职部门" name="partTimeDepart">
          <depart-select
            v-model="form.partTimeDepart"
            :company-id="form.partTimeCompany"
            :disabled="!form.partTimeCompany"
            @change="onDepartChange"
          ></depart-select>
        </a-form-item>
        <a-form-item label="兼职职位" name="partTimePost">
          <common-select
            v-model="form.partTimePost"
            :field-names="{ label: 'name', value: 'id' }"
            :options="postList"
            :disabled="!(form.partTimeCompany && form.partTimeDepart)"
          ></common-select>
        </a-form-item>
        <a-form-item label="是否结束" name="isFinish">
          <a-radio-group v-model:value="form.isFinish">
            <a-radio :value="true">是</a-radio>
            <a-radio :value="false">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="form.remark"
            show-count
            :maxlength="200"
            :auto-size="{ minRows: 3, maxRows: 3 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleConfirm">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { edit, add } from '../apis.js'
import { message } from 'ant-design-vue'
import { page as getPositionList } from '@/views/system/position/apis'

const emit = defineEmits(['refresh'])

const onCompanyChange = () => {
  form.partTimeDepart = ''
  form.partTimePost = ''
}

const onDepartChange = () => {
  form.partTimePost = ''
}

const visible = ref(false)
const viewMode = ref(false) // 是否查看模式
const modalTitle = computed(() => {
  if (viewMode.value) return '查看兼职管理'
  return form.id ? '编辑兼职管理' : '新增兼职管理'
})

const open = (data, isView) => {
  if (data) {
    Object.assign(form, data)
  }
  viewMode.value = Boolean(isView)
  visible.value = true
}

const postList = ref([])
const loadPostList = async () => {
  const { result } = await getPositionList({
    pageNo: 1,
    pageSize: 1000,
    company: form.partTimeCompany,
    depart: form.partTimeDepart
  })
  postList.value = result.records
}

const form = reactive({
  id: '',
  user: '',
  partTimeCompany: '',
  partTimeDepart: '',
  partTimePost: '',
  isFinish: false,
  remark: ''
})

const rules = {
  user: [{ required: true, message: '请选择用户', trigger: 'change' }],
  partTimeCompany: [{ required: true, message: '请选择兼职公司', trigger: 'change' }],
  partTimeDepart: [{ required: true, message: '请选择兼职部门', trigger: 'change' }],
  partTimePost: [{ required: true, message: '请选择兼职职位', trigger: 'change' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.user = ''
  form.partTimeCompany = ''
  form.partTimeDepart = ''
  form.partTimePost = ''
  form.isFinish = false
  form.remark = ''
  formRef.value.clearValidate()
  visible.value = false
}

watch([() => form.partTimeCompany, () => form.partTimeDepart], ([company, depart]) => {
  if (company && depart) {
    loadPostList()
  }
})

defineExpose({ open })
</script>
