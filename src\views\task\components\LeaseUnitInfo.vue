<template>
  <div>
    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">租赁单元基础信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">单元名称：{{ leaseUnitInfo.baseInfo.unitName }}</span>
      <span class="w-[50%]">房屋产权：{{ leaseUnitInfo.baseInfo.houseProperty }}</span>
      <span class="w-[50%]">关联项目楼栋：{{ leaseUnitInfo.baseInfo.projectBuilding }}</span>
      <span class="w-[50%]">资产类型：{{ leaseUnitInfo.baseInfo.assetType }}</span>
      <span class="w-[100%]">地址：{{ leaseUnitInfo.baseInfo.address }}</span>
      <span class="w-[50%]">权属公司：{{ leaseUnitInfo.baseInfo.ownershipCompany }}</span>
      <span class="w-[50%]">归集公司：{{ leaseUnitInfo.baseInfo.gatheringCompany }}</span>
      <span class="w-[50%]">业务状态：{{ leaseUnitInfo.baseInfo.businessStatus }}</span>
      <span class="w-[50%]">管理公司：{{ leaseUnitInfo.baseInfo.managementCompany }}</span>
      <span class="w-[50%]">租赁单元类别：{{ leaseUnitInfo.baseInfo.unitCategory }}</span>
      <span class="w-[50%]">来源租赁单元：{{ leaseUnitInfo.baseInfo.sourceUnit }}</span>
      <span class="w-[50%]">备注：{{ leaseUnitInfo.baseInfo.remark }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">租赁信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">使用类型：{{ leaseUnitInfo.leaseInfo.usageType }}</span>
      <span class="w-[50%]">租赁用途：{{ leaseUnitInfo.leaseInfo.leaseUsage }}</span>
      <span class="w-[50%]">租赁面积(m²)：{{ leaseUnitInfo.leaseInfo.leaseArea }}</span>
      <span class="w-[50%]">控制方式：{{ leaseUnitInfo.leaseInfo.controlType }}</span>
      <span class="w-[50%]">生效日期：{{ leaseUnitInfo.leaseInfo.effectiveDate }}</span>
      <span class="w-[50%]">到期日期：{{ leaseUnitInfo.leaseInfo.expiryDate }}</span>
      <span class="w-[50%]">片区管理员：{{ leaseUnitInfo.leaseInfo.areaManager }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">产权信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">产权证号：{{ leaseUnitInfo.propertyInfo.certificateNo }}</span>
      <span class="w-[50%]">权证获得日期：{{ leaseUnitInfo.propertyInfo.certificateDate }}</span>
      <span class="w-[50%]">取得来源：{{ leaseUnitInfo.propertyInfo.source }}</span>
      <span class="w-[50%]">产权用途：{{ leaseUnitInfo.propertyInfo.usage }}</span>
      <span class="w-[50%]">房地权证合一：{{ leaseUnitInfo.propertyInfo.isUnified }}</span>
      <span class="w-[50%]">产权情况：{{ leaseUnitInfo.propertyInfo.status }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">土地信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">用地性质：{{ leaseUnitInfo.landInfo.landNature }}</span>
      <span class="w-[50%]">土地建设情况：{{ leaseUnitInfo.landInfo.constructionStatus }}</span>
      <span class="w-[50%]">土地批复使用开始日期：{{ leaseUnitInfo.landInfo.approvalStartDate }}</span>
      <span class="w-[50%]">土地批复使用结束日期：{{ leaseUnitInfo.landInfo.approvalEndDate }}</span>
      <span class="w-[50%]">土地取得价格：{{ leaseUnitInfo.landInfo.obtainPrice }}</span>
      <span class="w-[50%]">地价款(租金)欠缴金额：{{ leaseUnitInfo.landInfo.arrears }}</span>
      <span class="w-[50%]">租赁土地租金：{{ leaseUnitInfo.landInfo.leaseRent }}</span>
    </div>

    <h4 class="text-[16px] font-bold mb-[12px] mt-[40px] text-[#1d335c]">建筑信息</h4>
    <div class="flex flex-wrap gap-y-[12px] text-secondary text-[14px]">
      <span class="w-[50%]">房产类型：{{ leaseUnitInfo.buildingInfo.propertyType }}</span>
      <span class="w-[50%]">建筑面积(m²)：{{ leaseUnitInfo.buildingInfo.buildingArea }}</span>
      <span class="w-[50%]">宗地面积(m²)：{{ leaseUnitInfo.buildingInfo.landArea }}</span>
      <span class="w-[50%]">建筑结构：{{ leaseUnitInfo.buildingInfo.structure }}</span>
      <span class="w-[50%]">建成年份：{{ leaseUnitInfo.buildingInfo.yearBuilt }}</span>
      <span class="w-[50%]">层数/总层数：{{ leaseUnitInfo.buildingInfo.floorInfo }}</span>
      <span class="w-[50%]">层高(m)：{{ leaseUnitInfo.buildingInfo.floorHeight }}</span>
      <span class="w-[50%]">户型：{{ leaseUnitInfo.buildingInfo.houseType }}</span>
      <span class="w-[50%]">消防等级：{{ leaseUnitInfo.buildingInfo.fireLevel }}</span>
      <span class="w-[50%]">房屋安全等级：{{ leaseUnitInfo.buildingInfo.safetyLevel }}</span>
    </div>
  </div>
</template>

<script setup>
const { leaseUnitInfo } = defineProps({
  leaseUnitInfo: { type: Object, required: true }
})
</script>
