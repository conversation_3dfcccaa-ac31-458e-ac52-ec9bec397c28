<template>
  <a-drawer
    v-model:open="visible"
    class="building-detail-drawer common-detail-drawer"
    title="楼栋详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleEditBuilding">编辑</span>
        <span class="primary-btn" @click="handleBuildingStatus">
          {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
        </span>
        <span class="primary-btn" @click="handleDeleteBuilding">删除</span>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <div>
        <h2 class="text-[16px] font-bold mb-[12px]">楼栋基础信息</h2>
        <a-row :gutter="20" class="text-secondary">
          <a-col :span="12">楼栋名称: {{ detail.name }}</a-col>
          <a-col :span="12">所属项目: {{ detail.wyProject_dictText }}</a-col>
        </a-row>
        <a-row :gutter="20" class="text-secondary my-[12px]">
          <a-col :span="12">项目所属公司: {{ detail.ctrlUnit_dictText }}</a-col>
          <a-col :span="12">
            <span class="mr-[6px]">状态:</span>
            <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status" type="dot"></status-tag>
          </a-col>
        </a-row>
        <div class="text-secondary">备注: {{ detail.remark }}</div>
      </div>
      <h2 class="text-[16px] font-bold mt-[40px] mb-[12px]">楼层信息</h2>
      <a-table :data-source="floorList" :columns="columns" :pagination="false">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-if="column.dataIndex === 'leaseUnitCount'">
            <span v-if="record.leaseUnitCount === 0">0</span>
            <span class="primary-btn" v-else @click="handleViewLeaseUnit(record)">
              查看({{ record.leaseUnitCount }})
            </span>
          </template>
          <template v-if="column.dataIndex === 'waterShareFormulas'">
            <span
              class="primary-btn"
              v-if="record.waterShareFormulas && record.waterShareFormulas.length"
              @click="handleViewFloorInfo(record)"
            >
              查看({{ record.waterShareFormulas.length }})
            </span>
            <span v-else>-</span>
          </template>
        </template>
      </a-table>
      <div class="flex items-center justify-between mt-[40px] mb-[12px]">
        <h2 class="text-[16px] font-bold">楼栋水电分摊信息</h2>
        <span v-if="detail.everyMonthAutoBillDay">自动出账时间: 每月{{ detail.everyMonthAutoBillDay }}日</span>
      </div>
      <a-table
        :data-source="waterElectricityList"
        :columns="waterElectricityColumns"
        :pagination="false"
        :scroll="{ y: '50vh', x: 2100 }"
      ></a-table>
    </a-spin>
  </a-drawer>
  <a-modal
    v-model:open="infoVisible"
    :title="`${floorData.name} - 楼层水电分摊信息`"
    width="1040px"
    wrap-class-name="common-modal"
    :footer="false"
    destroy-on-close
  >
    <a-table
      :data-source="floorData.waterShareFormulas"
      :columns="waterElectricityColumns"
      :pagination="false"
      :scroll="{ y: '50vh', x: 2100 }"
      style="margin-bottom: 12px"
    ></a-table>
  </a-modal>
  <watch-lease-unit ref="watchLeaseUnitRef"></watch-lease-unit>
</template>

<script setup>
import { buildingDetail, queryFloor, deleteBuilding, updateStatus, getWater } from '../apis/building.js'
import { message, Modal } from 'ant-design-vue'
import { renderMoney } from '@/utils/render'
import WatchLeaseUnit from './WatchLeaseUnit.vue'

const emit = defineEmits(['editBuilding', 'refresh'])

const visible = ref(false)
const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const loading = ref(false)
const detail = reactive({
  id: '',
  name: '',
  company_dictText: '',
  number: '',
  remark: '',
  createTime: '',
  createBy_dictText: '',
  status: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await buildingDetail({ id })
  Object.assign(detail, result)
  await Promise.all([loadFloorList(), loadWaterElectricityList(id)])
  loading.value = false
}

const handleEditBuilding = () => {
  visible.value = false
  emit('editBuilding', detail)
}

const handleBuildingStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该楼栋？`,
    content: detail.status === 'ENABLE' ? '楼栋禁用后将无法再被使用，但不影响已创建的数据。' : '',
    centered: true,
    onOk: async () => {
      const data = await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(data.message)
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDeleteBuilding = () => {
  Modal.confirm({
    title: '确认删除该楼栋？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await deleteBuilding({ ids: detail.id })
      message.success(data.message)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const columns = [
  { title: '#', dataIndex: 'index', width: 60 },
  { title: '楼层名称', dataIndex: 'name' },
  { title: '租赁单元', dataIndex: 'leaseUnitCount' },
  { title: '水电分摊信息', dataIndex: 'waterShareFormulas' }
]
const floorList = ref([])
const loadFloorList = async () => {
  const { result } = await queryFloor({ id: detail.id })
  floorList.value = result
}

const watchLeaseUnitRef = ref()
const handleViewLeaseUnit = (data) => {
  watchLeaseUnitRef.value.open({
    id: data.id,
    name: data.name,
    projectName: detail.wyProject_dictText,
    buildingName: detail.name
  })
}

const waterElectricityList = ref([])
// 获取水电分摊信息
const loadWaterElectricityList = async (id) => {
  const { result } = await getWater({
    relation: id,
    relationType: 'WyBuilding',
    pageNo: 1,
    pageSize: 1000
  })
  waterElectricityList.value = result.records
}
const waterElectricityColumns = [
  { title: '分摊类别', dataIndex: 'shareType_dictText', width: 200, fixed: 'left' },
  { title: '编码(表号)', dataIndex: 'waterEleTableNum_dictText', width: 240 },
  { title: '类型', dataIndex: 'type_dictText' },
  { title: '属性', dataIndex: 'property_dictText' },
  { title: '倍率', dataIndex: 'doubleRate' },
  { title: '单价', dataIndex: 'price', customRender: ({ text }) => renderMoney(text, 6) },
  { title: '单位分摊计算公式', dataIndex: 'unitShare_dictText' },
  { title: '公摊金额计算公式', dataIndex: 'shareAmount_dictText' },
  { title: '减免金额计算公式', dataIndex: 'remission_dictText' },
  { title: '自用金额计算公式', dataIndex: 'selfAmount_dictText' },
  { title: '合计计算公式', dataIndex: 'totalAmount_dictText' },
  { title: '税金计算公式', dataIndex: 'taxAmount_dictText' },
  { title: '含税合计计算公式', dataIndex: 'containTaxTotalAmount_dictText' }
]

const infoVisible = ref(false)
const floorData = reactive({})
// 查看楼层水电分摊信息
const handleViewFloorInfo = (data) => {
  Object.assign(floorData, data)
  infoVisible.value = true
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.building-detail-drawer {
  .building-item {
    border-radius: 8px;
    border: 1px solid #e6e9f0;
    background-color: #f7f8fa;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    transition:
      border-color 0.2s,
      background-color 0.2s;
    cursor: pointer;
    &:hover {
      border-color: var(--color-primary);
    }
    &.active {
      border-color: var(--color-primary);
      background-color: #eaf0fe;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .floor-section {
    flex: 1;
    border: 1px solid #e6e9f0;
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
