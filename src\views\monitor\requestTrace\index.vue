<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex items-center">
        <a-button @click="loadData">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <span class="ml-[40px]">共追踪到{{ list.length }}条近期HTTP请求记录</span>
      </div>
      <a-radio-group v-model:value="type" @change="loadData">
        <a-radio-button value="all">全部</a-radio-button>
        <a-radio-button value="success">成功</a-radio-button>
        <a-radio-button value="error">错误</a-radio-button>
      </a-radio-group>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ y: 'calc(100vh - 294px)' }"
      @change="loadData"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'method'">
          <status-tag :color="methodColor[record.request.method] || '#1D64F0'">{{ record.request.method }}</status-tag>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <status-tag :color="resColor[record.response.status] || '#1D64F0'">
            {{ record.response.status }}
          </status-tag>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { page } from './apis'
import dayjs from 'dayjs'

const columns = [
  {
    title: '请求时间',
    dataIndex: 'timestamp',
    width: 160,
    customRender: ({ text }) => dayjs(new Date(text)).format('YYYY-MM-DD HH:mm')
  },
  { title: '请求方法', dataIndex: 'method', width: 100 },
  { title: '请求URL', dataIndex: 'uri', customRender: ({ record }) => record.request.uri },
  { title: '响应状态', dataIndex: 'status', width: 100 },
  { title: '请求耗时', dataIndex: 'timeTaken', width: 100, customRender: ({ text }) => (text ? `${text}ms` : '') }
]

const type = ref('all') // all=全部 success=成功 error=错误

const tableLoading = ref(false)
const list = ref([])
const loadData = async () => {
  tableLoading.value = true
  const { traces } = await page(type.value)
  list.value = traces
  tableLoading.value = false
}

const methodColor = {
  GET: '#87d068',
  POST: '#1D64F0',
  DELETE: '#F03A1D'
}
const resColor = {
  200: '#6EC21B',
  400: '#FAB700',
  401: '#D7DAE0',
  500: '#F03A1D'
}

onMounted(() => {
  loadData()
})
</script>
