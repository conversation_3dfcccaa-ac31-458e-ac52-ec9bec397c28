import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterShareFormulaEntity/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterShareFormulaEntity/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/waterShareFormulaEntity/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterShareFormulaEntity/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterShareFormulaEntity/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterShareFormulaEntity/edit',
    data
  })
}
export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/waterShareFormulaEntity/deleteBatch',
    params
  })
}
