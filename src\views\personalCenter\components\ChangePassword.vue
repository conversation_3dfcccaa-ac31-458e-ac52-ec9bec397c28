<template>
  <a-modal
    v-model:open="visible"
    title="修改密码"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :label-col="{ style: { width: '120px' } }" :model="ruleForm" :rules="rules">
      <a-form-item label="输入原密码" name="oldpassword">
        <a-input-password
          autocomplete="current-password"
          v-model:value="ruleForm.oldpassword"
          placeholder="请输入原密码"
          :maxlength="20"
        ></a-input-password>
      </a-form-item>
      <a-form-item label="设置新密码" name="password">
        <a-input-password
          autocomplete="new-password"
          v-model:value="ruleForm.password"
          placeholder="请输入新密码"
          :maxlength="20"
          style="width: 100%"
        ></a-input-password>
      </a-form-item>
      <a-form-item label="再次输入新密码" name="confirmpassword">
        <a-input-password
          autocomplete="new-password"
          v-model:value="ruleForm.confirmpassword"
          placeholder="请再次输入新密码"
          :maxlength="20"
          style="width: 100%"
        ></a-input-password>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { updatePassword } from '@/apis/user'
import { validatePassword } from '@/utils/validate'
import { useUserStore } from '@/store/modules/user'
const visible = ref(false)
/**
 * 打开弹窗
 * @param extraParams formData所需的额外参数
 * 之所以放在open函数里，而不放在props里，是为了某些情况下，可以减少父组件的代码量，降低组件之间的耦合度
 */
const open = () => {
  visible.value = true
}
defineExpose({ open })

const store = useUserStore()
const userInfo = computed(() => store.userInfo)
const ruleForm = ref({
  oldpassword: '',
  password: '',
  confirmpassword: ''
})
const rules = computed(() => ({
  oldpassword: [{ required: true, message: '请输入原密码', trigger: ['blur'] }],
  password: [{ required: true, validator: validatePassword('新密码'), trigger: ['blur'] }],
  confirmpassword: [
    { required: true, validator: validatePassword('确认密码', ruleForm.value.password), trigger: ['blur'] }
  ]
}))
const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    await updatePassword({ ...ruleForm.value, username: userInfo.value.username })
    handleCancel()
    message.success('密码修改成功，请重新登录')
    store.logout()
  } catch (error) {
    message.error(error.message || '密码修改失败，请重试')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  visible.value = false
  formRef.value?.resetFields()
}
</script>
