<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="`数据权限规则[${params.menuName}]`"
    placement="right"
    width="1072px"
    @close="handleClose"
    :mask-closable="false"
  >
    <div class="flex items-center mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新增
      </a-button>
      <a-button @click="loadData">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <s-input
        v-model="params.ruleName"
        placeholder="搜索规则名称"
        class="ml-[40px] !w-[240px]"
        @input="handleInput"
      ></s-input>
      <s-input
        v-model="params.ruleValue"
        placeholder="搜索规则值"
        class="ml-[16px] !w-[240px]"
        @input="handleInput"
      ></s-input>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ y: 'calc(100vh - 226px)' }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-popconfirm title="是否确认删除？" ok-text="确认" cancel-text="取消" @confirm="handleRemove(record)">
            <span class="primary-btn">删除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <edit-data-rule ref="editDataRuleRef" @refresh="loadData"></edit-data-rule>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { queryPermissionRule, deletePermissionRule } from '../apis.js'
import EditDataRule from './EditDataRule.vue'

const visible = ref(false)

const open = (data) => {
  params.menuName = data.menuName
  params.permissionId = data.menuId
  visible.value = true
  loadData()
}

const params = reactive({
  menuName: '', // 前端自定义字段，用于展示
  permissionId: '',
  ruleName: '',
  ruleValue: ''
})

const columns = [
  { title: '序号', dataIndex: 'index', width: 80, fixed: 'left' },
  { title: '规则名称', dataIndex: 'ruleName' },
  { title: '规则字段', dataIndex: 'ruleColumn' },
  { title: '规则值', dataIndex: 'ruleValue' },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]

const list = ref([])
const tableLoading = ref(false)
const loadData = async () => {
  tableLoading.value = true
  const { result } = await queryPermissionRule(params)
  list.value = result
  tableLoading.value = false
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    loadData()
  }, 600)
}

const editDataRuleRef = ref()
const handleAdd = () => {
  editDataRuleRef.value.open()
}
const handleEdit = (data) => {
  editDataRuleRef.value.open({
    id: data.id,
    permissionId: data.permissionId,
    ruleColumn: data.ruleColumn,
    ruleConditions: data.ruleConditions,
    ruleName: data.ruleName,
    ruleValue: data.ruleValue,
    status: data.status
  })
}
const handleRemove = async (data) => {
  await deletePermissionRule({ id: data.id })
  message.success('删除成功')
  loadData()
}

const handleClose = () => {
  params.ruleName = ''
  params.ruleValue = ''
  list.value = []
}

defineExpose({ open })
</script>
