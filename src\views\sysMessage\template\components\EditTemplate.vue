<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}消息模板`"
    class="edit-template-drawer common-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h2 class="text-[16px] font-bold my-[24px]">基础信息</h2>
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="模版标题" name="templateName">
          <a-input v-model:value="formData.templateName" placeholder="请输入模板标题" />
        </a-form-item>
        <a-form-item label="模版编码" name="templateCode">
          <a-input v-model:value="formData.templateCode" placeholder="请输入模板编码" />
        </a-form-item>
        <a-form-item label="模板类型" name="templateType">
          <dict-select v-model="formData.templateType" placeholder="请选择模板类型" code="msgType"></dict-select>
        </a-form-item>
        <a-form-item label="公众号消息模板ID" name="mpTemplateId" class="form-item-full">
          <a-input v-model:value="formData.mpTemplateId" placeholder="请输入公众号消息模板ID" />
        </a-form-item>
        <a-form-item v-if="formData.mpTemplateId" label="微信小程序路径" name="maPath" class="form-item-full">
          <a-input v-model:value="formData.maPath" placeholder="请输入微信小程序路径" />
        </a-form-item>
        <a-form-item v-if="formData.mpTemplateId" label="参数映射" name="mpTemplateMap" class="form-item-full">
          <div class="parameter-mapping">
            <div
              v-for="(param, index) in formData.mpTemplateMap"
              :key="index"
              class="parameter-row flex gap-4 mb-2 items-center"
            >
              <a-input v-model:value="param.key" placeholder="参数名称" class="flex-1" />
              <a-input v-model:value="param.value" placeholder="参数值" class="flex-1" />
              <i class="a-icon-remove cursor-pointer mr-2 text-red-500" @click="removeParameter(index)"></i>
            </div>
            <a-button type="dashed" class="w-full mt-2" @click="addParameter">
              <i class="a-icon-plus mr-2"></i>
              新增
            </a-button>
          </div>
        </a-form-item>
        <a-form-item label="应用状态" name="useStatus" class="form-item-full">
          <a-switch v-model:checked="formData.useStatus" checked-value="1" un-checked-value="0" />
        </a-form-item>
        <h2 class="text-[16px] font-bold my-[24px]">模版内容</h2>
        <a-form-item name="templateContent" class="form-item-full">
          <a-textarea
            v-if="formData.templateType === '1'"
            v-model:value="formData.templateContent"
            placeholder="请输入模板内容"
            :rows="10"
            :maxlength="50000"
            show-count
          />
          <t-editor
            v-else-if="formData.templateType === '2'"
            v-model="formData.templateContent"
            :height="400"
            :maximum="50000"
          />
        </a-form-item>
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">保存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addTemplate, updateTemplate } from '../apis'
import TEditor from '@/components/Tinymce/TEditor.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  id: undefined,
  templateName: undefined,
  templateCode: undefined,
  templateContent: '',
  templateType: '1',
  mpTemplateId: undefined,
  maPath: undefined,
  mpTemplateMap: undefined,
  useStatus: '1'
}

const formData = reactive({ ...formDataDefault })

const rules = computed(() => ({
  templateName: [{ required: true, message: '请输入模板标题', trigger: 'blur' }],
  templateCode: [{ required: true, message: '请输入模板编码', trigger: 'blur' }],
  templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  maPath: formData.mpTemplateId ? [{ required: true, message: '请输入微信小程序路径', trigger: 'blur' }] : [],
  mpTemplateMap: formData.mpTemplateId ? [{ required: true, message: '请配置参数映射', trigger: 'blur' }] : [],
  templateContent: [{ required: true, message: '请输入模板内容', trigger: 'blur' }]
}))

/**
 * 打开抽屉
 */
const open = async (data) => {
  visible.value = true
  confirmLoading.value = true

  // 清空表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }

  // 重置表单数据
  Object.assign(formData, formDataDefault)

  if (data && data.id) {
    // 编辑模式：设置表单数据
    Object.assign(formData, data)

    // 处理参数映射数据
    if (typeof formData.mpTemplateMap === 'string') {
      try {
        formData.mpTemplateMap = JSON.parse(formData.mpTemplateMap)
      } catch {
        formData.mpTemplateMap = undefined
      }
    } else if (!formData.mpTemplateMap) {
      formData.mpTemplateMap = undefined
    }
  } else {
    formData.mpTemplateMap = undefined
  }

  await nextTick()
  confirmLoading.value = false
}

/**
 * 添加参数映射
 */
const addParameter = () => {
  if (!formData.mpTemplateMap) {
    formData.mpTemplateMap = []
  }
  formData.mpTemplateMap.push({ key: '', value: '' })
}

/**
 * 移除参数映射
 */
const removeParameter = (index) => {
  if (formData.mpTemplateMap && formData.mpTemplateMap.length > 1) {
    formData.mpTemplateMap.splice(index, 1)
  }
}

/**
 * 取消编辑
 */
const handleCancel = () => {
  emits('refresh')
  Object.assign(formData, formDataDefault)
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  visible.value = false
}

/**
 * 保存数据
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true

  const api = formData.id ? updateTemplate : addTemplate

  try {
    // 准备要发送的数据，将 mpTemplateMap 转为字符串
    const submitData = { ...formData }
    if (submitData.mpTemplateMap && Array.isArray(submitData.mpTemplateMap)) {
      submitData.mpTemplateMap = JSON.stringify(submitData.mpTemplateMap)
    }

    await api(submitData)

    const action = formData.id ? '编辑' : '新建'
    message.success(`消息模版${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

// 监听公众号消息模板ID变化
watch(
  () => formData.mpTemplateId,
  (newVal) => {
    if (newVal && (!formData.mpTemplateMap || formData.mpTemplateMap.length === 0)) {
      formData.mpTemplateMap = [{ key: '', value: '' }]
    } else if (!newVal) {
      formData.mpTemplateMap = undefined
    }
  }
)

defineExpose({ open })
</script>

<style lang="less">
.edit-template-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }
}
</style>
