<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="退款详情"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          class="primary-btn"
          @click="handleEdit"
          v-if="['TEMP', 'BACK'].includes(detailData.status)"
          v-auth="'biz.funds:ct_fun_refund_req_bill:edit'"
        >
          编辑
        </span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="delete" v-if="['TEMP', 'BACK'].includes(detailData.status)">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
              <a-menu-item key="submit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)">
                <div class="primary-btn" @click="handleSubmit(detailData)">提交</div>
              </a-menu-item>
              <a-menu-item key="audit" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleAudit(detailData)">审核</div>
              </a-menu-item>
              <a-menu-item key="unAudit" v-if="detailData.status === 'AUDITOK'">
                <div class="primary-btn" @click="handleUnAudit(detailData)">反审批</div>
              </a-menu-item>
              <a-menu-item key="back" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleBack(detailData)">撤回</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">退款详情</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_RefundReqBill_Status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">业务日期：{{ detailData.bizDate || '-' }}</span>
        <span class="w-[50%]">退款客户：{{ detailData.customer_dictText || '-' }}</span>
        <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
        <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
        <span class="w-[50%]">审核人：{{ detailData.auditBy_dictText || '-' }}</span>
        <span class="w-[50%]">审核时间：{{ detailData.auditTime || '-' }}</span>
        <span class="w-[50%]">退款时间：{{ detailData.refundTime || '-' }}</span>
        <span class="w-[50%]">退款金额：{{ renderMoney(detailData.refundReqAmount, 2, '元') }}</span>
        <span class="w-[100%] break-words whitespace-pre-wrap">备注：{{ detailData.remark || '-' }}</span>
      </div>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">退款明细</h4>
      <a-table
        v-if="list?.length"
        :columns="refundColumns"
        :data-source="list"
        :pagination="false"
        :scroll="{ x: 1200, y: tableHeight }"
        size="middle"
      ></a-table>
      <div v-else class="flex flex-col items-center">
        <img src="@/assets/imgs/no-data.png" />
        <span class="text-tertiary">暂无数据</span>
      </div>
    </a-spin>
    <template #footer v-if="detailData.status === 'AUDITOK'">
      <a-button type="primary" :loading="finishLoading" @click="showFinishModal">退款完成</a-button>
    </template>
  </a-drawer>
  <edit-refund ref="editDrawerRef" @refresh="refreshData" />

  <!-- 退款完成确认对话框 -->
  <a-modal
    v-model:open="finishModalVisible"
    title="确认退款完成"
    class="common-modal"
    :confirm-loading="finishLoading"
    @ok="handleFinish"
    @cancel="handleFinishCancel"
    :mask-closable="false"
  >
    <a-form ref="finishFormRef" :model="finishForm" :rules="finishFormRules">
      <a-form-item label="实际退款日期" name="refundDate" required>
        <a-date-picker v-model:value="finishForm.refundDate" placeholder="请选择实际退款日期" style="width: 100%" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import dayjs from 'dayjs'
import { message, Modal } from 'ant-design-vue'
import { renderMoney } from '@/utils/render'
import usePageTable from '@/hooks/usePageTable'
import { hasPermission } from '@/utils/permission'
import {
  submitRefundReqBill,
  auditRefundReqBill,
  unAuditRefundReqBill,
  backRefundReqBill,
  deleteRefundReqBill,
  getRefundReqBillById,
  queryRefundReqBillEntries,
  finishRefundReqBill
} from '../apis'
import EditRefund from './EditRefund.vue'

const emits = defineEmits(['refresh', 'edit'])

const { list, onTableList, tableHeight } = usePageTable(queryRefundReqBillEntries)

const visible = ref(false)
const loading = ref(false)
const finishLoading = ref(false)
const finishModalVisible = ref(false)
const finishFormRef = ref()
const editDrawerRef = ref()
const detailData = ref({})

const finishForm = reactive({
  id: undefined,
  refundDate: undefined
})

const finishFormRules = {
  refundDate: [{ required: true, message: '请选择实际退款日期', trigger: 'change' }]
}

const refundColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract_dictText', width: 160, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '减免金额', dataIndex: 'remission', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '实际应收',
    dataIndex: 'actualReceiveAmount',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已收金额', dataIndex: 'paid', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  { title: '未收金额', dataIndex: 'residual', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已转款抵扣',
    dataIndex: 'transferDeduction',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '已退金额', dataIndex: 'refunded', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元') },
  {
    title: '已处理尾差',
    dataIndex: 'offDifference',
    width: 120,
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '剩余可转',
    dataIndex: 'residueRefundAmount',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  {
    title: '本次转款金额',
    dataIndex: 'thisRefundAmt',
    width: 120,
    fixed: 'right',
    customRender: ({ text }) => renderMoney(text, 2, '元')
  },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true, fixed: 'right' }
]

/**
 * 打开退款详情抽屉
 * @param {Object} record - 退款记录对象
 */
const open = async (record) => {
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 关闭抽屉并重置数据
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
  emits('refresh')
}

/**
 * 编辑当前退款记录
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 删除当前退款记录
 */
const handleDelete = async () => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:delete')) return
  try {
    await deleteRefundReqBill({ id: detailData.value.id })
    message.success('删除成功')
    handleClose()
    emits('refresh')
  } catch {
    message.error('删除失败，请重试')
  }
}

/**
 * 加载退款详情数据
 * @param {string} id - 退款记录ID
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const { result } = await getRefundReqBillById({ id })
    detailData.value = result
    onTableList({ id })
  } finally {
    loading.value = false
  }
}

/**
 * 刷新详情数据
 */
const refreshData = () => {
  loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 提交退款申请记录
 * @param {Object} record - 要提交的记录对象
 */
const handleSubmit = async (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:submit')) return
  const detailList = await queryRefundReqBillEntries({ id: record.id })
  record.refundReqBillEntryList = detailList.result
  Modal.confirm({
    title: '确认提交',
    content: '确认提交该退款申请记录？',
    async onOk() {
      await submitRefundReqBill({ ...record })
      message.success('提交成功')
      handleClose()
    }
  })
}

/**
 * 审核退款申请记录
 * @param {Object} record - 要审核的记录对象
 */
const handleAudit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: '确认审核该退款申请记录？',
    async onOk() {
      await auditRefundReqBill({ id: record.id })
      message.success('审核成功')
      handleClose()
    }
  })
}

/**
 * 反审核退款申请记录
 * @param {Object} record - 要反审核的记录对象
 */
const handleUnAudit = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: '确认反审核该退款申请记录？',
    async onOk() {
      await unAuditRefundReqBill({ id: record.id })
      message.success('反审核成功')
      handleClose()
    }
  })
}

/**
 * 撤回退款申请记录
 * @param {Object} record - 要撤回的记录对象
 */
const handleBack = (record) => {
  if (!hasPermission('biz.funds:ct_fun_refund_req_bill:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: '确认撤回该退款申请记录？',
    async onOk() {
      await backRefundReqBill({ id: record.id })
      message.success('撤回成功')
      handleClose()
    }
  })
}
/**
 * 显示退款完成确认对话框
 */
const showFinishModal = () => {
  finishForm.refundDate = dayjs()
  finishModalVisible.value = true
}

/**
 * 取消退款完成操作
 */
const handleFinishCancel = () => {
  finishModalVisible.value = false
  finishFormRef.value?.resetFields()
}

/**
 * 完成退款操作
 */
const handleFinish = async () => {
  if (finishLoading.value) return

  await finishFormRef.value?.validate()

  finishLoading.value = true
  try {
    finishForm.id = detailData.value.id
    await finishRefundReqBill(finishForm)
    message.success('退款已完成')
    finishModalVisible.value = false
    handleClose()
  } finally {
    finishLoading.value = false
  }
}

defineExpose({
  open
})
</script>
