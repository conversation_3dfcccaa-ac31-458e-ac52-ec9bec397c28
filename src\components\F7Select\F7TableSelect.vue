<!--
如果f7-select需要被循环创建很多次，那么同时f7-select里面的f7-modal也会被创建很多次
这会造成较大的性能消耗，如果次数足够多，还会造成页面卡顿，这时候，应该用f7-table-select
f7-table-select除了必须传入mittId和f7-type以外，其余用法跟a-select没区别

因目前业务，所以f7-table-select暂时只处理单选，没处理多选
-->
<template>
  <a-select
    v-bind="$attrs"
    :options="options"
    :open="false"
    allow-clear
    :style="{ width }"
    v-model:value="innerValue"
    :field-names="fieldNames"
    @click="openF7Modal"
  ></a-select>
</template>

<script setup>
import mitt from '@/utils/mitt'
import config from './config'
import { message } from 'ant-design-vue'

const { modelValue, mittId, f7Type, fieldNames, relationDepart, departId } = defineProps({
  // 通信id，在使用该组件的时候，mittId值需要和f7-modal的mittId对应
  mittId: { required: true, type: String },
  modelValue: [String, Number],
  width: { type: String, default: '100%' },
  fieldNames: { type: Object, default: () => ({ label: 'label', value: 'value', options: 'options' }) },
  // departId: 部门id，只有f7Type=user时会用到，用于筛选在某个部门下的用户
  departId: { type: String, default: '' },
  // relationDepart: 是否关联业务部门，只有“f7Type=user && 只能选择业务部门之下的人员”才会用到，为true时，如果没有departId，则不允许打开弹窗
  relationDepart: { type: Boolean, default: false },
  f7Type: {
    required: true,
    type: String,
    validator: (value) => {
      return [
        'user', // 系统用户
        'contract', // 合同
        'customer', // 客户
        'waterElectricity', // 水电表
        'leaseUnit', // 租赁单元
        'paymentType', // 款项类型
        'receiptPayment', // 收付款记录
        'receiptPaymentDetail', // 收付款记录明细
        'receiveBill', // 应收单
        'receiveBillDetail', // 应收单明细
        'refund', // 退款明细
        'transfer', // 转款明细
        'debt' // 抵扣欠款明细
      ].includes(value)
    }
  }
})
const emit = defineEmits(['update:modelValue', 'change', 'click'])

const options = ref([])

const innerValue = ref(modelValue)

const loadDataById = async (id) => {
  const { result } = await config[f7Type].request({ id })
  options.value = result.records
}

const uid = Math.random().toString() // uid作为消息id，确保通信不会混乱

const openF7Modal = async () => {
  if (relationDepart && !departId) {
    message.warning('请先选择业务部门')
    return
  }
  // 有些时候，外层使用该组件，也需要定义click事件，做某些数据操作，操作以后才来打开f7-modal，所以需要先emit('click')
  emit('click')
  await nextTick()
  mitt.emit('openF7Modal', {
    mittId,
    uid,
    rows: options.value
  })
}

mitt.on('updateValue', (data) => {
  if (data.mittId !== mittId || data.uid !== uid) return
  innerValue.value = data.keys[0]
  options.value = data.rows.map((item) => ({
    ...item,
    label: item[fieldNames.label],
    value: item[fieldNames.value]
  }))
})

watch(
  () => modelValue,
  (val) => {
    innerValue.value = val
    if (val) {
      if (!options.value.length) {
        loadDataById(val)
      }
    } else {
      options.value = []
    }
  },
  { immediate: true }
)

watch(innerValue, (val) => {
  emit('update:modelValue', val)
  emit('change', val, options.value.length ? options.value[0] : {})
})
</script>
