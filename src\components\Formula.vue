<template>
  <div class="formula-generator">
    <div class="formula-generator__editor">
      <div class="formula-generator__editor-title">
        <div>公式编辑器</div>
        <div>
          <span class="primary-btn ml-[10px]" @click="showScript">查看脚本</span>
        </div>
      </div>
      <div class="formula-generator__editor-content">
        <FormulaCodemirror v-model="formulaZn" ref="codeMirrorRef" />
      </div>
    </div>

    <div class="formula-generator__tool">
      <div class="formula-generator__tool-entity">
        <div class="formula-generator__tool-title">业务实体</div>
        <div class="formula-generator__tool-content">
          <a-collapse accordion expand-icon-position="right">
            <a-collapse-panel
              v-for="info in classPath ? classPathInfos : classInfos"
              :key="info.key"
              :header="info.name"
            >
              <div
                class="field-item"
                v-for="(field, index) in info.fields"
                :key="index"
                @dblclick="insertField(info, field)"
              >
                <div class="flex-1">
                  <span class="block" :title="field.name">{{ field.name }}</span>
                  <small class="text-[12px] text-tertiary break-all">{{ field.field }}</small>
                </div>
                <div :class="`data_type__${fieldTypeMap[field.type]?.en} h-[24px]`">
                  {{ fieldTypeMap[field.type]?.cn }}
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
      <div class="formula-generator__tool-function">
        <div class="formula-generator__tool-title">常用函数</div>
        <div class="formula-generator__tool-content">
          <a-popover title="函数说明" v-for="(item, index) in functions" :key="index">
            <template v-slot:content>
              <p>{{ item.desc }}</p>
              <p v-for="(p, pi) in item.params" :key="pi">{{ p.text }}</p>
            </template>
            <div class="function-item" @dblclick="insertFunction(item)">
              <div class="function-item__value">{{ item.value }}</div>
              <div class="function-item__title">{{ item.title }}</div>
            </div>
          </a-popover>
        </div>
      </div>
      <div class="formula-generator__tool-variable">
        <div class="formula-generator__tool-title">
          <span>常用语法</span>
        </div>
        <div class="formula-generator__tool-content">
          <div class="keyboard-wrapper">
            <div class="keyboard">
              <div
                class="key-item"
                v-for="(key, index) in keyboards"
                :key="index"
                :style="`width: ${key.width}`"
                @click="insertKey(key)"
              >
                {{ key.text }}
              </div>
            </div>
          </div>

          <div class="keyboard-wrapper">
            <div class="keyboard">
              <div
                class="key-item"
                v-for="(key, index) in operators"
                :key="index"
                :style="`width: ${key.width}`"
                @click="insertKey(key)"
              >
                {{ key.text }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <formula-detail ref="formulaDetailRef" @mock="mock" />
  <formula-mock ref="formulaMockRef" />
</template>
<script setup>
import FormulaDetail from '@/views/billingManage/formula/components/FormulaDetail.vue'
import FormulaMock from '@/views/billingManage/formula/components/FormulaMock.vue'
import useFormula from '@/views/billingManage/formula/useFormual.js'
const formulaZn = ref('')
const emits = defineEmits(['update:modelValue'])
watch(
  () => modelValue,
  (newVal) => {
    formulaZn.value = newVal
  },
  { immediate: true }
)
const { classPath, modelValue } = defineProps({
  classPath: {
    default: '',
    type: String
  },
  modelValue: {
    default: '',
    type: String
  }
})
// getScriptZn 获取公式的方法
const { classInfos, classPathInfos, getScript } = useFormula(classPath)
const formulaDetailRef = ref(null)
const showScript = () => {
  const formula = getScript(formulaZn.value)
  formulaDetailRef?.value?.open(formula, '查看脚本')
}
const fieldTypeMap = {
  'java.lang.String': {
    cn: '文本',
    en: 'string'
  },
  'java.lang.Integer': {
    cn: '整数',
    en: 'integer'
  },
  'java.lang.Double': {
    cn: '小数',
    en: 'double'
  },
  'java.lang.Float': {
    cn: '小数',
    en: 'float'
  },
  'java.lang.Boolean': {
    cn: '布尔',
    en: 'boolean'
  },
  'java.math.BigDecimal': {
    cn: '金额',
    en: 'bigDecimal'
  },
  'java.util.Date': {
    cn: '日期',
    en: 'date'
  },
  'java.util.List': {
    cn: '列表',
    en: 'list'
  }
}
const functions = [
  {
    title: '求和',
    value: 'sum',
    desc: 'sum(a), 返回合计数',
    params: [
      {
        text: '参数a为列表或数组',
        value: '参数a'
      }
    ]
  },
  {
    title: '求平均值',
    value: 'avg',
    desc: 'avg(a), 返回列表或数组中所有值的平均值',
    params: [
      {
        text: '参数a为列表或数组',
        value: '参数a'
      }
    ]
  },
  {
    title: '求个数',
    value: 'count',
    desc: 'count(a), 返回列表或数组元素的个数',
    params: [{ text: '参数a为列表或数组', value: '参数a' }]
  },
  {
    title: '求最大值',
    value: 'max',
    desc: 'max(a), 返回最大值',
    params: [{ text: '参数a为列表或数组', value: '参数a' }]
  },
  {
    title: '求最小值',
    value: 'min',
    desc: 'min(a),返回最小值',
    params: [{ text: '参数a为列表或数组', value: '参数a' }]
  },
  {
    title: '取当前时间',
    value: 'now',
    desc: 'now(),返回当前日期时间字符串。 格式："2004-4-17 14:43:05" ',
    params: [{ text: '无参数' }]
  },
  {
    title: '取时间差',
    value: 'dateDiff',
    desc: 'dateDiff(a,b),返回参数1和参数2间相差多少天',
    params: [
      { text: '参数a：时间日期字符串', value: '参数a' },
      { text: '参数b：时间日期字符串', value: '参数b' }
    ]
  },
  {
    title: '四舍五入',
    value: 'round',
    desc: 'round(number, precision),小数点后指定位数的四舍五入',
    params: [
      { text: '参数number：需要四舍五入的数', value: '参数number' },
      { text: '参数precision：小数点后保留位数', value: '参数precision' }
    ]
  },
  {
    title: '映射',
    value: 'map',
    desc: 'map(objects, key),提取列表对象的指定属性输出列表',
    params: [
      { text: '参数objects：列表对象', value: '参数objects' },
      // eslint-disable-next-line quotes
      { text: "参数key：需要提取的属性，例如：'name'", value: '参数key' }
    ]
  }
]
const keyboards = [
  { text: '如果', width: '30%', value: 'if(){ \n// TODO  \n}' },
  { text: '如果...否则...', width: '40%', value: 'if(){  \n// TODO  \n }else{ \n// TODO  \n}' },
  { text: '循环', width: '30%', value: 'for(i=0;i<10;i++){ \n// TODO 循环体 \n}' },
  { text: '且', width: '25%', value: '&&', space: true },
  { text: '或', width: '25%', value: '||', space: true },
  { text: '等于', width: '25%', value: '==', space: true },
  { text: '不等于', width: '25%', value: '!=', space: true },
  { text: '大于', width: '25%', value: '>', space: true },
  { text: '小于', width: '25%', value: '<', space: true },
  { text: '大于等于', width: '25%', value: '>=', space: true },
  { text: '小于等于', width: '25%', value: '<=', space: true },
  { text: '小括号', width: '25%', value: '()' },
  { text: '中括号', width: '25%', value: '[]' },
  { text: '大括号', width: '25%', value: '｛｝' },
  { text: '返回', width: '25%', value: 'return ' }
]
const operators = [
  { text: '加', width: '25%', value: '+', space: true },
  { text: '减', width: '25%', value: '-', space: true },
  { text: '乘', width: '25%', value: '*', space: true },
  { text: '除', width: '25%', value: '/', space: true },
  { text: '递加', width: '25%', value: '++', space: true },
  { text: '递减', width: '25%', value: '--', space: true },
  { text: '=', width: '25%', value: '=', space: true },
  { text: '+=', width: '25%', value: '+=', space: true },
  { text: '-=', width: '25%', value: '-=', space: true },
  { text: '*=', width: '25%', value: '*=', space: true },
  { text: '/=', width: '25%', value: '/=', space: true },
  { text: '%=', width: '25%', value: '%=', space: true }
]
const formulaMockRef = ref(null)
const mock = () => {
  const formula = getScript(formulaZn.value)
  formulaMockRef.value?.open(classInfos.value, formula)
}
const codeMirrorRef = ref()
const lastCursor = computed(() => {
  if (!codeMirrorRef.value) return formulaZn.value.length
  if (codeMirrorRef.value.lastCursor === -1) return formulaZn.value.length
  return codeMirrorRef.value.lastCursor
})

const insertField = (info, field) => {
  const start = formulaZn.value.slice(0, lastCursor.value)
  const value = start.endsWith('.') ? field.name : ` ${info.name}.${field.name}`
  formulaZn.value = `${start}${value}${formulaZn.value.slice(lastCursor.value)}`
  emits('update:modelValue', formulaZn.value)
}
const insertFunction = (func) => {
  const params = func.params.map((item) => item.value)
  formulaZn.value += `${func.value}( ${params.join(', ')} )`
  emits('update:modelValue', formulaZn.value)
}
const insertKey = (key) => {
  formulaZn.value += ` ${key.value}`
  emits('update:modelValue', formulaZn.value)
}
</script>
<style lang="less" scoped>
.formula-generator {
  width: 100%;
  height: 600px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  &__editor {
    flex-basis: 200px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    &-title {
      display: flex;
      align-items: center;
      padding: 5px 10px;
    }
    &-content {
      flex: 1;
      overflow: auto;
    }
  }
  &__tool {
    flex: 1;
    display: flex;
    height: 360px;
    &-entity {
      width: 25%;
      border-top: 1px solid #d9d9d9;
      border-right: 1px solid #d9d9d9;
      padding: 1px;
      display: flex;
      flex-direction: column;
      .field-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        cursor: pointer;
        user-select: none;
        border-bottom: 1px solid #e0e0e0;
        &:last-child {
          border-bottom: none;
        }
        &:hover {
          background: #ebedf1;
        }
      }
    }
    &-function {
      width: 25%;
      border-top: 1px solid #d9d9d9;
      border-right: 1px solid #d9d9d9;
      padding: 1px;
      display: flex;
      flex-direction: column;
      .function-item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 5px 16px;
        cursor: pointer;
        user-select: none;
        &:hover {
          background: #ebedf1;
        }
        &__value {
          color: rgba(0, 0, 0, 0.85);
          font-size: 16px;
        }
        &__title {
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
        }
      }
    }
    &-variable {
      width: 50%;
      border-top: 1px solid #d9d9d9;
      padding: 1px;
      display: flex;
      flex-direction: column;
    }
    &-title {
      padding: 5px 10px;
      /*background: #eceff7;*/
      background: white;
      border-bottom: 1px solid #d9d9d9;
      user-select: none;
      display: flex;
      justify-content: space-between;
    }
    &-content {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
    .variable-wrapper {
      padding: 5px;
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      border-bottom: 1px solid #d9d9d9;
      display: flex;
      flex-direction: column;
      position: relative;
      .variable-header {
        background: #ebedf1;
        display: flex;
        justify-content: space-between;
        padding: 5px 16px;
        user-select: none;
        position: absolute;
        left: 5px;
        right: 5px;
      }
      .variable-body {
        overflow: auto;
        margin-top: 31px;
      }
      .variable-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 16px;
        cursor: pointer;
        user-select: none;
        &:hover {
          background: #ebedf1;
        }
        &__name {
          width: 40%;
        }
        &__type {
          width: 30%;
          text-align: center;
        }
        &__value {
          width: 30%;
          text-align: center;
        }
      }
    }
    .keyboard-wrapper {
      padding: 5px;
      flex: 1;
      .keyboard {
        display: flex;
        flex-wrap: wrap;
        border-top: 1px solid #d9d9d9;
        border-left: 1px solid #d9d9d9;
        .key-item {
          height: 30px;
          border-right: 1px solid #d9d9d9;
          border-bottom: 1px solid #d9d9d9;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          user-select: none;
          &:hover {
            background: rgba(24, 143, 253, 0.2);
            color: rgb(24, 143, 253);
          }
          &:active {
            background: rgb(24, 143, 253);
            color: white;
          }
        }
      }
    }
  }
  .data_type {
    &__string {
      color: rgba(40, 132, 227, 1);
      background: rgba(40, 132, 227, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__integer {
      color: rgb(5, 99, 43);
      background: rgba(5, 99, 43, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__double,
    &__float {
      color: rgb(63, 176, 218);
      background: rgba(63, 176, 218, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__boolean {
      color: rgba(123, 94, 186, 1);
      background: rgba(123, 94, 186, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__date {
      color: rgb(251, 193, 40);
      background: rgba(251, 193, 40, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__bigDecimal {
      color: rgb(227, 139, 82);
      background: rgba(227, 139, 82, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__list {
      color: rgb(136, 117, 104);
      background: rgba(92, 56, 34, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
    &__unknown {
      color: rgb(239, 26, 147);
      background: rgba(239, 26, 147, 0.2);
      padding: 0 10px;
      border-radius: 5px;
      display: inline-block;
    }
  }
}
</style>
