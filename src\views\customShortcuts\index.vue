<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button class="mb-[10px]" type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button class="mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button class="mb-[10px]" :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="handleRemove(false)">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="">
          <s-input
            v-model="search.name"
            placeholder="搜索名称"
            class="ml-[10px] !w-[280px]"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <a-form-item>
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 1000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'icon'">
          <i :class="record.icon"></i>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowEdit(record)">编辑</span>
          <span
            v-if="['TEMP', 'BACK', 'DISABLE'].includes(record.status)"
            class="primary-btn"
            @click="handleRemove(record)"
          >
            删除
          </span>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入订单"
      :download-fn="() => exportExcel('订单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import usePageTable from '@/hooks/usePageTable'
import { renderDict } from '@/utils/render'
import AddEdit from './components/AddEdit.vue'
import useTableSelection from '@/hooks/useTableSelection'
import { getPage, deleteBatch, exportExcel, importExcel, updateStatus } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
})
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
const search = ref({
  column: 'createTime',
  order: 'desc',
  id: '',
  name: ''
})
const searchFilter = ref({})
const searchList = reactive([
  { label: '启用状态', name: 'status', type: 'dic', placeholder: '请选择下单时间', code: 'CT_BASE_ENUM_BaseStatus' },
  {
    label: '所属模块',
    name: 'module',
    type: 'dic',
    placeholder: '请选择所属模块',
    code: 'CT_BASE_ENUM_CustomCommonFunction_Module'
  },
  { label: '图标', name: 'icon', type: 'input', placeholder: '请输入图标' },
  { label: '路径', name: 'url', type: 'input', placeholder: '请输入路径' }
])

const defaultColumns = [
  { title: '名称', dataIndex: 'name', width: 180, fixed: true },
  {
    title: '启用状态',
    dataIndex: 'status'
    // customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_BaseStatus', 'dot')
  },
  {
    title: '所属模块',
    dataIndex: 'module',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_CustomCommonFunction_Module')
  },
  { title: '图标', dataIndex: 'icon' },
  { title: '路径', dataIndex: 'url' },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)
const addEditRef = ref()
// 新增
const handleAdd = () => {
  addEditRef?.value.open()
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row.id)
}

const handleStatusChange = async (row, val) => {
  if (row.loading) return
  try {
    row.loading = true
    const data = await updateStatus({ ids: row.id, status: val ? 'ENABLE' : 'DISABLE' })
    row.loading = false
    message.success(data.message)
    onTableChange()
  } catch {
    row.loading = false
    row.checked = !val
  }
}

const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前资产处置？' : '确认批量删除选中资产处置？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('订单数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
