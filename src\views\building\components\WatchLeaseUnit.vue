<template>
  <a-modal
    v-model:open="visible"
    title="查看租赁单元"
    width="1200px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    :footer="false"
  >
    <p class="text-[16px] font-bold mb-[12px]">
      {{ floorInfo.projectName }} / {{ floorInfo.buildingName }} / {{ floorInfo.name }}
    </p>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 2000, y: '50vh' }"
    ></a-table>
    <div class="pt-[24px]"></div>
  </a-modal>
</template>

<script setup>
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { renderDictTag, renderMoney } from '@/utils/render'

const floorInfo = reactive({
  id: '',
  name: '',
  projectName: '',
  buildingName: ''
})

const visible = ref(false)
const open = (info) => {
  visible.value = true
  Object.assign(floorInfo, info)
  loadLeaseUnitList()
}

const list = ref([])
const tableLoading = ref(false)
const loadLeaseUnitList = async () => {
  tableLoading.value = true
  const { result } = await getLeaseUnitList({ wyFloor: floorInfo.id })
  list.value = result.records
  tableLoading.value = false
}

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '使用类型', dataIndex: 'useType_dictText' },
  { title: '租赁面积', dataIndex: 'leaseArea', customRender: ({ text }) => renderMoney(text, 4, 'm²') },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  {
    title: '数据状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '业务状态', dataIndex: 'bizStatus_dictText' },
  { title: '配套设施', dataIndex: 'supportFacility' },
  { title: '生效日期', dataIndex: 'effectDate' },
  { title: '到期时间', dataIndex: 'expireDate' },
  { title: '层数/总层数', dataIndex: 'layerNum' },
  { title: '产权', dataIndex: 'property' },
  { title: '项目', dataIndex: 'wyProject_dictText' },
  { title: '租赁单元编号', dataIndex: 'number', width: 200 }
]

defineExpose({ open })
</script>
