<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    :title="ruleForm.id ? '编辑预警方案' : '新建预警方案'"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
    :mask-closable="false"
  >
    <a-form :model="ruleForm" ref="formRef" :rules="rules" :label-col="{ style: { width: '80px' } }" autocomplete="off">
      <a-row :gutter="24">
        <a-col :span="12" v-if="ruleForm.id">
          <a-form-item label="方案编码" name="number">
            <a-input v-model:value="ruleForm.number" placeholder="自动生成" allow-clear disabled></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="方案名称" name="name">
            <a-input v-model:value="ruleForm.name" placeholder="请输入方案名称" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="预警实体" name="alterBizBill">
            <!-- <a-select
              v-model:value="ruleForm.alterBizBill"
              :options="classesList"
              placeholder="请选择预警实体"
              allow-clear
              @change="classesChange"
            /> -->
            <api-page-select
              v-model="ruleForm.alterBizBill"
              placeholder="请选择预警实体"
              :async-fn="warningClass"
              :field-names="{ label: 'name', value: 'id' }"
              @change="classesChange"
            ></api-page-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="经办人" name="operator">
            <a-form-item-rest>
              <f7-select
                v-model="ruleForm.operator"
                f7-type="user"
                :depart-id="ruleForm.operatorDepart"
                relation-depart
                placeholder="请选择经办人"
              ></f7-select>
            </a-form-item-rest>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="业务部门" name="operatorDepart">
            <depart-select v-model="ruleForm.operatorDepart" placeholder="请选择业务部门"></depart-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="ruleForm.remark"
              placeholder="请输入备注"
              :maxlength="255"
              :rows="4"
              show-count
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>
      <div class="mb-[12px] mt-[12px] flex justify-between items-center">
        <div class="text-[16px] font-bold">预警方案分录</div>
        <a-button type="primary" ghost @click="handleAlterSchemeEntryList">
          <span class="a-icon-plus mr-[8px]"></span>
          添加
        </a-button>
      </div>
      <a-table
        size="small"
        :row-key="(record, index) => record.id || index"
        :data-source="ruleForm.alterSchemeEntryList"
        :columns="columns"
        :scroll="{ y: 300, x: 1500 }"
        :pagination="false"
        bordered
      >
        <!-- <template #headerCell="{ column }"></template> -->
        <template #bodyCell="{ column, record, index }">
          <!-- <template v-if="column.dataIndex === 'ruleFormula'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'ruleFormula']"
              :rules="{
                required: true,
                message: '请选择预警条件',
                trigger: ['change', 'blur']
              }"
            >
              <div class="flex items-center w-[100%]">
                <span
                  class="a-icon-remove remove-btn cursor-pointer text-[18px] mr-[5px]"
                  @click="rowDel(index)"
                ></span>
                <a-input-search
                  v-model:value="record.ruleFormula"
                  placeholder="请选择预警条件"
                  enter-button="选择"
                  @search="ruleFormulaClick(record.ruleFormula, 'ruleFormula', index)"
                />
              </div>
            </a-form-item>
          </template> -->

          <template v-if="column.dataIndex === 'alterMessage'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'alterMessage']"
              :rules="{
                required: true,
                message: '请选择预警消息',
                trigger: ['change', 'blur']
              }"
            >
              <div class="flex items-center w-[100%]">
                <span
                  class="a-icon-remove remove-btn cursor-pointer text-[18px] mr-[5px]"
                  @click="rowDel(index)"
                ></span>
                <a-input-search
                  v-model:value="record.alterMessage"
                  placeholder="请选择预警消息"
                  enter-button="编辑"
                  readonly
                  @search="alterMessageClick(record.alterMessage, 'alterMessage', index)"
                />
              </div>
            </a-form-item>
          </template>
          <!-- 提交 预警时点 -->
          <template v-if="column.dataIndex === 'submitAlter'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'submitAlter']"
              :rules="{
                required: validateTrue(record),
                message: '请选择预警时点',
                trigger: ['change', 'blur']
              }"
            >
              <a-checkbox v-model:checked="record.submitAlter" @change="record.submitAlterType = undefined">
                是否预警
              </a-checkbox>
            </a-form-item>
          </template>
          <!--提交 预警方式 -->
          <template v-if="column.dataIndex === 'submitAlterType'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'submitAlterType']"
              :rules="{
                required: record.submitAlter,
                message: '请选择预警方式',
                trigger: ['change', 'blur']
              }"
            >
              <a-select
                v-model:value="record.submitAlterType"
                :options="alterTypeList"
                placeholder="请选择预警方式"
                allow-clear
              />
            </a-form-item>
          </template>
          <!--提交 预警人员-->
          <!-- <template v-if="column.dataIndex === 'submitAlterWho'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'submitAlterWho']"
              :rules="{
                required: false,
                message: '请输入预警对象',
                trigger: ['change', 'blur']
              }"
            >
              <div>{{ record.submitAlterWho }}</div>
            </a-form-item>
          </template> -->

          <!-- 审核 预警时点 -->
          <template v-if="column.dataIndex === 'auditAlter'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'auditAlter']"
              :rules="{
                required: validateTrue(record),
                message: '请选择预警时点',
                trigger: ['change', 'blur']
              }"
            >
              <a-checkbox v-model:checked="record.auditAlter" @change="record.auditAlterType = undefined">
                是否预警
              </a-checkbox>
            </a-form-item>
          </template>
          <!--审核 预警方式 -->
          <template v-if="column.dataIndex === 'auditAlterType'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'auditAlterType']"
              :rules="{
                required: record.auditAlter,
                message: '请选择预警方式',
                trigger: ['change', 'blur']
              }"
            >
              <a-select
                v-model:value="record.auditAlterType"
                :options="alterTypeList"
                placeholder="请选择预警方式"
                allow-clear
              />
            </a-form-item>
          </template>
          <!--审核 预警人员-->
          <!-- <template v-if="column.dataIndex === 'auditAlterWho'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'auditAlterWho']"
              :rules="{
                required: false,
                message: '请输入预警对象',
                trigger: ['change', 'blur']
              }"
            >
              <div>{{ record.auditAlterWho }}</div>
            </a-form-item>
          </template> -->

          <!--周期 定时表达式 -->
          <template v-if="column.dataIndex === 'clockingAlterCron'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'clockingAlterCron']"
              :rules="{
                required: validateTrue(record),
                message: '请选择定时表达式',
                trigger: ['change', 'blur']
              }"
            >
              <a-input-search
                v-model:value="record.clockingAlterCron"
                placeholder="请选择定时表达式"
                enter-button="选择"
                readonly
                @search="clockingAlterCronClick(record.clockingAlterCron, index)"
              />
            </a-form-item>
          </template>

          <!--周期 消息接收人 -->
          <template v-if="column.dataIndex === 'clockingAlterWho'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'clockingAlterWho']"
              :rules="{
                required: record.clockingAlterCron,
                message: '请选择消息接收人',
                trigger: ['change', 'blur']
              }"
            >
              <f7-select
                v-model="record.clockingAlterWho"
                f7-type="user"
                placeholder="请选择消息接收人"
                multiple
              ></f7-select>
            </a-form-item>
          </template>
          <!--周期 制单人领导 -->
          <template v-if="column.dataIndex === 'clockingAlterRole'">
            <a-form-item
              class="mb-[24px] !w-[100%]"
              label=""
              :name="['alterSchemeEntryList', index, 'clockingAlterRole']"
              :rules="{
                required: false,
                message: '请选择制单人领导',
                trigger: ['change', 'blur']
              }"
            >
              <dict-select
                v-model="record.clockingAlterRole"
                placeholder="请选择制单人领导"
                code="CT_BASE_ENUM_SelfAssetTrackingInfo_Status"
              ></dict-select>
            </a-form-item>
          </template>
        </template>
      </a-table>
    </a-form>

    <template #footer>
      <a-button type="primary" @click="handleConfirm" :loading="submitLoading">提交</a-button>
      <a-button @click="handleStash" :loading="stashLoading">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- <formula-edit ref="formulaEditRef" @change="valueChange"></formula-edit> -->
  <!-- 预警消息编辑 -->
  <warning-message-edit ref="warningMessageEditRef" @change="valueChange"></warning-message-edit>
  <cron-select ref="cronSelectRef" @change="cronChange"></cron-select>
</template>

<script setup>
// import FormulaEdit from '@/components/FormulaEdit.vue'
import WarningMessageEdit from './WarningMessageEdit.vue'
import { message, Modal } from 'ant-design-vue'
import { detailById, edit, stash, queryAlterSchemeEntryByMainId, submit } from '../apis'
import { useUserStore } from '@/store/modules/user'
import { page as warningClass } from '@/views/earlyWarningManage/earlyWarningEntity/apis'

const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (id) => {
  if (id) {
    getDetailById(id)
  } else {
    ruleForm.operator = userInfo.value.id || ''
    ruleForm.operatorDepart = userInfo.value.currentDepart
  }
  visible.value = true
}
defineExpose({ open })
const store = useUserStore()
const userInfo = computed(() => store.userInfo)

const getDetailById = async (id) => {
  const { result } = await detailById(id)
  const data = await queryAlterSchemeEntryByMainId(id)
  data.result.forEach((item) => {
    item.clockingAlterWho = item.clockingAlterWho && item.clockingAlterWho.split(',')
  })
  Object.assign(ruleForm, result, { alterSchemeEntryList: data.result })
}

const ruleForm = reactive({
  id: '',
  number: '',
  name: '',
  alterBizBill: undefined,
  operator: '',
  operatorDepart: '',
  alterSchemeEntryList: []
})
const requestParams = computed(() => {
  const params = { ...ruleForm }
  params.alterSchemeEntryList.forEach((item) => {
    item.clockingAlterWho = item.clockingAlterWho && item.clockingAlterWho.join(',')
  })
  return params
})
const rules = {
  name: [{ required: true, message: '请输入方案名称', trigger: ['blur'] }],
  alterBizBill: [{ required: true, message: '请选择预警实体', trigger: ['change'] }],
  operator: [{ required: true, message: '请选择经办人', trigger: ['change'] }],
  operatorDepart: [{ required: true, message: '请选择业务部门', trigger: ['change'] }]
}
const columns = [
  // {
  //   title: '序号',
  //   dataIndex: 'index',
  //   width: 80,
  //   align: 'center',
  //   fixed: true,
  //   customRender: ({ index }) => index + 1
  // },
  // { title: '预警条件', dataIndex: 'ruleFormula', width: 300, fixed: true },
  { title: '预警消息', dataIndex: 'alterMessage', width: 300, fixed: true },
  {
    title: '提交预警',
    children: [
      {
        title: '预警时点',
        dataIndex: 'submitAlter'
      },
      {
        title: '预警方式',
        dataIndex: 'submitAlterType'
      }
      // {
      //   title: '预警对象',
      //   dataIndex: 'submitAlterWho'
      // }
    ]
  },
  {
    title: '审核预警',
    children: [
      {
        title: '预警时点',
        dataIndex: 'auditAlter'
      },
      {
        title: '预警方式',
        dataIndex: 'auditAlterType'
      }
      // {
      //   title: '预警对象',
      //   dataIndex: 'auditAlterWho'
      // }
    ]
  },
  {
    title: '周期预警',
    children: [
      {
        title: '定时表达式',
        dataIndex: 'clockingAlterCron',
        width: 200
      },
      { title: '消息接收人', dataIndex: 'clockingAlterWho' }
      // {
      //   title: '预警对象',
      //   children: [
      //     { title: '消息接收人', dataIndex: 'clockingAlterWho' },
      //     { title: '制单人领导', dataIndex: 'clockingAlterRole' }
      //   ]
      // }
    ]
  }
]
// const classesList = [
//   {
//     label: '租赁合同',
//     value: 'org.jeecg.modules.park.biz.contractmanage.entity.Contract',
//     key: 'org.jeecg.modules.park.biz.contractmanage.entity.Contract'
//   },
//   {
//     label: '招租方案',
//     value: 'org.jeecg.modules.park.biz.leasemanage.entity.RentScheme',
//     key: 'org.jeecg.modules.park.biz.leasemanage.entity.RentScheme,org.jeecg.modules.park.biz.leasemanage.entity.RentSchemeEntry'
//   }
// ]
const alterTypeList = [
  { label: '提醒', value: 'Alter' },
  { label: '禁止', value: 'Forbidden' }
]
// 预警条件的变化
const curKey = ref('')
const classesChange = (value, option) => {
  curKey.value = option.className
}
const handleAlterSchemeEntryList = () => {
  ruleForm.alterSchemeEntryList.push({
    id: '',
    ruleFormula: '',
    submitAlter: false,
    submitAlterType: undefined,
    auditAlter: false,
    auditAlterType: undefined,
    clockingAlterCron: '',
    clockingAlterWho: ''
  })
}

const rowDel = (index) => {
  Modal.confirm({
    title: '确定删除当前预警方案分录？',
    content: '',
    centered: true,
    onOk: () => {
      ruleForm.value.alterSchemeEntryList.splice(index, 1)
    }
  })
}
// 预警条件选择
const curIndex = ref(0)
const curName = ref('')
// const formulaEditRef = ref()
// const ruleFormulaClick = (value, key, index) => {
//   curName.value = key
//   curIndex.value = index
//   formulaEditRef.value.open(value, curKey.value)
// }
// 预警消息
// const alterMessageClick = (value, key, index) => {
//   curName.value = key
//   curIndex.value = index
//   formulaEditRef.value.open(value, curKey.value)
// }
const valueChange = (value) => {
  ruleForm.alterSchemeEntryList[curIndex.value][curName.value] = value
}

// 预警消息
const warningMessageEditRef = ref()
const alterMessageClick = (value, key, index) => {
  curName.value = key
  curIndex.value = index
  warningMessageEditRef.value.open(value)
}

const cronSelectRef = ref()
const clockingAlterCronClick = (clockingAlterCron, index) => {
  curIndex.value = index
  cronSelectRef.value.open(clockingAlterCron)
}
const cronChange = (value) => {
  ruleForm.alterSchemeEntryList[curIndex.value].clockingAlterCron = value
}

const formRef = ref()
const submitLoading = ref(false)
// 提交
const handleConfirm = async () => {
  await formRef.value.validate()
  submitLoading.value = true
  try {
    const data = await submit(requestParams.value)
    message.success(data.message)
    emits('loadData')
    handleCancel()
  } finally {
    submitLoading.value = false
  }
}
// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  await formRef.value.validate()
  stashLoading.value = true
  try {
    const { result, message: msg } = await (ruleForm.id ? edit(requestParams.value) : stash(requestParams.value))
    message.success(msg)
    getDetailById(result.id)
    emits('loadData')
    stashLoading.value = false
  } finally {
    stashLoading.value = false
  }
}
// 取消
const handleCancel = () => {
  Object.assign(ruleForm, {
    id: '',
    number: '',
    name: '',
    alterBizBill: undefined,
    operator: '',
    operatorDepart: '',
    alterSchemeEntryList: []
  })
  visible.value = false
}

// 动态校验必填项
const validateTrue = (row) => {
  if (row.submitAlter || row.auditAlter || row.clockingAlterCron) {
    return false
  }
  return true
}
</script>
