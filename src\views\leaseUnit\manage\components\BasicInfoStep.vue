<template>
  <div>
    <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">租赁单元基础信息</h4>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-align="left"
      autocomplete="off"
      :label-col="{ style: { width: '100px' } }"
    >
      <a-form-item label="单元名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入单元名称" show-count :maxlength="50" />
      </a-form-item>
      <a-form-item label="资产产权" name="virtualLeaseUnit">
        <div class="flex">
          <a-radio-group v-model:value="formData.virtualLeaseUnit" :disabled="isAssetsSource">
            <a-radio :value="false">无产权</a-radio>
            <a-radio :value="true">有产权</a-radio>
          </a-radio-group>
          <assets-select
            v-if="formData.virtualLeaseUnit === true"
            v-model:model-value="formData.houseOwner"
            :options="formData.houseOwner ? [{ value: formData.houseOwner, label: formData.houseOwner_dictText }] : []"
            placeholder="选择资产"
            :disabled="isAssetsSource"
            @update:model-value="handleAssetChange"
          />
        </div>
      </a-form-item>
      <a-form-item label="产权用途" name="propertyUse">
        <dict-select v-model="formData.propertyUse" placeholder="产权用途" code="CT_BAS_PropertyUse"></dict-select>
      </a-form-item>
      <a-form-item label="所属项目" name="wyProject">
        <a-cascader
          v-model:value="formData.wyProjectArray"
          :options="projectOptions"
          :field-names="{ label: 'label', value: 'value', children: 'children' }"
          :load-data="loadBuildingFloorData"
          placeholder="请选择项目/楼栋/楼层"
          @change="handleProjectChange"
        />
      </a-form-item>
      <a-form-item label="地址" name="detailAddress">
        <address-select
          v-model:model-value="formData.pcaCode"
          v-model:address="formData.detailAddress"
          :disabled="isAssetsSource"
          @set-address="setAddress"
        />
      </a-form-item>
      <a-form-item label="资产类型" name="assetType">
        <dict-select
          v-model="formData.assetType"
          placeholder="资产类型"
          code="CT_BASE_ENUM_LeaseUnit_AssetType"
        ></dict-select>
      </a-form-item>
      <a-form-item label="资产权属公司" name="ownerCompany">
        <company-select v-model="formData.ownerCompany" placeholder="请选择资产权属公司" type="all"></company-select>
      </a-form-item>
      <a-form-item label="租金归集公司" name="collectionCompany">
        <company-select
          v-model="formData.collectionCompany"
          placeholder="请选择租金归集公司"
          type="all"
        ></company-select>
      </a-form-item>
      <a-form-item label="物业管理公司" name="manageCompany">
        <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司" disabled></company-select>
      </a-form-item>
      <a-form-item label="使用权类型" name="landNature">
        <dict-select v-model="formData.landNature" placeholder="使用权类型" code="CT_BAS_LandNature"></dict-select>
      </a-form-item>
      <a-form-item label="租赁单元类别" name="treePath">
        <a-cascader
          v-model:value="formData.treePath"
          :options="leaseUnitTreeOptions"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          :load-data="loadLeaseUnitTreeData"
          placeholder="请选择租赁单元类别"
          change-on-select
          @change="handleLeaseUnitTreeChange"
        />
      </a-form-item>
      <a-form-item label="配套设施" name="supportFacility" class="form-item-full">
        <a-textarea
          v-model:value="formData.supportFacility"
          placeholder="配套设施"
          :rows="4"
          show-count
          :maxlength="255"
        />
      </a-form-item>
      <a-form-item label="备注" name="remark" class="form-item-full">
        <a-textarea v-model:value="formData.remark" placeholder="备注" :rows="4" show-count :maxlength="255" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { projectPage, queryBuilding } from '@/views/projects/apis.js'
import { queryFloor } from '@/views/building/apis/building.js'
import { detailById } from '@/views/assets/manage/apis'
import { getLeaseUnitTree } from '../apis/leaseUnitTree'
import emitter from '@/utils/mitt'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  isAssetsSource: {
    type: Boolean,
    required: true
  }
})

const formRef = ref()

const projectOptions = ref([])
const leaseUnitTreeOptions = ref([])

const checkAddress = (_rule, value) => {
  // if (!props.formData.pcaCode || !Array.isArray(props.formData.pcaCode) || props.formData.pcaCode.length === 0) {
  //   return Promise.reject('请选择区域')
  // }
  if (!value) {
    return Promise.reject('请输入详细地址')
  }
  return Promise.resolve()
}

const rules = {
  name: [
    { required: true, message: '请输入单元名称', trigger: 'blur' },
    { min: 2, max: 50, message: '单元名称长度应为2-50个字符', trigger: 'blur' }
  ],
  detailAddress: [{ required: true, validator: checkAddress, trigger: ['blur', 'change'] }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: 'change' }],
  ownerCompany: [{ required: true, message: '请选择资产权属公司', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  landNature: [{ required: true, message: '请选择使用权类型', trigger: 'change' }],
  treePath: [{ required: true, message: '请选择租赁单元分类', trigger: 'change' }],
  supportFacility: [{ max: 500, message: '配套设施描述不能超过500个字符', trigger: 'blur' }],
  remark: [{ max: 500, message: '备注不能超过500个字符', trigger: 'blur' }],
  propertyUse: [{ required: true, message: '请选择产权用途', trigger: 'change' }]
}

/**
 * 处理资产选择变更事件，获取资产详情并同步相关字段
 * @param {string} selectId - 选中的资产ID
 */
const handleAssetChange = async (selectId) => {
  if (!selectId) {
    props.formData.houseOwner = undefined
    return
  }

  const { result } = await detailById(selectId)
  if (result) {
    props.formData.houseOwner = result.id
    // 关联项目-楼栋-楼层
    props.formData.wyProject = result.wyProject
    props.formData.wyBuilding = result.wyBuilding
    props.formData.wyFloor = result.wyFloor
    props.formData.wyProjectArray = [result.wyProject, result.wyBuilding, result.wyFloor].filter(Boolean)
    // 关联省市区
    props.formData.province = result.province
    props.formData.city = result.city
    props.formData.area = result.area
    props.formData.pcaCode = result.pcaCode ? result.pcaCode.split(';') : []
    // 关联详细地址
    props.formData.detailAddress = result.detailAddress

    if (result.wyProject) {
      await initProjectData()
    }
  }
}

/**
 * 处理项目楼栋楼层选择变更事件
 * @param {Array} value - 选中的项目楼栋楼层ID数组
 */
const handleProjectChange = (value) => {
  props.formData.wyProjectArray = value
  ;[props.formData.wyProject, props.formData.wyBuilding, props.formData.wyFloor] = value
}

/**
 * 设置地址数据
 * @param {Array} selectedOptions 选择的地址选项数组
 */
const setAddress = (selectedOptions) => {
  if (selectedOptions && selectedOptions.length) {
    props.formData.province = selectedOptions[0].label
    props.formData.city = selectedOptions.length === 3 ? selectedOptions[1].label : ''
    props.formData.area = selectedOptions.length === 3 ? selectedOptions[2].label : ''
    return
  }
  props.formData.province = ''
  props.formData.city = ''
  props.formData.area = ''
}

/**
 * 动态加载楼栋和楼层数据
 */
const loadBuildingFloorData = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true

  try {
    const isLoadingBuilding = selectedOptions.length === 1
    const api = isLoadingBuilding ? queryBuilding : queryFloor
    const res = await api({ id: targetOption.value })

    if (res?.result) {
      targetOption.children = res.result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: !isLoadingBuilding
      }))
    }
  } finally {
    targetOption.loading = false
  }
}

/**
 * 动态加载租赁单元树形数据
 */
const loadLeaseUnitTreeData = async () => {
  try {
    const response = await getLeaseUnitTree()
    if (response && response.success && Array.isArray(response.result)) {
      leaseUnitTreeOptions.value = response.result
    }
  } catch {
    // 加载失败时的处理
    leaseUnitTreeOptions.value = []
  }
}

/**
 * 处理租赁单元类别选择变更事件
 * @param {Array} value - 选中的租赁单元类别ID数组
 */
const handleLeaseUnitTreeChange = (value) => {
  props.formData.treeId = value && value.length > 0 ? value[value.length - 1] : undefined
}

/**
 * 加载项目列表数据
 */
const loadProjectList = async () => {
  const { result } = await projectPage({ status: 'ENABLE', pageSize: 1000 })
  projectOptions.value =
    result?.records?.map((item) => ({
      value: item.id,
      label: item.name,
      isLeaf: false
    })) || []

  if (props.formData.wyProject) {
    await initProjectData()
  }
}

/**
 * 加载租赁单元树形数据
 */
const loadLeaseUnitTreeList = async () => {
  try {
    const res = await getLeaseUnitTree()
    if (res?.result && Array.isArray(res.result)) {
      leaseUnitTreeOptions.value = res.result
    }
  } catch {
    leaseUnitTreeOptions.value = []
  }
}

/**
 * 初始化项目数据，用于编辑时回显级联选择器
 */
const initProjectData = async () => {
  const buildingRes = await queryBuilding({ id: props.formData.wyProject, pageSize: 1000 })
  const projectOption = projectOptions.value.find((item) => item.value === props.formData.wyProject)

  if (projectOption && buildingRes?.result) {
    projectOption.children = buildingRes.result.map((item) => ({
      value: item.id,
      label: item.name,
      isLeaf: false
    }))

    if (props.formData.wyBuilding) {
      const floorRes = await queryFloor({ id: props.formData.wyBuilding })
      const buildingOption = projectOption.children.find((item) => item.value === props.formData.wyBuilding)

      if (buildingOption && floorRes?.result) {
        buildingOption.children = floorRes.result.map((item) => ({
          value: item.id,
          label: item.name,
          isLeaf: true
        }))
      }
    }

    // 确保数据加载完成后重新设置级联选择器的值，触发重新渲染
    await nextTick()
    const currentArray = [props.formData.wyProject, props.formData.wyBuilding, props.formData.wyFloor].filter(Boolean)
    props.formData.wyProjectArray = [...currentArray]
  }
}

/**
 * 验证表单
 */
const validate = () => {
  return formRef.value?.validate()
}

/**
 * 重置表单字段
 */
const resetFields = () => {
  formRef.value?.resetFields()
}

watch(
  () => props.formData.virtualLeaseUnit,
  (newVal, oldVal) => {
    if (oldVal === true && newVal === false) {
      props.formData.houseOwner = undefined
    }
  }
)

onMounted(() => {
  loadProjectList()
  loadLeaseUnitTreeList()
  // 监听租赁单元树更新事件
  emitter.on('lease-unit-tree-updated', () => {
    // 重新加载租赁单元树数据
    loadLeaseUnitTreeList()
  })
})

onUnmounted(() => {
  // 移除事件监听
  emitter.off('lease-unit-tree-updated')
})

defineExpose({
  validate,
  resetFields,
  initProjectData
})
</script>
