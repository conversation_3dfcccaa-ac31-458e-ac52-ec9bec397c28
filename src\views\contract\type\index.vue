<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex items-center">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.name"
          placeholder="搜索名称"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
        <div class="flex items-center ml-[10px]">
          <a-checkbox v-model:checked="viewEnabled">仅看启用</a-checkbox>
        </div>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1400, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail ref="detailRef" @refresh="refreshFromDetail" @edit="handleEdit"></detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('合同类型导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, updateStatus } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'

const viewEnabled = ref(false) // 是否仅看启用
watch(viewEnabled, (val) => {
  params.status = val ? 'ENABLE' : undefined
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
})

const params = reactive({
  column: 'number',
  order: 'asc',
  name: undefined,
  number: undefined,
  status: undefined,
  manageCompany: undefined,
  paymentNature: undefined,
  createBy: undefined,
  isAutoClear: undefined,
  isIncludeJtStampTax: undefined,
  isKeepDecimals: undefined,
  contractCategory: undefined
})
const searchList = [
  { label: '编号', name: 'number', type: 's-input' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
  {
    label: '合同类别',
    name: 'contractCategory',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_ContractType_ContractCategory'
  },
  { label: '款项性质', name: 'paymentNature', type: 'dict-select', code: 'CT_BASE_ENUM_ContractType_PaymentNature' },
  { label: '状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_BaseStatus' },
  { label: '备注', name: 'remark', type: 's-input' },
  {
    label: '纳入计提印花税',
    name: 'isIncludeJtStampTax',
    type: 'a-select',
    options: [
      { name: '是', id: true },
      { name: '否', id: false }
    ]
  },
  {
    label: '保留小数',
    name: 'isKeepDecimals',
    type: 'a-select',
    options: [
      { name: '是', id: true },
      { name: '否', id: false }
    ]
  },
  {
    label: '自动清算',
    name: 'isAutoClear',
    type: 'a-select',
    options: [
      { name: '是', id: true },
      { name: '否', id: false }
    ]
  }
]

const defaultColumns = [
  { title: '编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText' },
  { title: '名称', dataIndex: 'name' },
  { title: '合同类别', dataIndex: 'contractCategory_dictText' },
  { title: '款项性质', dataIndex: 'paymentNature_dictText' },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  {
    title: '纳入计提印花税',
    width: 140,
    dataIndex: 'isIncludeJtStampTax',
    customRender: ({ text }) => (text ? '是' : text === false ? '否' : '')
  },
  {
    title: '保留小数',
    dataIndex: 'isKeepDecimals',
    customRender: ({ text }) => (text ? '是' : text === false ? '否' : '')
  },
  {
    title: '自动清算',
    dataIndex: 'isAutoClear',
    customRender: ({ text }) => (text ? '是' : text === false ? '否' : '')
  },
  { title: '状态', dataIndex: 'status', width: 100 },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

const handleStatusChange = async (data, val) => {
  if (data.loading) return
  try {
    data.loading = true
    const { message: msg } = await updateStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
    message.success(msg)
    data.loading = false
    data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    data.loading = false
    data.checked = !val
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除该项？',
    content: undefined,
    centered: true,
    onOk: async () => {
      const { message: msg } = await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success(msg)
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('合同类型数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
