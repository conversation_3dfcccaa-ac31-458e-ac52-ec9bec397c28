<template>
  <div>
    <div class="flex items-center justify-between mb-[12px]">
      <strong class="text-[16px]">租赁单元</strong>
      <a-button type="primary" size="medium" @click="handleAddUnit">
        <i class="a-icon-plus"></i>
        添加单元
      </a-button>
    </div>
    <a-table
      :data-source="form.rentSchemeEntryList"
      :columns="columns"
      :pagination="false"
      :scroll="{ x: 1500, y: 'calc(100vh - 348px)' }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'name'">
          <div class="flex">
            <a-popconfirm title="是否确认移除？" @confirm="handleRemoveUnit(index)">
              <span class="text-error cursor-pointer mr-[6px]">
                <i class="a-icon-remove"></i>
              </span>
            </a-popconfirm>
            <span>{{ record.name || record.leaseUnit_dictText }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn mr-[10px]" @click="viewUnitDetail(record)">单元详情</span>
        </template>
      </template>
    </a-table>
    <f7-modal ref="f7ModalRef" f7-type="leaseUnit" multiple @update-value="updateUnitList"></f7-modal>
    <lease-unit-detail ref="leaseUnitDetailRef"></lease-unit-detail>
  </div>
</template>

<script setup>
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import { message } from 'ant-design-vue'

const { form } = defineProps({
  form: { type: Object, required: true }
})

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '原租金', dataIndex: 'originalRent', width: 200 },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]

const f7ModalRef = ref()
const handleAddUnit = () => {
  f7ModalRef.value.open([...form.rentSchemeEntryList])
}
const updateUnitList = (_, list) => {
  form.rentSchemeEntryList = list
}

const leaseUnitDetailRef = ref()
const viewUnitDetail = (data) => {
  leaseUnitDetailRef.value.open(data)
}

const handleRemoveUnit = (index) => {
  form.rentSchemeEntryList.splice(index, 1)
}

/**
 * 校验字段是否都填写正确
 * @param opeType isSave=是否暂存 如果是暂存的话，则租赁单元可以不添加
 */
const validate = (isSave = false) => {
  if (!isSave && !form.rentSchemeEntryList.length) {
    message.warning('请选择租赁单元')
    return false
  }
  return true
}

defineExpose({ validate })
</script>
