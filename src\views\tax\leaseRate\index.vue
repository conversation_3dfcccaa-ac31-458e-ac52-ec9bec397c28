<template>
  <div class="flex flex-col h-full">
    <div class="flex-1 flex content overflow-hidden">
      <div class="left flex flex-col w-[300px]">
        <div class="text-[18px] font-bold mb-[10px]">公司</div>
        <div class="flex-1 overflow-y-auto">
          <div
            v-for="company in companyList"
            :key="company.id"
            :class="['company-item', { active: company.id === activeCompany.id }]"
            @click="selectCompany(company)"
          >
            {{ company.departName }}
          </div>
        </div>
      </div>
      <div class="right flex-1 p-[16px]">
        <div class="text-[18px] font-bold mb-[24px]">
          <span>{{ leaseRate.name || '' }}</span>
          <span class="ml-[10px] primary-btn" @click="handleEdit">
            <i class="a-icon-edit"></i>
          </span>
        </div>
        <scheme v-if="leaseRate.id" :id="leaseRate.id" />
      </div>
    </div>

    <rename ref="renameRef" @refresh="loadLeaseRate"></rename>
  </div>
</template>

<script setup>
import { getCurrentUserCompanies } from '@/views/system/depart/apis'
import { page as getLeaseRate, add } from './apis'
import Scheme from './scheme/index.vue'
import Rename from './components/Rename.vue'

const companyList = ref([])
const activeCompany = reactive({})

onMounted(async () => {
  const { result } = await getCurrentUserCompanies()
  companyList.value = result.companyList || []
  if (result?.companyList?.length) {
    selectCompany(result.companyList[0])
  }
})

const selectCompany = (company) => {
  Object.assign(activeCompany, company)
  loadLeaseRate()
}

const leaseRate = reactive({})
const loadLeaseRate = async () => {
  if (!activeCompany.id) return
  const { result } = await getLeaseRate({ manageCompany: activeCompany.id, pageSize: 1 })
  if (result.records.length) {
    Object.assign(leaseRate, result.records[0])
    return
  }
  addLeaseRate()
}

const addLeaseRate = async () => {
  if (!activeCompany.id) return
  await add({ manageCompany: activeCompany.id, name: `${activeCompany.departName}从租税率方案` })
  loadLeaseRate()
}

const renameRef = ref()
const handleEdit = () => {
  renameRef.value.open(leaseRate)
}
</script>

<style lang="less" scoped>
.content {
  border-radius: 10px;
  border: 1px solid #e6e9f0;
}

.left {
  padding: 16px;
  border-right: 1px solid #e6e9f0;
  flex-shrink: 0; /* 设置为 0 防止被挤压 */
}

.right {
  overflow: hidden;
}

.company-item {
  margin-top: 16px;
  padding: 16px;
  cursor: pointer;
  color: #1d335c;
  transition: background 0.2s;
  background: #f7f8fa;
  border: 1px solid #e6e9f0;
  border-radius: 8px;
  &.active {
    background: #eaf0fe;
    border: 1px solid var(--color-primary);
  }
}
</style>
