import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/fileFillType/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/fileFillType/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/fileFillType/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/fileFillType/importExcel',
    data
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/fileFillType/updateEnableDisableStatus',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/fileFillType/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/fileFillType/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/fileFillType/deleteBatch',
    params
  })
}

// 归档类型-分录主表ID查询
export const queryTypeList = (params) => {
  return request({
    method: 'get',
    url: '/bas/fileFillType/queryFileFillTypeEntryByMainId',
    params
  })
}
