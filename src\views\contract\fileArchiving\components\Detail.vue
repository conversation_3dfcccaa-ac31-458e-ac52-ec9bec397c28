<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="合同资料归档详情"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleClose"
  >
    <template #extra>
      <a-dropdown>
        <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          <span>操作</span>
          <i class="a-icon-arrow-down ml-[4px]"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <div class="primary-btn" @click="handleAudit(true)">审核(临时功能)</div>
            </a-menu-item>
            <a-menu-item>
              <div class="primary-btn" @click="handleViewContract">查看合同</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITING'].includes(detail.status)">
              <div class="primary-btn" @click="handleWithdraw">撤回</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
              <div class="primary-btn" @click="handleEdit">编辑</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK'].includes(detail.status)">
              <div class="primary-btn" @click="handleRemove">删除</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITOK'].includes(detail.status)">
              <div class="primary-btn" @click="handleAudit(detail, false)">反审核</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">合同资料归档</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[16px]">基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
        <span class="w-[50%]">归档部门: {{ detail.fileFillDepart_dictText }}</span>
        <span class="w-[50%]">归档类型: {{ detail.fileFillType_dictText }}</span>
        <span class="w-[50%]">合同编码: {{ detail.contract_dictText }}</span>
        <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
        <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
        <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
        <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
        <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
        <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
        <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
        <span class="w-[50%]">租赁单元: {{ detail.leaseUnit_dictText }}</span>
      </div>
      <h2 class="text-[16px] font-bold mt-[40px] mb-[16px]">归档明细</h2>
      <a-table :data-source="detail.dataFileFillDetailEntryList" :columns="columns" :pagination="false" bordered>
        <template #bodyCell="{ column, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
        </template>
      </a-table>
    </a-spin>
  </a-drawer>
  <contract-detail ref="contractDetailRef" readonly></contract-detail>
</template>

<script setup>
import { detail as getDetail, deleteBatch, fileDetail, audit, unAudit } from '../apis.js'
import { queryContractLeaseUnits } from '@/views/contract/management/apis'
import { Modal, message } from 'ant-design-vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'

const emit = defineEmits(['edit', 'refresh', 'withdraw'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadData(id)
}

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([loadDetail(id), loadArchiveList(id)])
  loading.value = false
}

const detail = reactive({
  id: '',
  bizDate: '',
  contract: '',
  contractType_dictText: '',
  contract_dictText: '',
  createBy_dictText: '',
  createTime: '',
  customer_dictText: '',
  expireDate: '',
  leaseUnit_dictText: '',
  manageCompany_dictText: '',
  number: '',
  fileFillDepart_dictText: '',
  fileFillType_dictText: '',
  operator_dictText: '',
  operatorDepart_dictText: '',
  signDate: '',
  startDate: '',
  status: '',
  dataFileFillDetailEntryList: [] // 归档明细
})
const loadDetail = async (id) => {
  const { result } = await getDetail({ id })
  for (const key in detail) {
    if (key !== 'dataFileFillDetailEntryList') {
      detail[key] = result[key]
    }
  }
  const { result: list } = await queryContractLeaseUnits({ id: result.contract })
  detail.leaseUnit_dictText = list.map((item) => item.leaseUnit_dictText).join(',')
}

const loadArchiveList = async (id) => {
  const { result } = await fileDetail({ id })
  detail.dataFileFillDetailEntryList = result
}

const columns = [
  { title: '序号', dataIndex: 'index', width: 80, fixed: 'left' },
  { title: '材料类别', dataIndex: 'materialType_dictText' },
  { title: '材料类别名称', dataIndex: 'materialTypeName' },
  { title: '提交人', dataIndex: 'submitPerson_dictText' },
  { title: '接收人', dataIndex: 'receivePerson_dictText' },
  { title: '归档日期', dataIndex: 'fileFillDate' }
]

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const contractDetailRef = ref()
const handleViewContract = () => {
  contractDetailRef.value.open(detail.contract)
}

const handleWithdraw = () => {
  emit('withdraw', detail)
}

const handleAudit = async (data, result) => {
  const { message: msg } = await (result ? audit({ id: data.id }) : unAudit({ id: data.id }))
  message.success(msg)
  loadDetail(detail.id)
  emit('refresh')
}
const handleRemove = () => {
  Modal.confirm({
    title: '确认删除该合同资料归档？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await deleteBatch({ ids: detail.id })
      message.success(data.message)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
  detail.dataFileFillDetailEntryList = [] // 账单明细
}

defineExpose({ open, visible, loadData })
</script>
