/**
 * a-table分页表格
 * @param {Function} request 请求函数
 * @param {Function} dataFormatter 需要对返回回来的列表数据做什么处理
 * @param {Boolean} isPagination 是否是分页表格，只有(需要设置表格高度 && 表格不分页)的时候才需设置这个参数为false
 */
const usePageTable = (request, dataFormatter, isPagination) => {
  // 加载状态
  const tableLoading = ref(false)
  const _list = ref([])
  const _total = ref(0)
  const _current = ref(1)
  const _pageSize = ref(10)
  // 分页对象
  const pagination = computed(() => ({
    total: _total.value,
    current: _current.value,
    pageSize: _pageSize.value,
    pageSizeOptions: ['10', '20', '30', '50', '100', '200'],
    showSizeChanger: true,
    showQuickJumper: true,
    showLessItems: true,
    showTotal: (total) => `共${total}条`
  }))
  async function onTableFetch(data = {}) {
    tableLoading.value = true
    const { result } = await request.call(null, data)
    _list.value = dataFormatter ? dataFormatter(result.records) : result.records
    _total.value = result.total
    _current.value = data.pageNo
    _pageSize.value = data.pageSize
    tableLoading.value = false
  }
  async function onTableList(data = {}) {
    tableLoading.value = true
    const { result } = await request.call(null, data)
    _list.value = dataFormatter ? dataFormatter(result) : result
    tableLoading.value = false
  }

  /**
   * 在BasicLayout右侧的表格，设置其高度值，超出这个高度值，则表格自动滚动
   * 使用要求：
   * 1. 表格必须是在BasicLayout的右侧
   * 2. 表格必须是ant-design-vue的a-table
   * 3. 页面的布局必须是只有一个a-table，并且a-table是最后一个元素（即经典的，顶部是页面名称，搜索区，操作按钮区，底部是表格的这种布局）
   */
  const height = ref('50vh')

  let page // BasicLayout右侧的router-view
  let table // a-table
  let tableHead // a-table的表格头

  function getDom() {
    page = document.getElementById('basic-router-view')
    table = page.querySelector('.ant-table-wrapper')
    tableHead = table.querySelector('.ant-table-thead')
  }

  function getHeight() {
    const tableHeadHeight = tableHead.getBoundingClientRect().height // 表格头的高度
    const tableTop = table.getBoundingClientRect().top // a-table在浏览器可视区域里的top值
    const paginationHeight = isPagination === false ? 0 : 64 // 64=分页器的高度32 + 上下margin32
    // 最后减去的2px是ant-table的border
    height.value = `${document.documentElement.offsetHeight - tableTop - tableHeadHeight - paginationHeight - 32 - 2}px`
  }

  onMounted(() => {
    getDom()
    getHeight()
    window.addEventListener('resize', getHeight)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', getHeight)
  })
  return {
    tableLoading,
    pagination,
    list: _list,
    onTableFetch,
    onTableList,
    tableHeight: height
  }
}
export default usePageTable
