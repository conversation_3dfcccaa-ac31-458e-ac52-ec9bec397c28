圆状步骤条
<template>
  <div class="flex items-center justify-between" :style="{ width }">
    <div class="flex items-center flex-1 step-item" v-for="(item, index) in stepList" :key="item">
      <div class="circle-item" :class="{ active: index + 1 <= current }">{{ index + 1 }}</div>
      <span class="text-[16px] text-tertiary" :class="{ '!text-main': current >= index + 1 }">
        {{ item }}
      </span>
      <div
        class="flex-1 h-[2px] bg-[#D7DAE0] mx-[12px]"
        :class="{ '!bg-primary': current > index + 1 }"
        v-if="index < stepList.length - 1"
      ></div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  width: { type: String, default: '100%' },
  current: {
    required: true,
    type: Number,
    validator: (val) => {
      if (typeof val !== 'number') {
        throw new Error('current prop must be a number')
      }
      if (!Number.isInteger(val)) {
        throw new Error('current prop must be an integer')
      }
      if (val < 1) {
        throw new Error('current prop must start from 1')
      }
      return true
    }
  },
  stepList: { required: true, type: Array } // 数据类型: string[]
})
</script>

<style lang="less" scoped>
.circle-item {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-tertiary);
  color: var(--color-tertiary);
  margin-right: 12px;
  line-height: 1;
  transition:
    color 0.2s,
    border-color 0.2s,
    background-color 0.2s;
  &.active {
    background-color: var(--color-primary);
    color: #fff;
    border-color: var(--color-primary);
  }
}
.step-item {
  &:last-child {
    flex: 0 0 auto;
  }
}
</style>
