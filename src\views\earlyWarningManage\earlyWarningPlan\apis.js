import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/alter/alterScheme/list',
    params
  })
}
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/submit',
    data
  })
}
export const stash = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/add',
    data
  })
}
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/edit',
    data
  })
}
// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/alter/alterScheme/delete?id=${id}`
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/alter/alterScheme/deleteBatch?ids=${ids}`
  })
}
// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/alter/alterScheme/queryById?id=${id}`
  })
}
// 预警方案分录主表ID查询
export const queryAlterSchemeEntryByMainId = (id) => {
  return request({
    method: 'get',
    url: `/alter/alterScheme/queryAlterSchemeEntryByMainId?id=${id}`
  })
}
// 撤回
export const back = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/back',
    data
  })
}
// 审核
export const audit = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/audit',
    data
  })
}
// 反审核
export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/unAudit',
    data
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/alter/alterScheme/importExcel', data, controller)
}

// 校验 预警消息
export const checkWarningMessage = (data) => {
  return request({
    method: 'post',
    url: '/alter/alterScheme/sqlVerify',
    data
  })
}
