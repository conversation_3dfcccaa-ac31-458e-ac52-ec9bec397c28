import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/receiveBill/list',
    params
  })
}
export const getReceiveBillUnConsumedPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/receiveBill/unConsumedPage',
    params
  })
}
export const getReceiveBillUnConsumedF7List = (params) => {
  return request({
    method: 'get',
    url: '/biz/tripartsettle/receiveBill/unConsumedF7List',
    params
  })
}
// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/receiveBill/queryById?id=${id}`
  })
}
// 租赁应收单分录主表ID查询
export const queryReceiveBillEntryByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/receiveBill/queryReceiveBillEntryByMainId?id=${id}`
  })
}
// 提交
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/submit',
    data
  })
}

// 暂存
export const stash = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/add',
    data
  })
}
// 编辑
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/edit',
    data
  })
}
// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/receiveBill/delete?id=${id}`
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/receiveBill/deleteBatch?ids=${ids}`
  })
}
// 审核
export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/audit',
    data
  })
}
// 反审核
export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/unAudit',
    data
  })
}
// 撤回
export const back = (data) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/back',
    data
  })
}
// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/tripartsettle/receiveBill/importExcel', data, controller)
}
