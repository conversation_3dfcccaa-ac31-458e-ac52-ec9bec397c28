<template>
  <div>
    <div class="flex items-center mb-[20px]">
      <a-form layout="inline">
        <a-form-item label="物业管理公司">
          <company-select v-model="userInfo.currentCompany" type="all" disabled width="240px"></company-select>
        </a-form-item>
      </a-form>
      <a-button @click="loadData">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <search-more
        v-model="searchFilter"
        :search-list="searchList"
        btn-text="组合条件"
        placement="bottom"
        @searchChange="loadData"
      ></search-more>
    </div>
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="false"
      bordered
      size="middle"
      :row-class-name="(record) => (record[singleArrKey] === '小计' ? 'sum-row' : '')"
    >
      <template #summary>
        <a-table-summary-row>
          <a-table-summary-cell>合计</a-table-summary-cell>
          <a-table-summary-cell v-for="item in recAccMapArr" :key="item.dataIndex"></a-table-summary-cell>
          <a-table-summary-cell>
            {{ totalRow.totalBalance }}
          </a-table-summary-cell>
          <a-table-summary-cell>{{ totalRow.totalCurrentDateEnd }}</a-table-summary-cell>
          <a-table-summary-cell>
            {{ totalRow.totalNotConsumedRecAmt }}
          </a-table-summary-cell>
        </a-table-summary-row>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { renderMoney } from '@/utils/render'
import { assetVirtualAgingSummaryList } from './apis'
// import dayjs from 'dayjs'
import { useUserStore } from '@/store/modules/user'
const store = useUserStore()
const userInfo = computed(() => store.userInfo)
onMounted(() => {
  // loadData()
})
const search = ref({
  company: '37'
})
const searchFilter = ref({})
const recAccMapArr = computed(() => {
  if (searchFilter.value.recAccMap) {
    const newArr = []
    Object.values(searchFilter.value.recAccMap).forEach((item) => {
      newArr.push(
        {
          title: item.label,
          dataIndex: item.item,
          customCell: (record, index) => {
            // 如果是第一行或与上一行不同，则设置rowSpan
            if (index === 0 || record[item.item] !== tableData.value[index - 1][item.item]) {
              let rowSpan = 1
              // 计算相同值的行数
              for (let i = index + 1; i < tableData.value.length; i++) {
                if (tableData.value[i][item.item] === record[item.item]) {
                  rowSpan++
                } else {
                  break
                }
              }
              return {
                rowSpan
              }
            }
            return {
              rowSpan: 0
            }
          }
        },
        {
          title: `${item.label}编码`,
          dataIndex: `${item.item}Num`,
          customCell: (record, index) => {
            // 如果是第一行或与上一行不同，则设置rowSpan
            if (index === 0 || record[item.item] !== tableData.value[index - 1][item.item]) {
              let rowSpan = 1
              // 计算相同值的行数
              for (let i = index + 1; i < tableData.value.length; i++) {
                if (tableData.value[i][item.item] === record[item.item]) {
                  rowSpan++
                } else {
                  break
                }
              }
              return {
                rowSpan
              }
            }
            return {
              rowSpan: 0
            }
          }
        }
      )
    })
    return newArr
  }
  return []
})
// 当动态组合条件的表头数据刚好是一个的时候的key值
const singleArrKey = computed(() => {
  if (recAccMapArr.value.length === 2) {
    return recAccMapArr.value[0].dataIndex
  }
  return ''
})
const searchList = [
  {
    label: '数据来源公司',
    name: 'companySource',
    type: 'select',
    list: [
      { label: '物业管理公司', value: 'manageCompany' },
      { label: '租金归集公司', value: 'collectionCompany' }
    ]
  },
  {
    label: '账龄来源对象',
    name: 'billSource',
    type: 'select',
    list: [
      { label: '应收单', value: 'receiveBill' },
      { label: '明细账单', value: 'detailBill' }
    ]
  },
  {
    label: '应收截止日期',
    name: 'agingCompareDate',
    type: 'date',
    placeholder: '请选择应收截止日期'
  },
  {
    label: '账龄比较日期',
    name: 'receiveEndDate',
    type: 'date',
    placeholder: '请选择账龄比较日期'
  },
  {
    label: '不含押金应收',
    name: 'notIncPressPayReceive',
    type: 'check'
  },
  {
    label: '含未审核单据',
    name: 'includeNotAudit',
    type: 'check'
  },
  {
    label: '期末余额为0显示',
    name: 'endDiffZeroDisplay',
    type: 'check'
  },
  {
    label: '',
    name: 'recAccMap',
    type: 'map'
  }
]
const loading = ref(false)
const totalRow = ref({})
const loadData = async () => {
  loading.value = true
  try {
    const { result } = await assetVirtualAgingSummaryList({ ...search.value, ...searchFilter.value })
    if (result && !result.length) return
    if (singleArrKey.value) {
      tableData.value = addSubtotal(result, singleArrKey.value)
      totalRow.value.totalBalance = renderMoney(
        tableData.value
          .filter((item) => item[singleArrKey.value] !== '小计')
          .reduce((sum, item) => sum + item.balance, 0)
      )
      totalRow.value.totalCurrentDateEnd = renderMoney(
        tableData.value
          .filter((item) => item[singleArrKey.value] !== '小计')
          .reduce((sum, item) => sum + item.currentDateEnd, 0)
      )
      totalRow.value.totalNotConsumedRecAmt = renderMoney(
        tableData.value
          .filter((item) => item[singleArrKey.value] !== '小计')
          .reduce((sum, item) => sum + item.notConsumedRecAmt, 0)
      )
    } else {
      tableData.value = result
      totalRow.value.totalBalance = renderMoney(tableData.value.reduce((sum, item) => sum + item.balance, 0))
      totalRow.value.totalCurrentDateEnd = renderMoney(
        tableData.value.reduce((sum, item) => sum + item.currentDateEnd, 0)
      )
      totalRow.value.totalNotConsumedRecAmt = renderMoney(
        tableData.value.reduce((sum, item) => sum + item.notConsumedRecAmt, 0)
      )
    }
  } finally {
    loading.value = false
  }
}
// 生成小计行数据
const addSubtotal = (data, dynamicName) => {
  const result = []
  let currentName = null
  let totalBalance = 0
  let totalCurrentDateEnd = 0
  let totalNotConsumedRecAmt = 0
  for (const item of data) {
    // 如果遇到新公司，且不是第一条数据
    if (currentName !== null && currentName !== item[dynamicName]) {
      // 添加小计行
      result.push({
        [dynamicName]: '小计',
        balance: totalBalance,
        currentDateEnd: totalCurrentDateEnd,
        notConsumedRecAmt: totalNotConsumedRecAmt
      })
      // 重置小计
      totalBalance = 0
      totalCurrentDateEnd = 0
      totalNotConsumedRecAmt = 0
    }

    // 添加当前条目
    result.push(item)
    totalBalance += item.balance
    totalCurrentDateEnd += item.currentDateEnd
    totalNotConsumedRecAmt += item.notConsumedRecAmt
    currentName = item[dynamicName]
  }

  // 添加最后一个小计行
  if (currentName !== null) {
    result.push({
      [dynamicName]: '小计',
      balance: totalBalance,
      currentDateEnd: totalCurrentDateEnd,
      notConsumedRecAmt: totalNotConsumedRecAmt
    })
  }
  return result
}
const tableData = ref([])
const columns = computed(() => {
  const commonList = [
    { title: '余额', dataIndex: 'balance', customRender: ({ text }) => renderMoney(text) },
    { title: '当日到期', dataIndex: 'currentDateEnd', customRender: ({ text }) => renderMoney(text) },
    { title: '未核销收款金额', dataIndex: 'notConsumedRecAmt', customRender: ({ text }) => renderMoney(text) }
  ]
  if (searchFilter.value.recAccMap) {
    return [
      { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
      {
        title: '动态组合条件',
        children: recAccMapArr.value
      },
      ...commonList
    ]
  }
  return [
    { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
    ...commonList
  ]
})
</script>
<style lang="less" scoped>
:deep(.ant-table-wrapper) {
  .ant-table-tbody > tr.ant-table-row:hover > td {
    background: transparent;
  }
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    background: transparent;
  }
  .ant-table-tbody > tr.ant-table-row > td:hover {
    background-color: #eaf0fe;
  }
  .ant-table-tbody {
    .sum-row td {
      background-color: rgba(var(--color-primary-rgb), 0.1) !important;
    }
  }
  .ant-table-summary {
    .ant-table-cell {
      background-color: rgba(var(--color-primary-rgb), 0.1);
    }
  }
}
</style>
