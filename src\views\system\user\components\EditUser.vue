<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="drawerTitle"
    placement="right"
    width="650px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form
        :model="form"
        ref="formRef"
        :rules="rules"
        :label-col="{ style: { width: '74px' } }"
        :disabled="disabled"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="用户账号" name="username">
          <a-input
            v-model:value="form.username"
            placeholder="请输入用户账号"
            :maxlength="30"
            :disabled="Boolean(form.id)"
          />
        </a-form-item>
        <a-form-item label="用户姓名" name="realname">
          <a-input v-model:value="form.realname" placeholder="请输入用户姓名" :maxlength="10" />
        </a-form-item>
        <a-form-item label="登录密码" name="password" v-if="!form.id">
          <a-input-password v-model:value="form.password" placeholder="请输入登录密码"></a-input-password>
        </a-form-item>
        <a-form-item label="确认密码" name="confirmPassword" v-if="!form.id">
          <a-input-password v-model:value="form.confirmPassword" placeholder="请输入确认密码"></a-input-password>
        </a-form-item>
        <a-form-item label="工号" name="workNo">
          <a-input v-model:value="form.workNo" placeholder="请输入工号" :maxlength="30" />
        </a-form-item>
        <a-form-item label="角色" name="selectedRoles">
          <a-select v-model:value="form.selectedRoles" mode="multiple" placeholder="请选择角色">
            <a-select-option v-for="item in roleList" :key="item.id" :value="item.id">
              {{ item.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="所属公司" name="company">
          <company-select v-model="form.company" @change="onCompanyChange"></company-select>
        </a-form-item>
        <a-form-item label="所属部门" name="depart">
          <depart-select v-model="form.depart"></depart-select>
        </a-form-item>
        <a-form-item label="所属职务" name="post">
          <common-select
            v-model="form.post"
            :options="postList"
            mode="multiple"
            :field-names="{ label: 'name', value: 'id' }"
          ></common-select>
        </a-form-item>
        <a-form-item label="头像" name="avatar">
          <img-upload v-model="form.avatar" :disabled="disabled"></img-upload>
        </a-form-item>
        <a-form-item label="生日" name="birthday">
          <a-date-picker v-model:value="form.birthday" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>
        <a-form-item label="性别" name="sex">
          <dict-select v-model="form.sex" code="sex"></dict-select>
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="form.email" placeholder="请输入邮箱" :maxlength="200" />
        </a-form-item>
        <a-form-item label="手机号码" name="phone">
          <a-input v-model:value="form.phone" placeholder="请输入手机号码" :maxlength="11" />
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer v-if="drawerTitle !== '查看用户'">
      <a-button type="primary" :loading="confirmLoading" @click="handleConfirm">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addUser, editUser, checkField, queryUserRole } from '../apis'
import { queryAllNoByTenant } from '@/views/system/role/apis'
import { validatePassword, emailRegexp, phoneRegexp } from '@/utils/validate'
import { getDepartTreeLazy } from '@/views/system/depart/apis'
import { page } from '@/views/system/position/apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const disabled = ref(false)
const open = (data, isDisabled) => {
  visible.value = true
  disabled.value = isDisabled
  if (data) {
    Object.assign(form, {
      id: data.id,
      username: data.username,
      realname: data.realname,
      company: data.company,
      depart: data.depart,
      workNo: data.workNo,
      avatar: data.avatar || '',
      birthday: data.birthday || '',
      sex: data.sex || '',
      email: data.email,
      phone: data.phone,
      post: data.post ? data.post.split(',') : []
    })
    loadUserRoles(data.id)
    loadDeptList()
  }
}

const drawerTitle = computed(() => {
  if (disabled.value) return '查看用户'
  return form.id ? '编辑用户' : '新增用户'
})

const loading = ref(false)
const loadUserRoles = async (userId) => {
  const { result } = await queryUserRole({ userid: userId })
  form.selectedRoles = result
}

const deptList = ref([])
const loadDeptList = async () => {
  if (!form.company) {
    deptList.value = []
    return
  }
  const { result } = await getDepartTreeLazy({ pid: form.company })
  deptList.value = result
}
const onCompanyChange = (id) => {
  if (!id) {
    form.depart = ''
  }
  loadDeptList()
}

const postList = ref([])
const loadPostList = async () => {
  const { result } = await page({ pageNo: 1, pageSize: 5000 })
  postList.value = result.records
}

const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  realname: '',
  workNo: '',
  selectedRoles: [],
  post: [],
  company: '',
  depart: '',
  avatar: '',
  birthday: '',
  sex: '',
  email: '',
  phone: ''
})

const roleList = ref([])
const loadRoleList = async () => {
  const { result } = await queryAllNoByTenant()
  roleList.value = result
}

const validateUsername = async (rule, value) => {
  if (form.id) return Promise.resolve()
  if (!value) return Promise.reject('请输入用户账号')
  try {
    const { success } = await checkField({ tableName: 'sys_user', fieldName: 'username', fieldVal: value })
    return success ? Promise.resolve() : Promise.reject('用户账号已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const validateWorkNo = async (rule, value) => {
  if (!value) return Promise.reject('请输入工号')
  try {
    const { success } = await checkField({
      tableName: 'sys_user',
      fieldName: 'work_no',
      fieldVal: value,
      dataId: form.id || undefined
    })
    return success ? Promise.resolve() : Promise.reject('工号已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const validatePhone = async (rule, value) => {
  if (!value) return Promise.reject('请输入手机号码')
  if (!phoneRegexp.test(value)) return Promise.reject('请输入正确的11位手机号码')
  try {
    const { success } = await checkField({
      tableName: 'sys_user',
      fieldName: 'phone',
      fieldVal: value,
      dataId: form.id || undefined
    })
    return success ? Promise.resolve() : Promise.reject('手机号码已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const validateEmail = async (rule, value) => {
  if (!value) return Promise.resolve()
  if (!emailRegexp.test(value)) return Promise.reject('请输入正确邮箱')
  try {
    const { success } = await checkField({
      tableName: 'sys_user',
      fieldName: 'email',
      fieldVal: value,
      dataId: form.id || undefined
    })
    return success ? Promise.resolve() : Promise.reject('邮箱已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const rules = computed(() => ({
  username: [{ required: true, validator: validateUsername, trigger: 'blur' }],
  realname: [{ required: true, message: '请输入用户姓名', trigger: 'blur' }],
  company: [{ required: true, message: '请选择所属公司', trigger: 'blur' }],
  depart: [{ required: true, message: '请选择所属部门', trigger: 'blur' }],
  selectedRoles: [{ required: true, message: '请选择用户角色', trigger: 'change' }],
  workNo: [{ required: true, validator: validateWorkNo, trigger: 'blur' }],
  password: [{ required: true, validator: validatePassword('登录密码'), trigger: 'blur' }],
  confirmPassword: [{ required: true, validator: validatePassword('确认密码', form.password), trigger: 'blur' }],
  email: [{ required: false, validator: validateEmail, trigger: 'blur' }],
  phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
  post: [{ required: true, type: 'array', message: '请选择所属职务', trigger: 'change' }]
}))

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    params.selectedRoles = params.selectedRoles && params.selectedRoles.length ? params.selectedRoles.join(',') : ''
    params.post = params.post && params.post.length ? params.post.join(',') : ''
    if (form.id) {
      delete params.password
      delete params.confirmPassword
      await editUser(params)
    } else {
      await addUser(params)
    }
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.username = ''
  form.password = ''
  form.confirmPassword = ''
  form.realname = ''
  form.workNo = ''
  form.selectedRoles = []
  form.post = []
  form.company = ''
  form.depart = ''
  form.avatar = ''
  form.birthday = ''
  form.sex = ''
  form.email = ''
  form.phone = ''
  formRef.value.clearValidate()
  deptList.value = []
  visible.value = false
}

onMounted(() => {
  loadRoleList()
  loadPostList()
})

defineExpose({ open })
</script>
