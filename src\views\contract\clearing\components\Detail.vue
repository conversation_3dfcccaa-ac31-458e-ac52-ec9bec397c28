<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer contract-clearing-detail-drawer"
    title="合同退租清算详情"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleClose"
    :root-style="{ zIndex: 900 }"
  >
    <template #extra>
      <a-dropdown>
        <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          <span>操作</span>
          <i class="a-icon-arrow-down ml-[4px]"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <div class="primary-btn" @click="handleAudit(true)">审核(临时功能)</div>
            </a-menu-item>
            <a-menu-item>
              <div class="primary-btn" @click="handleViewContract">查看合同</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITING'].includes(detail.status)">
              <div class="primary-btn" @click="handleWithdraw">撤回</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
              <div class="primary-btn" @click="handleEdit">编辑</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK'].includes(detail.status)">
              <div class="primary-btn" @click="handleRemove">删除</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITOK'].includes(detail.status)">
              <div class="primary-btn" @click="handleAudit(false)">反审核</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">合同退租清算</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>单据编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[16px]">基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
        <span class="w-[50%]">经办人: {{ detail.handler_dictText }}</span>
        <span class="w-[50%]">计费截止日期: {{ detail.closingDate }}</span>
        <span class="w-[50%]">清算类型: {{ detail.clearingType_dictText }}</span>
        <span class="w-ull">备注: {{ detail.remark }}</span>
        <div class="w-full"></div>
        <span class="w-[50%]">合同编号: {{ detail.contract_dictText }}</span>
        <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
        <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
        <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
        <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
        <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
        <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
        <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
      </div>
      <div class="w-full mb-[20px]" v-if="billList.length">
        <h4 class="text-[16px] font-bold mt-[40px] mb-[16px] w-full">清算明细</h4>
        <div
          class="flex items-center justify-between h-[32px] rounded-[8px] mb-[12px] border border-solid border-primary px-[16px] text-primary"
          v-if="detail.transferDeductionReqAmount || detail.refundReqBillAmount || detail.liquidatedDamagesAmount"
        >
          <span>
            <i class="a-icon-warning"></i>
            相关款项处理申请
          </span>
          <div>
            <span v-if="detail.transferDeductionReqAmount">
              转款抵扣申请 {{ renderMoney(detail.transferDeductionReqAmount) }}
              <i class="a-icon-arrow-right"></i>
            </span>
            <span v-if="detail.refundReqBillAmount">
              退款申请 {{ renderMoney(detail.refundReqBillAmount) }}
              <i class="a-icon-arrow-right"></i>
            </span>
            <span v-if="detail.liquidatedDamagesAmount">
              违约金处置 {{ renderMoney(detail.liquidatedDamagesAmount) }}
              <i class="a-icon-arrow-right"></i>
            </span>
          </div>
        </div>
        <div class="flex gap-x-[16px]">
          <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
            <div class="detail-item-header">
              <span>应收</span>
              <strong class="clearing-money">{{ renderMoney(billList[0].receiveAmount) }}</strong>
            </div>
            <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
              <li
                class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                v-for="item in billList[0].detailBillEntryVOList"
                :key="item.id"
              >
                <div>
                  <p>{{ item.incomeBelongYm }} {{ item.paymentType_dictText }}</p>
                  <small class="text-tertiary text-[12px]">
                    {{ item.receiveBeginDate }} - {{ item.receiveEndDate }}
                  </small>
                </div>
                <div>
                  <p class="text-right">{{ renderMoney(item.paymentAmount) }}</p>
                  <small class="text-tertiary text-[12px]">
                    应收{{ renderMoney(item.actualReceiveAmount) }} | 已收{{ renderMoney(item.paid) }}
                  </small>
                </div>
              </li>
            </ul>
            <a-empty
              description="暂无应收数据"
              class="py-[20px]"
              v-show="!billList[0].detailBillEntryVOList.length"
            ></a-empty>
          </section>
          <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
            <div class="detail-item-header">
              <span>应退</span>
              <strong class="clearing-money">{{ renderMoney(billList[1].refundedAmount) }}</strong>
            </div>
            <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
              <li
                class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                v-for="item in billList[1].detailBillEntryVOList"
                :key="item.id"
              >
                <div>
                  <p>{{ item.paymentType_dictText }}</p>
                  <small class="text-tertiary text-[12px]">{{ item.number }}</small>
                </div>
                <div>
                  <p class="text-right">{{ item.actualReceiveAmount }}</p>
                  <small class="text-tertiary text-[12px]">
                    应收{{ renderMoney(item.actualReceiveAmount) }} | 已收{{ renderMoney(item.paid) }}
                  </small>
                </div>
              </li>
            </ul>
            <a-empty
              description="暂无应退数据"
              class="py-[20px]"
              v-show="!billList[1].detailBillEntryVOList.length"
            ></a-empty>
          </section>
          <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
            <div class="detail-item-header">
              <span>已收待核销</span>
              <strong class="clearing-money">{{ renderMoney(billList[2].paidWaitConsumedAmount) }}</strong>
            </div>
            <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
              <li
                class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                v-for="item in billList[2].payExplainBookVOList"
                :key="item.id"
              >
                <div>
                  <p>{{ item.receiveDate }}</p>
                  <small class="text-tertiary text-[12px]">{{ item.number }}</small>
                </div>
                <div>
                  <p class="text-right">{{ renderMoney(item.sumWaitConsumedAmt) }}</p>
                  <small class="text-tertiary text-[12px]">
                    到账{{ renderMoney(item.sumAmt) }} | 已核销{{ renderMoney(item.sumConsumedAmt) }}
                  </small>
                </div>
              </li>
            </ul>
            <a-empty
              description="暂无已收待核销数据"
              class="py-[20px]"
              v-show="!billList[2].payExplainBookVOList.length"
            ></a-empty>
          </section>
        </div>
      </div>
      <div v-if="detail.liquidatedDamagesBillAmount || detail.liquidatedDamagesAmount">
        <h2 class="text-[16px] font-bold mt-[40px] mb-[16px]">违约情况</h2>
        <div class="text-secondary">
          <span>违约金额: {{ renderMoney(detail.liquidatedDamagesBillAmount || detail.liquidatedDamagesAmount) }}</span>
          <p class="mt-[12px]">违约说明: {{ detail.liquidatedDamagesRemark }}</p>
        </div>
      </div>
      <div
        class="rounded-[8px] border-[#e6e9f0] border-solid border bg-[#f7f8fa] mt-[20px] p-[16px]"
        v-if="detail.sumResultStatus"
      >
        <div class="flex items-center mb-[8px]">
          <span>{{ detail.sumResultStatus }}:</span>
          <strong class="clearing-money">{{ renderMoney(detail.sumResultAmount) }}</strong>
        </div>
        <div class="mt-[16px] text-secondary">{{ detail.sumResultRemark }}</div>
      </div>
    </a-spin>
    <template #footer v-if="detail.status === 'AUDITOK' && detail.sumResultAmount >= 0">
      <a-button type="primary" @click="handleRefund" v-if="showRefundBtn">申请退款</a-button>
      <a-button type="primary" @click="handleTransferDeduction" v-if="showTransferDeductionBtn">申请转款抵扣</a-button>
      <a-button type="primary" @click="handleWriteOff" v-if="showWriteOffBtn">进行收款核销</a-button>
      <a-button type="primary" @click="handleBillAdjust">申请账单调整</a-button>
    </template>
    <template #footer v-else-if="detail.status === 'AUDITOK' && detail.sumResultAmount < 0">
      <a-button type="primary" @click="handleSendNotice">立即催款</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail } from '../apis.js'
import { renderMoney } from '@/utils/render'

const emit = defineEmits([
  'edit',
  'withdraw',
  'audit',
  'viewContract',
  'remove',
  'adjust',
  'refund',
  'transferDeduction',
  'writeOff'
])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const detail = reactive({
  status: '',
  sumResultAmount: 0
})
const billList = computed(() => detail.clearingDetailBillVOList || [])

// 是否显示“申请退款”按钮
const showRefundBtn = computed(() => {
  if (!billList.value.length) return false
  return Boolean(billList.value[1].detailBillEntryVOList && billList.value[1].detailBillEntryVOList.length)
})

// 是否显示“转款抵扣”按钮
const showTransferDeductionBtn = computed(() => {
  if (!billList.value.length) return false
  return (
    Boolean(billList.value[0].detailBillEntryVOList && billList.value[0].detailBillEntryVOList.length) ||
    showRefundBtn.value
  )
})

// 是否显示“收款核销”按钮
const showWriteOffBtn = computed(() => {
  if (!billList.value.length) return false
  return Boolean(billList.value[2].detailBillEntryVOList && billList.value[2].detailBillEntryVOList.length)
})

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  loading.value = false
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleViewContract = () => {
  emit('viewContract', detail)
}

// 申请退款
const handleRefund = () => {
  handleClose()
  emit('refund', detail)
}
// 申请转款抵扣
const handleTransferDeduction = () => {
  handleClose()
  emit('transferDeduction', detail)
}

// 进行收款核销
const handleWriteOff = () => {
  handleClose()
  emit('writeOff', detail)
}

// 账单调整
const handleBillAdjust = () => {
  emit('adjust', detail)
  handleClose()
}

// 立即催款
const handleSendNotice = () => {}

const handleWithdraw = () => {
  emit('withdraw', detail)
}

const handleAudit = (result) => {
  emit('audit', detail, result)
}

const handleRemove = () => {
  emit('remove', detail)
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open, loadDetail, visible, handleClose })
</script>

<style lang="less">
.contract-clearing-detail-drawer {
  .detail-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e0e0e0;
    padding: 16px;
    background-color: #f7f8fa;
    & > span {
      font-size: 16px;
    }
  }
  .clearing-money {
    color: var(--color-error);
    font-size: 16px;
    line-height: 1;
    margin-left: 10px;
    &::before {
      content: '￥';
      font-size: 12px;
    }
  }
}
</style>
