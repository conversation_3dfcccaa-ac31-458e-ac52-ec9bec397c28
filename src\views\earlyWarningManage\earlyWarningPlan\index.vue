<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button v-auth="'bas:ct_bas_house_owner:add'" class="mb-[10px]" type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button v-auth="'bas:ct_bas_house_owner:importExcel'" class="mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'bas:ct_bas_house_owner:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchChangeProject">
                <div class="primary-btn" @click="handleBatchChangeProject">维护项目</div>
              </a-menu-item>
              <a-menu-item>
                <div v-auth="'bas:ct_bas_house_owner:deleteBatch'" class="primary-btn" @click="handleRemove(false)">
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!mb-[10px] !ml-[40px]" label="">
          <s-input v-model="search.name" placeholder="搜索方案名称" class="!w-[280px]" @input="handleInput"></s-input>
        </a-form-item>
        <a-form-item>
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set class="!mb-[10px]" :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'layerNum'">
          <div>
            <span>{{ record.layerNum }}</span>
            <span v-if="record.layerNum && record.totalLayerNum">/</span>
            <span>{{ record.totalLayerNum }}</span>
          </div>
        </template>

        <!--暂存:TEMP；已撤回:BACK；审核中:AUDITING;审核不通过:AUDITNO;审核通过:AUDITOK;启用:ENABLE;禁用:DISABLE;关闭:CLOSED;意向:INTEND-->
        <template v-if="column.dataIndex === 'action'">
          <span v-auth="'bas:ct_bas_house_owner:view'" class="primary-btn" @click="rowView(record)">查看</span>
          <!--  暂存 已撤回 审核不通过 才有 -->
          <span
            v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)"
            v-auth="'bas:ct_bas_house_owner:edit'"
            class="primary-btn"
            @click="rowEdit(record)"
          >
            编辑
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <!-- 审核中才有审核操作（临时） -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:audit'" class="primary-btn" @click="rowVerify(record)">
                    审核通过(临时)
                  </div>
                </a-menu-item>
                <!-- 审核通过才能 反审核 -->
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:unAudit'" class="primary-btn" @click="rowReverse(record)">
                    反审核
                  </div>
                </a-menu-item>
                <!-- 审核中才有撤回操作 -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:edit'" class="primary-btn" @click="rowBack(record)">撤回</div>
                </a-menu-item>
                <!-- 暂存 已撤回 才有删除 -->
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div v-auth="'bas:ct_bas_house_owner:delete'" class="primary-btn" @click="handleRemove(record)">
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 详情 -->
    <detail ref="detailRef" @load-data="onTableChange"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产数据"
      :download-fn="() => exportExcel('资产数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import { renderDictTag, renderRegion } from '@/utils/render'
import usePageTable from '@/hooks/usePageTable'
import AddEdit from './components/AddEdit.vue'
import Detail from './components/Detail.vue'
import useTableSelection from '@/hooks/useTableSelection'
import { getPage, deleteBatch, exportExcel, importExcel, back, audit, unAudit } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
})

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
    item.allAddress = `${renderRegion(item.pcaCode)}${item.detailAddress}`
  })
  return list
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...search.value,
    ...searchFilter.value,
    pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(';') : ''
  })
}
const defaultColumns = [
  { title: '公司', dataIndex: 'ctrlUnit_dictText', width: 180, fixed: true },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '方案编号', dataIndex: 'number' },
  { title: '方案名称', dataIndex: 'name' },
  { title: '预警实体', dataIndex: 'alterBizBill_dictText' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建人名称', dataIndex: 'createBy_dictText' },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const search = ref({
  column: 'number',
  order: 'desc',
  name: ''
})
const searchFilter = ref({})

const searchList = reactive([
  { label: '公司', name: 'ctrlUnit', type: 'companySelect', placeholder: '请选择公司' },
  {
    label: '状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  },
  { label: '方案编号', name: 'number', type: 's-input', placeholder: '请输入方案编号' }
])

// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row.id)
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row.id)
}

// 审核操作（临时）
const rowVerify = (row) => {
  Modal.confirm({
    title: '确认审核通过？',
    content: '',
    async onOk() {
      const data = await audit({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
// 反审核
const rowReverse = (row) => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await unAudit({ id: row.id })
      message.success(data.message)
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}
// 撤回
const rowBack = (row) => {
  // if (!hasPermission('biz.funds:ct_fun_refund_req_bill:back')) return
  Modal.confirm({
    title: '确认撤回该资产？',
    content: '',
    async onOk() {
      const data = await back({ id: row.id })
      message.success(data.message)
      onTableChange()
    }
  })
}
/**
 * 批量维护项目
 */
const batchChangeProjectModalRef = ref()
const handleBatchChangeProject = () => {
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要维护项目的租赁单元')
    return
  }
  if (selectedRows.value.some((item) => item.status === 'AUDITING')) {
    message.warning('所选租赁单元中存在审核中的数据，无法进行批量维护项目')
    return
  }
  batchChangeProjectModalRef.value?.open(selectedRows.value)
}

// 批量删除
const handleRemove = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前资产？' : '确认批量删除选中资产？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('资产数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(';') : '',
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
