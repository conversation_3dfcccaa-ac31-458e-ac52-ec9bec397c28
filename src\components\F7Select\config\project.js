import { projectPage } from '@/views/projects/apis'
import { renderDictTag } from '@/utils/render'
export default {
  modalTitle: '选择项目',
  request: projectPage,
  params: {
    column: 'number',
    order: 'desc',
    id: undefined,
    company: undefined,
    name: undefined,
    number: undefined,
    miniUnitPrice: undefined,
    miniMonthlyRent: undefined,
    status: undefined,
    wyBuildingCount: undefined,
    remark: undefined,
    createBy: undefined,
    createTime: undefined,
    updateBy: undefined,
    updateTime: undefined,
    auditBy: undefined,
    auditTime: undefined,
    sourceBillId: undefined,
    sourceBillEntryId: undefined
  },
  rowKey: 'id',
  displayKey: 'name',
  keywordKey: 'name',
  keywordPlaceholder: '搜索项目名称',
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  scrollX: 900,
  columns: [
    { title: '项目名称', dataIndex: 'name', width: 200, fixed: 'left', ellipsis: true },
    { title: '项目编号', dataIndex: 'number', width: 180 },
    { title: '公司', dataIndex: 'company_dictText', ellipsis: true },
    {
      title: '启用状态',
      dataIndex: 'status',
      customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_BaseStatus', 'dot')
    },
    { title: '楼栋数', dataIndex: 'wyBuildingCount' },
    { title: '备注', dataIndex: 'remark', ellipsis: true }
  ],
  searchList: [
    { label: '项目编号', name: 'number', type: 's-input' },
    { label: '公司', name: 'company', type: 'depart-select', companyType: 'company' },
    { label: '启用状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_BaseStatus' },
    { label: '创建时间', name: 'createTime', type: 'date' },
    { label: '创建人', name: 'createBy', type: 'user-select' },
    { label: '备注', name: 'remark', type: 's-input' }
  ]
}
