<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑数据源' : '新增数据源'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '84px' } }"
      autocomplete="off"
    >
      <a-form-item label="数据源编码" name="code">
        <a-input
          v-model:value="form.code"
          placeholder="请输入数据源编码"
          :maxlength="20"
          :disabled="Boolean(form.id)"
        />
      </a-form-item>
      <a-form-item label="数据源名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入角色名称" :maxlength="30" />
      </a-form-item>
      <a-form-item label="数据库类型" name="dbType">
        <dict-select v-model="form.dbType" code="database_type" @change="onDbTypeChange"></dict-select>
      </a-form-item>
      <a-form-item label="驱动类" name="dbDriver">
        <a-input v-model:value="form.dbDriver" placeholder="请输入驱动类" :maxlength="255" />
      </a-form-item>
      <a-form-item label="数据源地址" name="dbUrl">
        <a-input v-model:value="form.dbUrl" placeholder="请输入数据源地址" :maxlength="255" />
      </a-form-item>
      <a-form-item label="用户名" name="dbUsername">
        <a-input v-model:value="form.dbUsername" placeholder="请输入用户名" :maxlength="30" />
      </a-form-item>
      <a-form-item label="密码" name="dbPassword">
        <div class="flex items-center">
          <a-input v-model:value="form.dbPassword" placeholder="请输入密码" :maxlength="20" />
          <a-button type="primary" :disabled="loading" @click="handleTest" class="ml-[16px]">测试</a-button>
        </div>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" placeholder="请输入备注" :maxlength="255" :rows="4" show-count />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { add, edit, testConnection } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  id: '',
  code: '',
  name: '',
  dbType: '',
  dbDriver: '',
  dbUrl: '',
  dbUsername: '',
  dbPassword: '',
  remark: ''
})

const rules = {
  code: [{ required: true, message: '请输入数据源编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  dbType: [{ required: true, message: '请选择数据库类型', trigger: 'change' }],
  dbDriver: [{ required: true, message: '请输入驱动类', trigger: 'blur' }],
  dbUrl: [{ required: true, message: '请输入数据源地址', trigger: 'blur' }],
  dbUsername: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  dbPassword: [{ required: true, message: '请输入用户密码', trigger: 'blur' }]
}

const loading = ref(false)
const handleTest = async () => {
  if (loading.value) return
  await formRef.value.validate()
  try {
    loading.value = true
    await testConnection(form)
    message.success('测试成功')
  } finally {
    loading.value = false
  }
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh', !form.id)
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.code = ''
  form.name = ''
  form.dbType = ''
  form.dbDriver = ''
  form.dbUrl = ''
  form.dbUsername = ''
  form.dbPassword = ''
  form.remark = ''
  formRef.value.clearValidate()
  visible.value = false
}

const onDbTypeChange = (val) => {
  Object.assign(form, dbDriverMap[val], dbUrlMap[val])
}

const dbDriverMap = {
  // MySQL 数据库
  1: { dbDriver: 'com.mysql.jdbc.Driver' },
  // MySQL5.7+ 数据库
  4: { dbDriver: 'com.mysql.cj.jdbc.Driver' },
  // Oracle
  2: { dbDriver: 'oracle.jdbc.OracleDriver' },
  // SQLServer 数据库
  3: { dbDriver: 'com.microsoft.sqlserver.jdbc.SQLServerDriver' },
  // marialDB 数据库
  5: { dbDriver: 'org.mariadb.jdbc.Driver' },
  // postgresql 数据库
  6: { dbDriver: 'org.postgresql.Driver' },
  // 达梦 数据库
  7: { dbDriver: 'dm.jdbc.driver.DmDriver' },
  // 人大金仓 数据库
  8: { dbDriver: 'com.kingbase8.Driver' },
  // 神通 数据库
  9: { dbDriver: 'com.oscar.Driver' },
  // SQLite 数据库
  10: { dbDriver: 'org.sqlite.JDBC' },
  // DB2 数据库
  11: { dbDriver: 'com.ibm.db2.jcc.DB2Driver' },
  // Hsqldb 数据库
  12: { dbDriver: 'org.hsqldb.jdbc.JDBCDriver' },
  // Derby 数据库
  13: { dbDriver: 'org.apache.derby.jdbc.ClientDriver' },
  // H2 数据库
  14: { dbDriver: 'org.h2.Driver' },
  // 其他数据库
  15: { dbDriver: '' }
}
const dbUrlMap = {
  // MySQL 数据库
  1: { dbUrl: '*******************************************************************************************' },
  // MySQL5.7+ 数据库
  4: {
    dbUrl:
      '*******************************************************************************************&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai'
  },
  // Oracle
  2: { dbUrl: '*************************************' },
  // SQLServer 数据库
  3: { dbUrl: '**************************************************************************' },
  // Mariadb 数据库
  5: { dbUrl: '*****************************************************************************' },
  // Postgresql 数据库
  6: { dbUrl: '*******************************************' },
  // 达梦 数据库
  7: {
    dbUrl:
      'jdbc:dm://127.0.0.1:5236/?jeecg-boot&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8'
  },
  // 人大金仓 数据库
  8: { dbUrl: '*******************************************' },
  // 神通 数据库
  9: { dbUrl: 'jdbc:oscar://*************:2003/jeecg-boot' },
  // SQLite 数据库
  10: { dbUrl: '*************************' },
  // DB2 数据库
  11: { dbUrl: '*************************************' },
  // Hsqldb 数据库
  12: { dbUrl: '***************************************' },
  // Derby 数据库
  13: { dbUrl: '**************************************' },
  // H2 数据库
  14: { dbUrl: 'jdbc:h2:tcp://127.0.0.1:8082/jeecg-boot' },
  // 其他数据库
  15: { dbUrl: '' }
}

defineExpose({ open })
</script>
