<template>
  <a-radio-group v-model:value="paymentType" button-style="solid" size="medium">
    <a-radio-button v-for="item in detail.billList" :key="item.paymentType" :value="item.paymentType">
      {{ item.paymentType_dictText }}
    </a-radio-button>
  </a-radio-group>
  <div class="flex gap-x-[12px] mt-[16px]">
    <div class="money-item">
      <div class="mb-[12px]">
        <span>实际应收总金额</span>
        <a-tooltip>
          <template #title>本合同全部实际应收金额</template>
          <span class="ml-[4px]"><i class="a-icon-tips cursor-pointer"></i></span>
        </a-tooltip>
      </div>
      <strong class="text-[24px]">{{ renderMoney(currentContent.actualReceiveAmount) }}</strong>
    </div>
    <div class="money-item">
      <div class="mb-[12px]">
        <span>到期应收金额</span>
        <a-tooltip>
          <template #title>已到应收日期的款项金额-减免金额</template>
          <span class="ml-[4px]"><i class="a-icon-tips cursor-pointer"></i></span>
        </a-tooltip>
      </div>
      <strong class="text-[24px]">{{ renderMoney(currentContent.dueAmount) }}</strong>
    </div>
    <div class="money-item">
      <div class="mb-[12px]">
        <span>未到期金额</span>
        <a-tooltip>
          <template #title>还未到应收日期的款项金额</template>
          <span class="ml-[4px]"><i class="a-icon-tips cursor-pointer"></i></span>
        </a-tooltip>
      </div>
      <strong class="text-[24px]">{{ renderMoney(currentContent.unDueAmount) }}</strong>
    </div>
    <div class="money-item">
      <div class="mb-[12px]">
        <span>已结算金额</span>
        <a-tooltip>
          <template #title>已交费核销的应收金额</template>
          <span class="ml-[4px]"><i class="a-icon-tips cursor-pointer"></i></span>
        </a-tooltip>
      </div>
      <strong class="text-[24px]">{{ renderMoney(currentContent.payAmount) }}</strong>
    </div>
    <div class="money-item">
      <div class="mb-[12px]">
        <span>待结算金额</span>
        <a-tooltip>
          <template #title>还未交费核销的应收金额</template>
          <span class="ml-[4px]"><i class="a-icon-tips cursor-pointer"></i></span>
        </a-tooltip>
      </div>
      <strong class="text-[24px]">{{ renderMoney(currentContent.notPayAmount) }}</strong>
    </div>
    <div class="money-item">
      <div class="mb-[12px]">
        <span>减免金额</span>
        <a-tooltip>
          <template #title>正数为减免，负数为增加</template>
          <span class="ml-[4px]"><i class="a-icon-tips cursor-pointer"></i></span>
        </a-tooltip>
      </div>
      <strong class="text-[24px]">{{ renderMoney(currentContent.remissionAmount) }}</strong>
    </div>
  </div>
  <div class="flex items-center justify-between my-[16px]">
    <div class="flex items-center">
      <a-button>
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <!-- <s-input v-model="keyword" class="ml-[16px]"></s-input> -->
    </div>
    <columns-set ref="columnsRef" :default-columns="defaultBillColumns"></columns-set>
  </div>
  <a-table
    :data-source="tableData"
    :columns="billColumns"
    :pagination="false"
    :scroll="{ x: 2400, y: '50vh' }"
  ></a-table>
</template>

<script setup>
import { renderMoney, renderBoolean } from '@/utils/render'

const { detail } = defineProps({
  detail: { required: true, type: Object }
})

const defaultBillColumns = [
  { title: '款项类型', dataIndex: 'paymentType_dictText', fixed: 'left' },
  { title: '业务状态', dataIndex: 'bizStatus' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '是否押金', dataIndex: 'isDeposit', customRender: ({ text }) => renderBoolean(text) },
  { title: '应收金额', dataIndex: 'paymentAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '减免金额', dataIndex: 'remission', customRender: ({ text }) => renderMoney(text) },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => renderMoney(text) },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => renderMoney(text) },
  { title: '实际应收金额', dataIndex: 'actualReceiveAmount', customRender: ({ text }) => renderMoney(text) },
  { title: '已转应收金额', dataIndex: 'transfered', customRender: ({ text }) => renderMoney(text) },
  { title: '未转应收金额', dataIndex: 'transferdBalance', customRender: ({ text }) => renderMoney(text) },
  { title: '已抵扣金额', dataIndex: 'transferDeduction', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '已退款金额', dataIndex: 'refunded', customRender: ({ text }) => renderMoney(text) || '-' },
  {
    title: '尾差已处理金额',
    dataIndex: 'offDifference',
    width: 140,
    customRender: ({ text }) => renderMoney(text) || '-'
  },
  { title: '可抵退转金额', dataIndex: 'balance', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '已提房产税', dataIndex: 'houseTax', customRender: ({ text }) => renderMoney(text) || '-' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' }
]
const columnsRef = ref()
const billColumns = computed(() => columnsRef.value?.columns)

const paymentType = ref('全部')
// const keyword = ref('')

const tableData = computed(() => {
  if (!(detail.billList && detail.billList.length)) return []
  const data = detail.billList.find((i) => i.paymentType === paymentType.value)
  return data ? data.contractDetailBillsList : []
})

const currentContent = computed(() => {
  if (!(detail.billList && detail.billList.length)) return {}
  const data = detail.billList.find((i) => i.paymentType === paymentType.value)
  return data || {}
})
</script>

<style lang="less" scoped>
.money-item {
  height: 86px;
  border: 1px solid #e6e9f0;
  background: #fff url('@/assets/imgs/money-item.png') right center/62px 84px no-repeat;
  padding: 12px;
  border-radius: 8px;
  flex: 1;
  color: var(--color-secondary);
  & > strong {
    line-height: 1;
  }
}
</style>
