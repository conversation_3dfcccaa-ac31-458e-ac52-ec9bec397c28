import request from '@/apis/http'
const apiObj = {
  keysSize: '/sys/actuator/redis/keysSize',
  memoryInfo: '/sys/actuator/redis/memoryInfo',
  info: '/sys/actuator/redis/info',
  metricsHistory: '/sys/actuator/redis/metrics/history'
}

/**
 * key个数
 */
export const getKeysSize = () => {
  return request({
    method: 'get',
    url: apiObj.keysSize,
    params: { isTransformResponse: false }
  })
}

/**
 * 内存信息
 */
export const getMemoryInfo = () => {
  return request({
    method: 'get',
    url: apiObj.memoryInfo,
    params: { isTransformResponse: false }
  })
}

/**
 * 详细信息
 */
export const getInfo = () => {
  return request({
    method: 'get',
    url: apiObj.info
  })
}

/**
 * 历史监控记录
 */
export const getMetricsHistory = () => {
  return request({
    method: 'get',
    url: apiObj.metricsHistory
  })
}

export const getRedisInfo = () => {
  return Promise.all([getKeysSize(), getMemoryInfo()])
}
