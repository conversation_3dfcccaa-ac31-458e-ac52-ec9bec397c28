import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/bas/customCommonFunction/list',
    params
  })
}
// 提交
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/bas/customCommonFunction/submit',
    data
  })
}
// 添加
export const stash = (data) => {
  return request({
    method: 'post',
    url: '/bas/customCommonFunction/add',
    data
  })
}
// 编辑
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/customCommonFunction/edit',
    data
  })
}
// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/bas/customCommonFunction/queryById?id=${id}`
  })
}

// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/bas/customCommonFunction/delete?id=${id}`
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/bas/customCommonFunction/deleteBatch?ids=${ids}`
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/customCommonFunction/updateEnableDisableStatus',
    data
  })
}
// 自定义常用功能用户关系表-批量添加
export const userCustomFunctionAddBatch = (data) => {
  return request({
    method: 'post',
    url: '/bas/userCustomFunction/addBatch',
    data
  })
}
// 自定义常用功能用户关系表-当前用户的关系列表
export const listByCurrentUser = () => {
  return request({
    method: 'get',
    url: '/bas/userCustomFunction/listByCurrentUser'
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/customCommonFunction/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/customCommonFunction/importExcel', data, controller)
}
