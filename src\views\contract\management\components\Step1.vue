<template>
  <div>
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '94px' } }"
      label-align="left"
      autocomplete="off"
    >
      <a-form-item label="合同编号" name="contractNumber">
        <!-- 合同变更时不可编辑 -->
        <a-input
          v-model:value="form.contractNumber"
          :maxlength="50"
          show-count
          :disabled="Boolean(form.originalContract && form.changeReason)"
        ></a-input>
      </a-form-item>
      <a-form-item label="业务日期" name="bizDate">
        <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD"></a-date-picker>
      </a-form-item>
      <a-form-item label="签约日期" name="signDate">
        <a-date-picker v-model:value="form.signDate" value-format="YYYY-MM-DD"></a-date-picker>
      </a-form-item>
      <a-form-item label="签约客户" name="customer">
        <a-form-item-rest>
          <f7-select v-model="form.customer" f7-type="customer"></f7-select>
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="合同类型" name="contractType">
        <api-select v-model="form.contractType" :async-fn="getContractType"></api-select>
      </a-form-item>
      <a-form-item label="物业管理公司" name="manageCompany">
        <company-select v-model="form.manageCompany" type="company" disabled></company-select>
      </a-form-item>
      <a-form-item label="业务员" name="operator">
        <a-form-item-rest>
          <f7-select
            v-model="form.operator"
            f7-type="user"
            :depart-id="form.operatorDepart"
            relation-depart
          ></f7-select>
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="业务部门" name="operatorDepart">
        <depart-select v-model="form.operatorDepart" @change="onDepartChange"></depart-select>
      </a-form-item>
      <a-form-item label="合同开始日期" name="startDate">
        <a-date-picker v-model:value="form.startDate" value-format="YYYY-MM-DD"></a-date-picker>
      </a-form-item>
      <a-form-item label="合同结束日期" name="expireDate">
        <a-date-picker v-model:value="form.expireDate" value-format="YYYY-MM-DD"></a-date-picker>
      </a-form-item>
      <a-form-item label="定价类型" name="pricedType">
        <dict-select v-model="form.pricedType" code="CT_BASE_ENUM_Contract_PricedType"></dict-select>
      </a-form-item>
    </a-form>
    <div class="flex items-center justify-between mt-[40px] mb-[12px]">
      <strong class="text-[16px]">租赁单元</strong>
      <a-button type="primary" size="medium" @click="handleAddUnit">
        <i class="a-icon-plus"></i>
        添加单元
      </a-button>
    </div>
    <a-table
      :data-source="form.contractLeaseUnitsList"
      :columns="columns"
      :pagination="false"
      :scroll="{ x: 1500, y: 'calc(100vh - 348px)' }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'name'">
          <div class="flex">
            <a-popconfirm title="是否确认移除？" @confirm="handleRemoveUnit(index)">
              <span class="text-error cursor-pointer mr-[6px]">
                <i class="a-icon-remove"></i>
              </span>
            </a-popconfirm>
            <span>{{ record.name || record.leaseUnit_dictText }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn mr-[10px]" @click="viewUnitDetail(record)">单元详情</span>
        </template>
      </template>
    </a-table>
    <f7-modal ref="f7ModalRef" f7-type="leaseUnit" multiple @update-value="updateUnitList"></f7-modal>
    <lease-unit-detail ref="leaseUnitDetailRef"></lease-unit-detail>
  </div>
</template>

<script setup>
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import { message } from 'ant-design-vue'
import { page } from '@/views/contract/type/apis'

const { form } = defineProps({
  form: { type: Object, required: true }
})

const getContractType = () => page({ status: 'ENABLE' })

const validateEndDate = (_, value) => {
  if (!value) return Promise.reject('请选择合同结束日期')
  if (new Date(value) < new Date(form.startDate)) {
    return Promise.reject('合同结束日期不得早于合同开始日期')
  }
  return Promise.resolve()
}
const rules = {
  contractNumber: [{ required: true, message: '请填写合同编号', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签约日期', trigger: 'change' }],
  customer: [{ required: true, message: '请选择签约客户', trigger: 'change' }],
  contractType: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  pricedType: [{ required: true, message: '请选择定价类型', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择合同开始日期', trigger: 'change' }],
  expireDate: [{ required: true, validator: validateEndDate, trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }]
}

const formRef = ref()

const clearValidate = () => {
  formRef.value.clearValidate()
}

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]

const f7ModalRef = ref()
const handleAddUnit = () => {
  f7ModalRef.value.open([...form.contractLeaseUnitsList])
}
const updateUnitList = (_, list) => {
  form.contractLeaseUnitsList = list
}

const leaseUnitDetailRef = ref()
const viewUnitDetail = (data) => {
  leaseUnitDetailRef.value.open(data)
}

const handleRemoveUnit = (index) => {
  form.contractLeaseUnitsList.splice(index, 1)
}

const validate = async () => {
  await formRef.value.validate()
  if (!form.contractLeaseUnitsList.length) {
    message.warning('请选择租赁单元')
    return Promise.reject('请选择租赁单元')
  }
  return Promise.resolve()
}

const onDepartChange = () => {
  form.operator = ''
}

defineExpose({ validate, clearValidate })
</script>

<style lang="less" scoped>
:deep(.ant-form) {
  display: flex;
  flex-wrap: wrap;
  gap: 0 40px;
  .ant-form-item {
    width: calc(50% - 20px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
}
</style>
