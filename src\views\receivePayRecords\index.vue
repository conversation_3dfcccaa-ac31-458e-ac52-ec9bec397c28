<template>
  <div>
    <a-tabs v-model:active-key="search.bizStatus" @change="onTableChange">
      <a-tab-pane key="" tab="全部"></a-tab-pane>
      <a-tab-pane key="NotConsumed,PartlyConsumed" tab="待核销"></a-tab-pane>
      <a-tab-pane key="Consumed" tab="已核销"></a-tab-pane>
    </a-tabs>

    <div class="flex justify-between !mt-[24px] !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button
          v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:add'"
          class="mb-[10px]"
          type="primary"
          @click="handleAdd"
        >
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button
          v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:importExcel'"
          class="mb-[10px]"
          @click="handleImport"
        >
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div
                  v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:deleteBatch'"
                  class="primary-btn"
                  @click="rowDel(false)"
                >
                  删除
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="">
          <s-input v-model="search.number" placeholder="搜索单据编号" class="!w-[280px]" @input="handleInput"></s-input>
        </a-form-item>
        <a-form-item>
          <search-more
            v-model="searchFilter"
            :search-list="searchList"
            @searchChange="filterSearchChange"
          ></search-more>
        </a-form-item>
      </a-form>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 1500 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:view'" class="primary-btn" @click="rowView(record)">
            详情
          </span>
          <!-- 待核销的才有核销操作 -->
          <span
            v-auth="'biz.consume:ct_con_consumed_record:add'"
            v-if="['NotConsumed', 'PartlyConsumed'].includes(record.bizStatus)"
            class="primary-btn"
            @click="rowWriteOff(record)"
          >
            核销
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div
                    v-auth="'biz.consume:ct_con_consumed_record:list'"
                    class="primary-btn"
                    @click="turnToPage(record)"
                  >
                    核销记录
                  </div>
                </a-menu-item>
                <!-- 审核中才有审核操作（临时） -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:audit'"
                    class="primary-btn"
                    @click="handleVerify(record)"
                  >
                    审核通过(临时)
                  </div>
                </a-menu-item>
                <!-- 审核通过才能 反审核 -->
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:unAudit'"
                    class="primary-btn"
                    @click="rowReverse(record)"
                  >
                    反审核
                  </div>
                </a-menu-item>
                <!-- 审核中才有撤回操作 -->
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div
                    v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:edit'"
                    class="primary-btn"
                    @click="rowBack(record)"
                  >
                    撤回
                  </div>
                </a-menu-item>
                <a-menu-item>
                  <div
                    v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)"
                    v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:edit'"
                    class="primary-btn"
                    @click="rowEdit(record)"
                  >
                    编辑
                  </div>
                </a-menu-item>
                <a-menu-item>
                  <div
                    v-if="['TEMP', 'BACK'].includes(record.status)"
                    v-auth="'biz.tripartsettle:ct_biz_pay_explain_book:delete'"
                    class="primary-btn"
                    @click="rowDel(record)"
                  >
                    删除
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @loadData="onTableChange"></add-edit>
    <!-- 详情 -->
    <detail ref="detailRef" @load-data="onTableChange"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <!-- 核销 -->
    <write-off-add-edit ref="writeOffAddEditRef"></write-off-add-edit>
  </div>
</template>
<script setup>
import WriteOffAddEdit from '@/views/writeOff/records/components/AddEdit.vue'
import Detail from './components/Detail.vue'
import AddEdit from './components/AddEdit.vue'
import { renderDict, renderDictTag, renderMoney } from '@/utils/render'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage, deleteBatch, getUnConsumedPage, audit, unAudit, back, exportExcel, importExcel } from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  // 从路由查询参数中读取客户信息
  if (route.query.customer) {
    routeParams.value.customer = route.query.customer
  }

  // 从转款抵扣列表 查看缴款单 跳转过来
  if (route.query.sourceBillId) {
    routeParams.value.sourceBillId = route.query.sourceBillId
  }
  onTableChange()
})
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value, ...routeParams.value })
}

const route = useRoute()
const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 190, fixed: true, ellipsis: true },
  {
    title: '数据状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '客户名称', dataIndex: 'customer_dictText', ellipsis: true },
  { title: '收付款公司', dataIndex: 'manageCompany_dictText', width: 200, ellipsis: true },
  // {
  //   title: '单据类型',
  //   dataIndex: 'landNature',
  //   width: 120,
  //   customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  // },
  { title: '实收金额', dataIndex: 'actualReceiveAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '手续费', dataIndex: 'serviceCharge', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '合计金额', dataIndex: 'sumAmt', customRender: ({ text }) => renderMoney(text, 2) },

  {
    title: '核销情况',
    dataIndex: 'bizStatus',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BizStatus')
  },
  {
    title: '来源',
    dataIndex: 'billSource',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BillSource')
  },
  {
    title: '经办人',
    dataIndex: 'operator_dictText'
  },
  { title: '收款日期', dataIndex: 'receiveDate', width: 120 },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
// 路由携带过来的参数
const routeParams = ref({})
const search = ref({
  column: 'number',
  order: 'desc',
  bizStatus: '',
  number: ''
})
const searchFilter = ref({})
const searchList = reactive([
  {
    label: '客户',
    name: 'customer',
    type: 'customerSelect',
    placeholder: '请选择客户'
  },
  {
    label: '收付款公司',
    name: 'manageCompany',
    type: 'companySelect',
    companyType: 'all',
    placeholder: '请选择收付款公司'
  },
  // {
  //   label: '单据类型',
  //   name: 'landNature',
  //   type: 'dic',
  //   placeholder: '请选择单据类型',
  //   code: 'CT_BAS_LandNature'
  // },
  { label: '收付金额', name: 'actualReceiveAmt', type: 'number', placeholder: '请输入收付金额' },
  { label: '手续费', name: 'serviceCharge', type: 'number', placeholder: '请输入手续费' },
  { label: '合计金额', name: 'sumAmt', type: 'number', placeholder: '请输入合计金额' },
  { label: '数据状态', name: 'status', type: 'dic', placeholder: '请选择数据状态', code: 'CT_BASE_ENUM_AuditStatus' },
  // {
  //   label: '核销情况',
  //   name: 'bizStatus',
  //   type: 'dic',
  //   placeholder: '请输入核销情况',
  //   code: 'CT_BASE_ENUM_PayExplainBook_BizStatus'
  // },
  {
    label: '来源',
    name: 'billSource',
    type: 'dic',
    placeholder: '请选择来源',
    code: 'CT_BASE_ENUM_PayExplainBook_BillSource'
  },
  {
    label: '经办人',
    name: 'operator',
    type: 'userSelect',
    placeholder: '请选择经办人'
  },
  { label: '收款日期', name: 'receiveDate', type: 'date', placeholder: '请选择收款日期' }
])
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 筛选值变化的回调
const filterSearchChange = () => {
  routeParams.value = {}
  onTableChange()
}
// 审核操作（临时）
const handleVerify = (row) => {
  Modal.confirm({
    title: '确认审核通过？',
    content: '',
    async onOk() {
      const data = await audit({ id: row.id })
      message.success(data.message)
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}
// 反审核
const rowReverse = (row) => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await unAudit({ id: row.id })
      message.success(data.message)
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}
// 撤回
const rowBack = (row) => {
  Modal.confirm({
    title: '确认撤回该缴款记录？',
    content: '',
    async onOk() {
      const data = await back({ id: row.id })
      message.success(data.message)
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = (row) => {
  addEditRef.value.open(row)
}
// 删除
const rowDel = (data) => {
  Modal.confirm({
    title: data ? '确认删除当前收付款记录？' : '确认批量删除选中收付款记录？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

// 核销
const writeOffAddEditRef = ref()
const rowWriteOff = async (row) => {
  if (row.status === 'AUDITOK' && ['NotConsumed', 'PartlyConsumed'].includes(row.bizStatus)) {
    const { result } = await getUnConsumedPage({ parent: row.id })
    writeOffAddEditRef.value.open(result.records)
    return
  }
  message.warning('只有审核通过且待核销的收付款记录才能核销')
}
// 查看核销记录
const router = useRouter()
const turnToPage = (row) => {
  return router.push({ path: '/writeOff/records', query: { id: row.id } })
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('缴款记录数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      ...routeParams.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
