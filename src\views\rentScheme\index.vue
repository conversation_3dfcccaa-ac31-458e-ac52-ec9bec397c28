<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" v-auth="'biz.leasemanage:ct_biz_rent_scheme:add'" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'biz.leasemanage:ct_biz_rent_scheme:importExcel'">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button
          :loading="exportLoading"
          @click="handleExport"
          v-auth="'biz.leasemanage:ct_biz_rent_scheme:exportXls'"
        >
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.title"
          placeholder="搜索招租方案名称"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      v-model:expanded-row-keys="expandedRowKeys"
      @change="onTableChange"
      @expand="handleExpand"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)" v-auth="'biz.leasemanage:ct_biz_rent_scheme:view'">
            查看
          </span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <div class="primary-btn" @click="handleAudit(record, true)">审核(临时功能)</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <div class="primary-btn" @click="handleRemove(record)">删除</div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITING'].includes(record.status)">
                  <div class="primary-btn" @click="handleWithdraw(record)">撤回</div>
                </a-menu-item>
                <a-menu-item v-if="record.bidResult">
                  <div class="primary-btn" @click="handleViewCustomer(record)">查看中标客户</div>
                </a-menu-item>
                <a-menu-item v-if="record.contract">
                  <div class="primary-btn" @click="handleViewContract(record)">查看中标合同</div>
                </a-menu-item>
                <a-menu-item v-if="record.bizStatus === 'Listing' && record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleSetResult(record)">招标结果</div>
                </a-menu-item>
                <a-menu-item v-if="record.bizStatus === 'FailedBidding' && record.status === 'AUDITOK'">
                  <div class="primary-btn" @click="handleRegain(record)">重新招标</div>
                </a-menu-item>
                <a-menu-item v-if="record.bizStatus === 'SuccessBidding' && !record.contract">
                  <div class="primary-btn" @click="handleCreateContract(record)">创建合同</div>
                </a-menu-item>
                <a-menu-item v-if="['AUDITOK'].includes(record.status)">
                  <div class="primary-btn" @click="handleAudit(record, false)">反审核</div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <ul class="pl-[48px]">
          <li v-for="item in record.logList" :key="item.id" class="log-li">
            <p>{{ item.name }}</p>
            <div class="flex gap-x-[40px] mt-[12px]" v-if="item.name === '创建方案'">
              <span>创建人: {{ item.createBy_dictText }}</span>
              <span>创建时间: {{ item.createTime }}</span>
            </div>
            <div class="flex gap-x-[40px] mt-[12px]" v-else-if="item.name === '中标'">
              <span>操作人: {{ item.operator_dictText }}</span>
              <span>业务日期: {{ item.bizDate }}</span>
              <span>中标结果: {{ item.customer_dictText }}</span>
            </div>
            <div class="flex gap-x-[40px] mt-[12px]" v-else-if="item.name === '流标'">
              <span>操作人: {{ item.operator_dictText }}</span>
              <span>业务日期: {{ item.bizDate }}</span>
              <span>流标说明: {{ item.remark }}</span>
            </div>
            <div class="flex gap-x-[40px] mt-[12px]" v-else-if="item.name === '重新招标'">
              <span>操作人: {{ item.createBy_dictText }}</span>
              <span>操作时间: {{ item.createTime }}</span>
            </div>
            <div class="flex gap-x-[40px] mt-[12px]" v-else>
              <span>操作人: {{ item.createBy_dictText }}</span>
              <span>变更时间: {{ item.createTime }}</span>
            </div>
            <file-list :biz-id="item.id" :show-empty="false" class="mt-[12px]"></file-list>
          </li>
        </ul>
        <a-empty description="当前招租方案暂无跟踪记录" v-show="!(record.logList && record.logList.length)">
          <template #image>
            <img src="@/assets/imgs/no-data.png" class="m-auto" />
          </template>
        </a-empty>
      </template>
    </a-table>
    <edit-rent-scheme ref="editRentSchemeRef" @refresh="refresh"></edit-rent-scheme>
    <rent-scheme-detail
      ref="rentSchemeDetailRef"
      @edit="handleEdit"
      @refresh="refreshFromDetail"
      @audit="handleAudit"
      @withdraw="handleWithdraw"
      @viewContract="handleViewContract"
      @createContract="handleCreateContract"
      @viewCustomer="handleViewCustomer"
      @setResult="handleSetResult"
      @regain="handleRegain"
    ></rent-scheme-detail>
    <set-result ref="setResultRef" @refresh="refreshByResult"></set-result>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('招租方案导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <official-customer-detail ref="customerDetailRef" readonly></official-customer-detail>
    <contract-detail ref="contractDetailRef"></contract-detail>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import {
  page,
  deleteBatch,
  exportExcel,
  importExcel,
  audit,
  unAudit,
  logList,
  withdraw,
  queryLeaseUnit,
  regain,
  queryRentSchemeCustomers
} from './apis.js'
import EditRentScheme from './components/EditRentScheme.vue'
import RentSchemeDetail from './components/RentSchemeDetail.vue'
import SetResult from './components/SetResult.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag, renderMoney } from '@/utils/render'
import OfficialCustomerDetail from '@/views/customer/manage/components/OfficialCustomerDetail.vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import { hasPermission } from '@/utils/permission'

const router = useRouter()

const params = reactive({
  column: 'number',
  order: 'desc',
  id: undefined,
  number: undefined,
  title: undefined,
  status: undefined,
  bizStatus: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  operator: undefined,
  operatorDepart: undefined,
  auditDocumentNo: undefined,
  reviewDocumentNo: undefined,
  publicStartTime: undefined,
  publicEndTime: undefined,
  rentType: undefined,
  publicDate: undefined,
  bearerObject: undefined,
  totalArea: undefined,
  referencePrice: undefined,
  limitPrice: undefined,
  price: undefined,
  rentMonths: undefined,
  priceIncrease: undefined,
  managerange: undefined,
  environmental: undefined,
  supporting: undefined,
  advantage: undefined,
  failedBiddingCount: undefined,
  chanageCount: undefined,
  redecorateReq: undefined,
  otherReq: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined
})
const searchList = [
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '操作人', name: 'operator', type: 'user-select' },
  { label: '招租总面积', name: 'totalArea', type: 'input' },
  { label: '单位租金', name: 'price', type: 'input' },
  { label: '经营范围', name: 'managerange', type: 'input' },
  { label: '租赁期限', name: 'rentMonths', type: 'input' },
  { label: '业务状态', name: 'bizStatus', type: 'dict-select', code: 'CT_BASE_ENUM_RentScheme_BizStatus' },
  { label: '方案编号', name: 'number', type: 's-input' },
  { label: '流标次数', name: 'failedBiddingCount', type: 'input' },
  { label: '变更次数', name: 'chanageCount', type: 'input' }
]

const defaultColumns = [
  { title: '招租方案名称', dataIndex: 'title', width: 200, fixed: 'left' },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '操作人', dataIndex: 'operator_dictText', width: 100 },
  { title: '招租总面积', dataIndex: 'totalArea', width: 120, customRender: ({ text }) => (text ? `${text}㎡` : '') },
  { title: '单位租金', dataIndex: 'price', width: 120, customRender: ({ text }) => renderMoney(text, 2, '元/㎡') },
  { title: '经营范围', dataIndex: 'managerange', width: 140 },
  { title: '租赁期限', dataIndex: 'rentMonths', width: 100, customRender: ({ text }) => (text ? `${text}个月` : '') },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_RentScheme_BizStatus', 'dot')
  },
  { title: '中标结果', dataIndex: 'bidResult_dictText', width: 100 },
  { title: '签约合同', dataIndex: 'contract_dictText', width: 100 },
  { title: '方案编号', dataIndex: 'number', width: 180 },
  { title: '流标次数', dataIndex: 'failedBiddingCount', width: 100 },
  { title: '变更次数', dataIndex: 'chanageCount', width: 100 },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const onTableChange = async ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  await onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
  list.value.forEach((item) => {
    if (expandedRowKeys.value.includes(item.id)) {
      loadLogList(item)
    }
  })
}

const expandedRowKeys = ref([])

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const editRentSchemeRef = ref()
const handleAdd = () => {
  editRentSchemeRef.value.open()
}
const handleEdit = (data) => {
  if (!hasPermission('biz.leasemanage:ct_biz_rent_scheme:edit')) return
  editRentSchemeRef.value.open(data.id)
}

const rentSchemeDetailRef = ref()
const handleView = (data) => {
  logList({ parent: data.id })
  rentSchemeDetailRef.value.open(data.id)
}

const handleWithdraw = (data) => {
  if (!hasPermission('biz.leasemanage:ct_biz_rent_scheme:edit')) return
  Modal.confirm({
    title: '是否确认撤回该招租方案？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await withdraw({ id: data.id })
      message.success('已撤回')
      onTableChange(pagination.value)
      if (rentSchemeDetailRef.value && rentSchemeDetailRef.value.visible) {
        rentSchemeDetailRef.value.loadData(data.id)
      }
    }
  })
}

// 流标以后重新招标
const handleRegain = (data) => {
  Modal.confirm({
    title: '是否重新招标该招租方案？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await regain({ id: data.id })
      message.success('处理成功')
      onTableChange(pagination.value)
      if (rentSchemeDetailRef.value && rentSchemeDetailRef.value.visible) {
        rentSchemeDetailRef.value.loadData(data.id)
      }
    }
  })
}

let findLoading = false
const customerDetailRef = ref()
/**
 * 查看客户详情
 * @param data data.bidResult存在时，表示“查看中标客户”  data.id存在时，表示在详情弹窗里，点击了“查看竞标客户详情”
 */
const handleViewCustomer = async (data) => {
  if (!hasPermission('bas:ct_bas_customer:view')) return
  // 从竞标客户列表中点击“查看详情”按钮，必带系统客户id
  if (data.customerId) {
    customerDetailRef.value.open({ id: data.customerId })
    return
  }
  // 从列表或详情弹窗的右上角查看中标客户，则需要发起请求
  if (findLoading) return
  try {
    findLoading = true
    const { result } = await queryRentSchemeCustomers({ id: data.id })
    const customer = result.find((i) => i.isSuccessBidding)
    if (customer.customer) {
      customerDetailRef.value.open({ id: customer.customer })
      return
    }
    showModalToCreateCustomer(customer)
  } finally {
    findLoading = false
  }
}

// 展示创建客户弹窗
const showModalToCreateCustomer = (customer) => {
  Modal.confirm({
    title: '当前中标客户不存在于系统中，是否到系统中去创建该中标客户？',
    content: '',
    okText: '去创建',
    centered: true,
    onOk: () => {
      if (!hasPermission('bas:ct_bas_customer:add')) return
      sessionStorage.setItem(
        'customerFromRentScheme',
        JSON.stringify({
          sourceBillId: customer.id,
          linkman: customer.linkman,
          linkmanPhone: customer.linkmanPhone,
          name: customer.name,
          customerType: customer.customerType,
          customerSource: 'Bid'
        })
      )
      router.push({ path: '/customer/manage' })
    }
  })
}

const contractDetailRef = ref()
const handleViewContract = (data) => {
  if (!hasPermission('biz.contractmanage:ct_con_lease:view')) return
  contractDetailRef.value.open(data.contract)
}

const handleAudit = async (record, result) => {
  if (result && !hasPermission('biz.leasemanage:ct_biz_rent_scheme:audit')) return
  if (!result && !hasPermission('biz.leasemanage:ct_biz_rent_scheme:unAudit')) return
  result ? await audit({ id: record.id }) : await unAudit({ id: record.id })
  onTableChange(pagination.value)
  if (rentSchemeDetailRef.value && rentSchemeDetailRef.value.visible) {
    rentSchemeDetailRef.value.loadData(record.id)
  }
}

const handleExpand = (expand, record) => {
  if (!expand) return
  loadLogList(record)
}

// 获取招租方案跟踪记录
const loadLogList = async (record) => {
  const { result } = await logList({ parent: record.id })
  record.logList = result.records
}

const setResultRef = ref()
const handleSetResult = (record) => {
  if (!hasPermission('biz.leasemanage:ct_biz_rent_scheme:setwin')) return
  setResultRef.value.open(record.id)
}

const handleCreateContract = async (data) => {
  if (!hasPermission('biz.contractmanage:ct_con_lease:add')) return
  if (data.loading) return
  try {
    data.loading = true
    const { result } = await queryRentSchemeCustomers({ id: data.id })
    const customer = result.find((i) => i.isSuccessBidding)
    // 先判断中标客户是否存在于系统中，没有则提示新建客户
    if (!customer.customer) {
      showModalToCreateCustomer(customer)
      data.loading = false
      return
    }
    const { result: leaseUnitList } = await queryLeaseUnit({ id: data.id })
    sessionStorage.setItem(
      'rentSchemeInfo',
      JSON.stringify({
        id: data.id,
        customer: customer.customer,
        leaseUnitList: leaseUnitList && leaseUnitList.length ? leaseUnitList : []
      })
    )
    router.push({ path: '/contract/management' })
  } finally {
    data.loading = false
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  if (!hasPermission('biz.leasemanage:ct_biz_rent_scheme:delete')) return
  Modal.confirm({
    title: '确认删除招租方案？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data.id })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (pageNo > 1 && list.value.length === 1) {
        pageNo--
      }
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const refreshByResult = (detailId) => {
  onTableChange(pagination.value)
  if (rentSchemeDetailRef.value && rentSchemeDetailRef.value.visible) {
    rentSchemeDetailRef.value.loadData(detailId)
  }
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('招租方案数据导出.xls', { ...params, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>

<style lang="less" scoped>
.log-li {
  padding-left: 16px;
  position: relative;
  padding-bottom: 10px;
  &:last-child {
    padding-bottom: 0;
    &::after {
      content: none;
    }
  }
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--color-primary);
    position: absolute;
    left: 0;
    top: 8px;
    z-index: 1;
  }
  &::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 8px;
    width: 1px;
    height: 100%;
    background-color: #e0e0e0;
  }
}
:deep(.ant-table-wrapper) {
  tr.ant-table-expanded-row > td {
    background-color: #fff;
  }
}
</style>
