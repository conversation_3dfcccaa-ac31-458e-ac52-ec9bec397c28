<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    title="租赁单元详情"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <template #extra v-if="!readonly">
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span
          class="primary-btn"
          @click="handleEdit"
          v-auth="'bas:ct_bas_lease_unit:edit'"
          v-if="['TEMP', 'BACK', 'AUDITNO', 'DISABLE'].includes(detailData.status)"
        >
          编辑
        </span>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="submit" v-if="['BACK', 'AUDITNO'].includes(detailData.status)">
                <div class="primary-btn" @click="handleSubmit">提交</div>
              </a-menu-item>
              <a-menu-item key="back" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleBack">撤回</div>
              </a-menu-item>
              <a-menu-item key="audit" v-if="detailData.status === 'AUDITING'">
                <div class="primary-btn" @click="handleAudit">审核</div>
              </a-menu-item>
              <a-menu-item key="unAudit" v-if="detailData.status === 'AUDITOK'">
                <div class="primary-btn" @click="handleUnAudit">反审核</div>
              </a-menu-item>
              <a-menu-item key="stateChange" v-if="detailData.status === 'AUDITOK'">
                <div class="primary-btn" @click="handleStateChange">业务状态变更</div>
              </a-menu-item>
              <a-menu-item key="delete" v-if="['TEMP', 'BACK', 'DISABLE'].includes(detailData.status)">
                <div class="primary-btn" @click="handleDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <span class="primary-btn">
            更多
            <i class="a-icon-arrow-down"></i>
          </span>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">{{ detailData.name }}</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_HouseOwner_AuditStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        租赁单元编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于
        {{ detailData.createTime }}
      </div>

      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #baseInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">单元名称: {{ detailData.name || '-' }}</span>
            <span class="w-[50%]">房屋产权: {{ detailData.houseOwner_dictText || '-' }}</span>
            <span class="w-[50%]">
              关联项目楼栋: {{ detailData.wyProject_dictText || '-' }}/{{ detailData.wyBuilding_dictText || '-' }}/{{
                detailData.wyFloor_dictText || '-'
              }}
            </span>
            <span class="w-[50%]">产权用途: {{ detailData.propertyUse_dictText || '-' }}</span>
            <span class="w-[50%]">
              详细地址: {{ renderRegion(detailData.pcaCode) }}{{ detailData.detailAddress || '-' }}
            </span>
            <span class="w-[50%]">单元编号: {{ detailData.number || '-' }}</span>
            <span class="w-[50%]">资产类型: {{ detailData.assetType_dictText || '-' }}</span>
            <span class="w-[50%]">资产权属公司: {{ detailData.ownerCompany_dictText || '-' }}</span>
            <span class="w-[50%]">租金归集公司: {{ detailData.collectionCompany_dictText || '-' }}</span>
            <span class="w-[50%]">物业管理公司: {{ detailData.manageCompany_dictText || '-' }}</span>
            <span class="w-[50%]">使用权类型: {{ detailData.landNature_dictText || '-' }}</span>
            <span class="w-[50%]">租赁单元类别: {{ detailData.treeId_dictText || '-' }}</span>
            <span class="w-[50%]">业务状态: {{ detailData.bizStatus_dictText || '-' }}</span>
            <span class="w-[100%] whitespace-pre-wrap">配套设施: {{ detailData.supportFacility || '-' }}</span>
            <span class="w-[100%] whitespace-pre-wrap">备注: {{ detailData.remark || '-' }}</span>
          </div>

          <div class="mt-[24px]">
            <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">租赁信息</h4>
            <div class="flex flex-wrap gap-y-[12px] text-secondary">
              <span class="w-[50%]">使用类型: {{ detailData.useType_dictText || '-' }}</span>
              <span class="w-[50%]">
                租赁面积: {{ detailData.leaseArea ? renderMoney(detailData.leaseArea, 4, 'm²') : '-' }}
              </span>
              <span class="w-[50%]">租赁用途: {{ detailData.leaseUse_dictText || '-' }}</span>
              <span class="w-[50%]">控制方式: {{ detailData.controlType || '-' }}</span>
              <span class="w-[50%]">生效日期: {{ detailData.effectDate || '-' }}</span>
              <span class="w-[50%]">到期日期: {{ detailData.expireDate || '-' }}</span>
              <span class="w-[50%]">片区管理员: {{ detailData.areaManager_dictText || '-' }}</span>
            </div>
          </div>
        </template>

        <template #buildingInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">房产类型: {{ detailData.houseType_dictText || '-' }}</span>
            <span class="w-[50%]">
              建筑面积: {{ detailData.structureArea ? renderMoney(detailData.structureArea, 4, 'm²') : '-' }}
            </span>
            <span class="w-[50%]">
              宗地面积: {{ detailData.floorArea ? renderMoney(detailData.floorArea, 4, 'm²') : '-' }}
            </span>
            <span class="w-[50%]">
              层高: {{ detailData.layerHight ? renderMoney(detailData.layerHight, 4, 'm') : '-' }}
            </span>
            <span class="w-[50%]">层数/总层数: {{ detailData.layerNum || '-' }}</span>
            <span class="w-[50%]">建成年份: {{ detailData.buildYear || '-' }}</span>
            <span class="w-[50%]">建筑结构: {{ detailData.buildStructrue_dictText || '-' }}</span>
            <span class="w-[50%]">户型: {{ detailData.houseModel || '-' }}</span>
            <span class="w-[50%]">消防等级: {{ detailData.firefightingRate_dictText || '-' }}</span>
            <span class="w-[50%]">房屋安全等级: {{ detailData.houseSafeRate_dictText || '-' }}</span>
          </div>
        </template>

        <template #taxInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">
              房产税计税原值: {{ detailData.houseTaxOrgValue ? renderMoney(detailData.houseTaxOrgValue) : '-' }}
            </span>
            <span class="w-[50%]">
              开票增值税率: {{ detailData.addTaxRate ? renderMoney(detailData.addTaxRate, 4, '%') : '-' }}
            </span>
            <span class="w-[50%]">发票地址: {{ detailData.invoiceAddress || '-' }}</span>
          </div>
        </template>

        <template #meterInfo>
          <div>
            <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">水表信息</h4>
            <a-table
              v-if="waterMeterData.length"
              :columns="waterMeterColumns"
              :data-source="waterMeterData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ record, column }">
                <template v-if="column.dataIndex === 'action'">
                  <span class="primary-btn" @click="handleWaterElectricityDetail(record)">查看</span>
                </template>
              </template>
            </a-table>
            <div v-else class="flex flex-col items-center py-[40px]">
              <img src="@/assets/imgs/no-data.png" class="w-[80px] h-[80px]" />
              <span class="text-tertiary mt-[8px]">暂无数据</span>
            </div>

            <h4 class="text-[16px] font-bold mb-[12px] mt-[24px] text-[#1d335c]">电表信息</h4>
            <a-table
              v-if="electricMeterData.length"
              :columns="electricMeterColumns"
              :data-source="electricMeterData"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ record, column }">
                <template v-if="column.dataIndex === 'action'">
                  <span class="primary-btn" @click="handleWaterElectricityDetail(record)">查看</span>
                </template>
              </template>
            </a-table>
            <div v-else class="flex flex-col items-center py-[40px]">
              <img src="@/assets/imgs/no-data.png" class="w-[80px] h-[80px]" />
              <span class="text-tertiary mt-[8px]">暂无数据</span>
            </div>
          </div>
        </template>

        <template #attachmentInfo>
          <div>
            <file-list :biz-id="detailData.id"></file-list>
          </div>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <edit-lease-unit ref="editDrawerRef" @refresh="refreshData"></edit-lease-unit>

  <edit-lease-unit-state-change ref="editStateChangeDrawerRef" @refresh="refreshData"></edit-lease-unit-state-change>

  <water-electricity-detail ref="waterElectricityDetailRef" readonly />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { renderRegion, renderMoney } from '@/utils/render'
import { hasPermission } from '@/utils/permission'
import { getFilesById } from '@/apis/common'
import { getLeaseUnitDetail, deleteLeaseUnit, audit, unAudit, back, submitLeaseUnit } from '../apis/leaseUnit'
import { queryWaterElectricityById } from '@/views/waterElectricity/manage/apis/waterElectricity'
import EditLeaseUnit from './EditLeaseUnit.vue'
import EditLeaseUnitStateChange from './EditLeaseUnitStateChange.vue'
import WaterElectricityDetail from '@/views/waterElectricity/manage/components/WaterElectricityDetail.vue'

defineProps({
  readonly: { type: Boolean, default: false }
})

const emits = defineEmits(['refresh', 'edit', 'editStatusChange'])

const visible = ref(false)
const loading = ref(false)
const editDrawerRef = ref()
const editStateChangeDrawerRef = ref()
const waterElectricityDetailRef = ref()
const currentId = ref('')
const detailData = ref({})
const electricMeterData = ref([])
const waterMeterData = ref([])

const tabList = [
  { name: 'baseInfo', title: '基础信息' },
  { name: 'buildingInfo', title: '建筑信息' },
  { name: 'taxInfo', title: '税务信息' },
  { name: 'meterInfo', title: '水电表信息' },
  { name: 'attachmentInfo', title: '附件信息' }
]

const waterMeterColumns = [
  { title: '名称', dataIndex: 'name', key: 'name', fixed: 'left', width: 200 },
  { title: '编号', dataIndex: 'number', key: 'number', width: 160 },
  { title: '类型', dataIndex: 'type_dictText', key: 'type', width: 120 },
  { title: '倍率', dataIndex: 'doubleRate', key: 'doubleRate', width: 120 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 140 }
]

const electricMeterColumns = [
  { title: '名称', dataIndex: 'name', key: 'name', fixed: 'left', width: 200 },
  { title: '编号', dataIndex: 'number', key: 'number', width: 160 },
  { title: '类型', dataIndex: 'type_dictText', key: 'type', width: 120 },
  { title: '倍率', dataIndex: 'doubleRate', key: 'doubleRate', width: 120 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 140 }
]

/**
 * 打开详情抽屉
 */
const open = async (record) => {
  if (!record || !record.id) return
  visible.value = true
  currentId.value = record.id
  loading.value = true
  try {
    await fetchDetail(record.id)
  } finally {
    loading.value = false
  }
}

/**
 * 关闭抽屉
 */
const handleCancel = () => {
  emits('refresh')
  visible.value = false
  detailData.value = {}
  currentId.value = ''
  waterMeterData.value = []
  electricMeterData.value = []
}

/**
 * 编辑租赁单元
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 加载详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  await fetchDetail(id)
  loading.value = false
}

/**
 * 删除租赁单元
 */
const handleDelete = () => {
  if (!hasPermission('bas:ct_bas_lease_unit:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除租赁单元"${detailData.value.name || detailData.value.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnit({ id: currentId.value })
      message.success('删除成功')
      visible.value = false
      emits('refresh')
    }
  })
}

/**
 * 刷新当前详情数据
 */
const refreshData = async () => {
  await loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 获取详情数据
 */
const fetchDetail = async (id) => {
  loading.value = true
  const res = await getLeaseUnitDetail({ id })
  if (res.success) {
    detailData.value = res.result || {}
    // 获取附件信息
    await fetchAttachmentIds(id)
    // 获取水电表详情数据
    await fetchWaterElectricityDetails()
  } else {
    message.error(res.message || '获取详情失败')
  }
  loading.value = false
}

/**
 * 获取附件ID列表
 */
const fetchAttachmentIds = async (id) => {
  try {
    const fileList = await getFilesById(id)
    // 提取附件ID列表
    detailData.value.attachmentIds = fileList.map((file) => file.id).join(',')
  } catch {
    detailData.value.attachmentIds = undefined
  }
}

/**
 * 获取水电表详情数据
 */
const fetchWaterElectricityDetails = async () => {
  if (!detailData.value.waterShareFormulas || !detailData.value.waterShareFormulas.length) {
    waterMeterData.value = []
    electricMeterData.value = []
    return
  }

  try {
    const promises = detailData.value.waterShareFormulas.map((item) =>
      queryWaterElectricityById({ id: item.waterEleTableNum })
    )

    const results = await Promise.all(promises)

    const waterMeters = []
    const electricMeters = []

    results.forEach((res) => {
      if (res.success && res.result) {
        const meterDetail = res.result
        if (meterDetail.type === 'Water') {
          // 水表
          waterMeters.push(meterDetail)
        } else if (meterDetail.type === 'electricity') {
          // 电表
          electricMeters.push(meterDetail)
        }
      }
    })

    waterMeterData.value = waterMeters
    electricMeterData.value = electricMeters
  } catch {
    waterMeterData.value = []
    electricMeterData.value = []
  }
}

/**
 * 业务状态变更
 */
const handleStateChange = () => {
  editStateChangeDrawerRef.value.open([detailData.value])
}

/**
 * 审核操作
 */
const handleAudit = () => {
  if (!hasPermission('bas:ct_bas_lease_unit:audit')) return
  Modal.confirm({
    title: '确认审核',
    content: `确定要对租赁单元"${detailData.value.name || detailData.value.number}"执行审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await audit({ id: currentId.value })
      message.success('审核成功')
      await fetchDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 反审核操作
 */
const handleUnAudit = () => {
  if (!hasPermission('bas:ct_bas_lease_unit:unAudit')) return
  Modal.confirm({
    title: '确认反审核',
    content: `确定要对租赁单元"${detailData.value.name || detailData.value.number}"执行反审核操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await unAudit({ id: currentId.value })
      message.success('反审核成功')
      await fetchDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 提交租赁单元
 */
const handleSubmit = () => {
  if (!hasPermission('bas:ct_bas_lease_unit:submit')) return
  Modal.confirm({
    title: '确认提交',
    content: `确定要提交租赁单元"${detailData.value.name || detailData.value.number}"审核吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await submitLeaseUnit(detailData.value)
      message.success('提交成功')
      await fetchDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 撤回租赁单元
 */
const handleBack = () => {
  if (!hasPermission('bas:ct_bas_lease_unit:edit')) return
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回租赁单元"${detailData.value.name || detailData.value.number}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await back({ id: currentId.value })
      message.success('撤回成功')
      await fetchDetail(currentId.value)
      emits('refresh')
    }
  })
}

/**
 * 查看水电表详情
 */
const handleWaterElectricityDetail = (record) => {
  waterElectricityDetailRef.value.open(record)
}

defineExpose({
  open
})
</script>
