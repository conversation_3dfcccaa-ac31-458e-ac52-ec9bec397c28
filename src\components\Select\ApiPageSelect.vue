api-select: 需要调用接口获取分页列表数据的选择器
<template>
  <a-select
    v-bind="$attrs"
    ref="selectRef"
    show-search
    :filter-option="false"
    :value="modelValue || undefined"
    :style="{ width }"
    :field-names="fieldNames"
    allow-clear
    :loading="loading"
    @popupScroll="handlePopupScroll"
    @change="onchange"
    @search="handleSearch"
    :options="options"
  ></a-select>
</template>

<script setup>
const { modelValue, asyncFn, fieldNames } = defineProps({
  width: { type: String, default: '100%' },
  modelValue: { type: [Array, String] },
  fieldNames: { type: Object, default: () => ({ label: 'name', value: 'id' }) },
  /**
   * 注意：如果请求需要传参的话，则在父组件，:async-fn="() => getList({ pageSize: 10000 })"
   * 不能写成:async-fn="getList({ pageSize: 10000 })"
   * 这样的话接收到的async-fn就不是一个函数，而是一个Promise对象，不符合传参要求
   * 如果你的请求不需要传参的话，才可以直接写成:async-fn="getList"这样子
   * 但是，尽量不要使用匿名函数，不然的话，每次组件更新的时候都会重新请求一次，会带来性能消耗
   * :async-fn="() => getList({ pageSize: 10000 })"应该写成
   * fn = () => getList({ pageSize: 10000 })
   * :async-fn="fn"
   */
  asyncFn: {
    required: true,
    type: Function,
    validator: (val) => {
      if (typeof val === 'function') return true
      throw new Error('请确认你传入的是一个函数，而不是一个Promise对象')
    }
  }
})

const emit = defineEmits(['update:modelValue', 'change'])
const onchange = (val, option) => {
  emit('update:modelValue', val || '', option)
  emit('change', val || '', option)
}
const selectRef = ref(null)
const isComposing = ref(false)
onMounted(() => {
  const input = selectRef.value?.$el.querySelector('input')
  if (input) {
    input.addEventListener('compositionstart', () => {
      isComposing.value = true
    })
    input.addEventListener('compositionend', () => {
      isComposing.value = false
      handleSearch(input.value)
    })
  }
})
const handleSearch = (e) => {
  if (isComposing.value) return
  options.value = []
  requestEnd.value = false
  params.value.pageNo = 1
  params.value.name = e ? `*${e}*` : undefined

  getOptions(asyncFn)
}
const handlePopupScroll = (e) => {
  const { target } = e
  // 判断是否滚动到底部
  if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
    if (requestEnd.value) return
    params.value.pageNo++
    getOptions(asyncFn)
  }
}
const options = ref([])
const params = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined
})
const loading = ref(false)
const requestEnd = ref(false)
// 获取分页下拉数据
const getOptions = async (asyncFn) => {
  loading.value = true
  try {
    const { result } = await asyncFn(params.value)
    if (result && result.records) {
      // 请求完最后一页数据
      if (result.records.length < 10) {
        requestEnd.value = true
      }
      options.value.push(...result.records)
    }
  } finally {
    loading.value = false
  }
}

watch(
  // 当异步函数发生变化，重新执行异步函数，以获取最新数据
  () => asyncFn,
  (fn) => {
    getOptions(fn)
  }
)

onMounted(() => {
  getOptions(asyncFn)
})

defineExpose({
  // 在父组件传递过来的异步函数没有发生变化的情况下，手动提供给父组件，重新执行异步函数的方法
  executeAsyncFn: () => {
    getOptions(asyncFn)
  }
})
</script>
