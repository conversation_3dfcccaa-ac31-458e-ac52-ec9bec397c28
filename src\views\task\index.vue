<template>
  <div class="!p-[0px] task">
    <div class="content">
      <div class="flex items-center justify-between mb-[28px]">
        <h2 class="text-[#1D335C] text-[16px] font-bold flex items-center">
          <img src="@/assets/svgs/task.svg" class="w-[24px] h-[24px]" />
          <span class="ml-[8px]">审批中心</span>
        </h2>
        <span class="manage-btn flex items-center">
          <img src="@/assets/svgs/task-btn.svg" class="w-[16px] h-[16px]" />
          <span class="ml-[8px]">审批管理</span>
        </span>
      </div>

      <div class="flex items-center mb-[18px]">
        <a-radio-group class="text-[14px] text-center" v-model:value="activeType">
          <a-radio-button class="w-[108px]" value="todo">待办</a-radio-button>
          <a-radio-button class="w-[108px]" value="finished">已办</a-radio-button>
          <a-radio-button class="w-[108px]" value="myProcess">已发起</a-radio-button>
        </a-radio-group>
        <s-input
          v-model:value="searchParams.name"
          placeholder="搜索名称"
          class="ml-[32px] !w-[280px]"
          @change="loadTaskList"
        />
        <span class="ml-[16px] mr-[9px] text-[14px] text-[#1D335C]">审批类型</span>

        <dict-select
          v-model:value="searchParams.category"
          code="bpm_process_type"
          placeholder="请选择审批类型"
          class="!w-[280px]"
          @change="loadTaskList"
        />
        <span class="ml-[16px] mr-[9px] text-[14px] text-[#1D335C]">提交时间</span>
        <a-range-picker
          v-model:value="searchParams.submitTime"
          class="!w-[280px]"
          format="YYYY-MM-DD"
          @change="loadTaskList"
        />
      </div>

      <div class="flex">
        <div class="w-[332px] mr-[16px]">
          <div v-if="taskList.length === 0" class="text-center py-[32px] text-[#8992a3]">暂无数据</div>
          <div
            v-else
            v-for="task in taskList"
            :key="task.key"
            class="task-item"
            :class="{ active: selectedTask === task.key }"
            @click="handleSelectTask(task.key)"
          >
            <div class="flex items-center justify-between mb-[16px]">
              <span class="text-[16px] font-bold text-[#1D335C]">{{ task.title }}</span>
              <status-tag :dict-value="task.status" dict-code="CT_BASE_ENUM_Customer_CustomerStatus"></status-tag>
            </div>
            <div class="text-[14px] text-[#495a7a] mb-[12px]">{{ task.description }}</div>
            <div class="flex justify-between items-center text-[12px] text-tertiary">
              <div class="flex items-center">
                <img :src="task.avatar" class="w-[20px] h-[20px] rounded-[50%]" />
                <span class="ml-[4px]">审批人</span>
              </div>
              <div>提交于：{{ task.updateTime }}</div>
            </div>
          </div>
        </div>

        <!-- 详情 -->
        <div v-if="selectedTask" class="flex-1 p-[16px] border border-[#e6e9f0] rounded-[8px]">
          <h2 class="text-[18px] text-[#1d335c] font-bold mb-[12px]">合同审批</h2>
          <div
            class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[24px] text-[14px]"
          >
            编号：
          </div>
          <anchor-tabs :tab-list="tabList" height="calc(100vh - 460px)">
            <!-- 审批信息 -->
            <template #basic>
              <contract-info :info="currentApproval" :lease-units="leaseUnits" />
            </template>

            <!-- 审批流程 -->
            <template #process>
              <div class="bg-white rounded-[8px]">
                <div class="steps-container">
                  <div
                    v-for="(step, index) in approvalSteps"
                    :key="index"
                    class="step-item"
                    :class="{ completed: step.status === 'completed', processing: step.status === 'processing' }"
                  >
                    <div class="step-content">
                      <div class="step-icon">
                        <img v-if="step.status === 'processing'" src="@/assets/svgs/task-process.svg" />
                        <i v-else class="a-icon-dagou"></i>
                      </div>
                      <div class="step-info">
                        <div class="flex items-center mb-[12px]">
                          <div class="step-title">{{ step.title }}</div>
                          <div class="step-time">{{ step.time }}</div>
                        </div>
                        <div class="step-detail">
                          <div class="flex items-center">
                            <img :src="step.avatar" class="w-[32px] h-[32px] rounded-full mr-[8px]" />
                            <span class="text-[14px] text-[#1d335c]">{{ step.operator }}</span>
                          </div>
                          <div v-if="step.comment" class="step-comment">
                            {{ step.comment }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 当前用户审批操作区域 -->
                <div class="mt-[24px] pt-[24px] border-t border-[#e6e9f0]">
                  <a-button type="primary" @click="handleApprove" :loading="approving">同意</a-button>
                  <a-button danger @click="handleReject" :loading="rejecting">拒绝</a-button>
                </div>
              </div>
            </template>
          </anchor-tabs>
        </div>
        <div v-else class="flex-1 flex items-center justify-center text-[#8992a3]">请选择左侧任务查看详情</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ContractInfo from './components/ContractInfo.vue'
import { todoList, finishedList, myProcessList } from './apis'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'

/**
 * 当前选中的审批类型
 */
const activeType = ref('todo')

/**
 * 搜索参数
 */
const searchParams = reactive({
  column: 'createTime',
  order: 'desc',
  name: undefined,
  category: undefined, // 审批类型
  createTime: undefined, // 开始时间
  finishTime: undefined, // 结束时间
  pageNo: 1,
  pageSize: 10
})

/**
 * 审批意见
 */
const approvalComment = ref('')

/**
 * 审批中状态
 */
const approving = ref(false)

/**
 * 驳回中状态
 */
const rejecting = ref(false)

/**
 * 当前选中的审批项
 */
const selectedTask = ref(null)

const tabList = [
  { title: '审批信息', name: 'basic' },
  { title: '审批进程', name: 'process' }
]

/**
 * 任务列表数据
 */
const taskList = ref([])

/**
 * 当前审批详情
 */
const currentApproval = reactive({
  code: '20250403888',
  subject: '建立文关于2025-04-03 09:52',
  createTime: '2025-04-03 09:52',
  customer: '杭州庄橙网络科技有限公司',
  phone: '18205951829',
  period: '12月',
  customerType: '自然人',
  intentDate: '2025-04-03',
  prepayDate: '2025-04-03',
  monthlyRent: '6,600.00',
  description:
    '不知道客户会少多少钱是理解不了一件事，APP二过去了手机，用过人以，就试过告诉过之后做，即买那买的现实的，却你看那重劣了一期的制理由上班'
})

/**
 * 租赁单元数据
 */
const leaseUnits = ref([
  {
    key: '1',
    name: '红工大厦2301',
    usage: '商用',
    area: '58m²',
    areaDetail: '60m²',
    weight: '3,600.00',
    rent: '10,800.00',
    company: '厦门开丰商务房地产公司',
    innerCompany: '厦门开丰商务房地产公司',
    account: '李芮茵',
    contact: ''
  },
  {
    key: '2',
    name: '红工大厦2302',
    usage: '商用',
    area: '58m²',
    areaDetail: '60m²',
    weight: '3,600.00',
    rent: '10,800.00',
    company: '厦门开丰商务房地产公司',
    innerCompany: '厦门开丰商务房地产公司',
    account: '李芮茵',
    contact: ''
  }
])

/**
 * 审批流程步骤
 */
const approvalSteps = ref([
  {
    title: '提交审批',
    time: '2025-04-03 08:32:42',
    operator: '林大宇',
    avatar: '/src/assets/imgs/avatar.png',
    status: 'completed'
  },
  {
    title: '审批通过',
    time: '2025-04-03 08:32:42',
    operator: '林大宇',
    avatar: '/src/assets/imgs/avatar.png',
    comment: '原条件必送要提高，新工要确认上班具体决定资，一步才目标',
    status: 'completed'
  },
  {
    title: '审批中',
    time: '2025-04-03 08:44:36',
    operator: '黄天宇',
    avatar: '/src/assets/imgs/avatar.png',
    comment: '原条件必送要提高，新工要确认上班具体决定资，一步才目标',
    status: 'processing'
  }
])

/**
 * 加载任务列表
 */
const loadTaskList = async () => {
  try {
    const params = {
      ...searchParams,
      title: searchParams.name,
      createTime: searchParams.submitTime?.[0]
        ? dayjs(searchParams.submitTime[0]).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      finishTime: searchParams.submitTime?.[1]
        ? dayjs(searchParams.submitTime[1]).format('YYYY-MM-DD HH:mm:ss')
        : undefined
    }

    let res
    switch (activeType.value) {
      case 'todo':
        res = await todoList(params)
        break
      case 'finished':
        res = await finishedList(params)
        break
      case 'myProcess':
        res = await myProcessList(params)
        break
    }

    if (res?.data?.list) {
      taskList.value = res.data.list.map((item) => ({
        key: item.taskId,
        title: item.taskName,
        description: `流程：${item.procDefName}`,
        updateTime: item.createTime,
        avatar: item.assigneeAvatar || '/src/assets/imgs/avatar.png'
      }))

      // 如果当前选中项不在列表中，清空选中
      if (!taskList.value.find((item) => item.key === selectedTask.value)) {
        selectedTask.value = null
      }
    }
  } catch {
    taskList.value = []
    selectedTask.value = null
    message.error('加载任务列表失败，请稍后重试')
  }
}

/**
 * 监听筛选条件变化
 */
watch(
  [() => activeType.value, () => searchParams],
  () => {
    loadTaskList()
  },
  { deep: true }
)

// 初始加载
onMounted(() => {
  loadTaskList()
})

/**
 * 处理审批通过
 */
const handleApprove = () => {
  if (!approvalComment.value.trim()) {
    // 这里可以添加提示用户输入审批意见的逻辑
    return
  }

  try {
    approving.value = true
    // 这里添加审批通过的 API 调用
    // await approveAPI({ comment: approvalComment.value, action: 'approve' })
    console.log('审批通过:', approvalComment.value)
  } finally {
    approving.value = false
  }
}

/**
 * 处理审批驳回
 */
const handleReject = () => {
  if (!approvalComment.value.trim()) {
    // 这里可以添加提示用户输入审批意见的逻辑
    return
  }

  try {
    rejecting.value = true
    // 这里添加审批驳回的 API 调用
    // await rejectAPI({ comment: approvalComment.value, action: 'reject' })
    console.log('审批驳回:', approvalComment.value)
  } finally {
    rejecting.value = false
  }
}

/**
 * 处理选择审批项
 * @param {string} key - 审批项的唯一标识
 */
const handleSelectTask = (key) => {
  selectedTask.value = key
  // 清空之前的审批意见
  approvalComment.value = ''
}
</script>

<style lang="less" scoped>
.task {
  background: rgba(255, 255, 255, 0.56);
}

.manage-btn {
  border: 1px solid #165dff;
  border-radius: 8px;
  cursor: pointer;
  padding: 8px 16px;
  color: #165dff;
  font-size: 14px;
  font-weight: 400;
}

.content {
  max-width: 1680px;
  margin: 0 auto;
  padding: 40px 0;
}

.task-item {
  width: 100%;
  border: 1px solid #e6e9f0;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 15px;
  cursor: pointer;
  &.active {
    border: 1px solid #165dff;
    background: #eaf0fe;
  }
}

.steps-container {
  padding: 8px 0;

  .step-item {
    position: relative;
    padding-left: 40px;
    padding-bottom: 16px;

    &:last-child {
      padding-bottom: 0;
    }

    &::before {
      content: '';
      position: absolute;
      left: 9px;
      top: 30px;
      width: 1px;
      height: calc(100% - 24px);
      background-color: #bcc1cc;
    }

    &:last-child::before {
      display: none;
    }

    .step-content {
      position: relative;
    }

    .step-icon {
      position: absolute;
      left: -40px;
      top: 4px;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }

    .step-title {
      font-size: 14px;
      font-weight: 500;
      padding: 4px;
      border-radius: 8px;
      color: #6ec21b;
      background-color: #edfbe2;
    }

    .step-time {
      font-size: 14px;
      color: #8992a3;
      margin-left: 12px;
    }

    .step-comment {
      font-size: 12px;
      color: #8992a3;
      background-color: #f7f8fa;
      padding: 8px;
      border-radius: 8px;
      display: inline-block;
      margin-left: 40px;
    }

    &.completed {
      .step-icon {
        background-color: #6ec21b;
        color: #fff;
      }
    }

    &.processing {
      .step-icon {
        background: transparent;
      }
      .step-title {
        color: #165dff;
        background-color: #eaf0fe;
      }
    }
  }
}
</style>
