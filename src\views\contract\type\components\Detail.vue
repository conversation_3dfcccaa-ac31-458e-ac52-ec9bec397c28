<template>
  <a-drawer
    v-model:open="visible"
    title="合同类型详情"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
        <span class="primary-btn" @click="handleEdit">编辑</span>
        <span class="primary-btn" @click="handleUpdateStatus">
          {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
        </span>
        <span class="primary-btn" @click="handleDelete">删除</span>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
      <div class="text-secondary flex flex-wrap gap-y-[12px]">
        <span class="w-1/2">名称: {{ detail.name }}</span>
        <span class="w-1/2">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-1/2">合同类别: {{ detail.contractCategory_dictText }}</span>
        <span class="w-1/2">款项性质: {{ detail.paymentNature_dictText }}</span>
        <span class="w-1/2">
          状态:
          <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status" type="dot"></status-tag>
        </span>
        <span class="w-1/2">
          纳入计提印花税: {{ detail.isIncludeJtStampTax ? '是' : detail.isIncludeJtStampTax === false ? '否' : '' }}
        </span>
        <span class="w-1/2">
          保留小数: {{ detail.isKeepDecimals ? '是' : detail.isKeepDecimals === false ? '否' : '' }}
        </span>
        <span class="w-1/2">自动清算: {{ detail.isAutoClear ? '是' : detail.isAutoClear === false ? '否' : '' }}</span>
        <span class="w-full">备注: {{ detail.remark }}</span>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, updateStatus, deleteBatch } from '../apis.js'
import { Modal, message } from 'ant-design-vue'

const emit = defineEmits(['edit', 'refresh'])

const visible = ref(false)
const open = (id) => {
  visible.value = true
  loadDetail(id)
}

const loading = ref(false)
const detail = reactive({
  id: '',
  name: '',
  number: '',
  manageCompany_dictText: '',
  contractCategory_dictText: '',
  paymentNature_dictText: '',
  isKeepDecimals: undefined,
  isIncludeJtStampTax: undefined,
  isAutoClear: undefined,
  status: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  loading.value = false
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleUpdateStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该合同类型？`,
    content: detail.status === 'ENABLE' ? '禁用后将无法再被使用，但不影响已创建的数据。' : '',
    centered: true,
    onOk: async () => {
      const data = await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(data.message)
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该合同类型？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await deleteBatch({ ids: detail.id })
      message.success(data.message)
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>
