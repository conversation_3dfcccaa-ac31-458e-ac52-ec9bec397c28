import { getReceiveBillUnConsumedF7List } from '@/views/statement/receiveCertificate/apis'
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { f7List } from '@/views/paymentType/apis'
const getFundTypeList = () => f7List({ pageNo: 1, pageSize: 10000 })
const loadLeaseUnit = () => getLeaseUnitList({ pageNo: 1, pageSize: 50000 })
export default {
  modalTitle: '选择应收单明细',
  request: getReceiveBillUnConsumedF7List,
  params: {
    column: 'number',
    order: 'desc',
    billType: undefined,
    receiveDate: undefined,
    customer: undefined,
    collectionCompany: undefined,
    leaseUnit: undefined,
    contractNum: undefined,
    operatorDepart: undefined,
    operator: undefined,
    serviceCenter: undefined,
    park: undefined,
    carportNum: undefined,
    receiveAmt: undefined,
    consumedAmt: undefined,
    notConsumedAmt: undefined,
    transferBalance: undefined
  },
  rowKey: 'id',
  displayKey: 'number',
  keywordKey: 'number',
  keywordPlaceholder: '搜索单据编号',
  scrollX: 2500,
  // filterWidth: '100px',
  filterLabelWidth: '100px',
  clearIgnoreKeys: ['status'],
  statIgnoreKeys: ['status'],
  columns: [
    { title: '单据类型', dataIndex: 'billType', width: 150, fixed: true },
    { title: '款项类型', dataIndex: 'paymentTypeName' },
    { title: '开票类型', dataIndex: 'invoiceType_dictText' },
    { title: '应收日期', dataIndex: 'receiveDate' },
    { title: '应收开始日期', dataIndex: 'receiveBeginDate' },
    { title: '应收结束日期', dataIndex: 'receiveEndDate' },
    { title: '单据编号', dataIndex: 'number', width: 180 },
    { title: '客户', dataIndex: 'customerName', ellipsis: true },
    { title: '归集公司', dataIndex: 'collectionCompanyName', ellipsis: true },
    { title: '款项类型', dataIndex: 'paymentTypeName', ellipsis: true },
    { title: '租赁单元', dataIndex: 'leaseUnitName', ellipsis: true },
    { title: '合同编号', dataIndex: 'contractNum' },
    { title: '业务部门', dataIndex: 'operatorDepartName', ellipsis: true },
    { title: '业务员', dataIndex: 'operatorName' },
    { title: '服务处', dataIndex: 'serviceCenter' },
    { title: '停车场', dataIndex: 'park' },
    { title: '车位号', dataIndex: 'carportNum' },
    { title: '应收金额', dataIndex: 'receiveAmt' },
    { title: '已核销金额', dataIndex: 'consumedAmt' },
    { title: '未核销金额', dataIndex: 'notConsumedAmt' },
    { title: '未转金额', dataIndex: 'transferBalance' }
  ],
  searchList: [
    { label: '单据类型', name: 'billType', type: 'dict-select', code: 'CT_BASE_ENUM_DetailBill_BillType' },
    { label: '款项类型', name: 'paymentType', type: 'api-select', asyncFn: getFundTypeList },
    { label: '开票类型', name: 'invoiceType', type: 'dict-select', code: 'CT_BASE_ENUM_Customer_InvoiceType' },
    { label: '应收日期', name: 'receiveDate', type: 'date' },
    { label: '应收开始日期', name: 'receiveBeginDate', type: 'date' },
    { label: '应收结束日期', name: 'receiveEndDate', type: 'date' },
    { label: '客户', name: 'customer', type: 'customer-select' },
    { label: '归集公司', name: 'collectionCompany', type: 'company-select', companyType: 'all' },
    { label: '租赁单元', name: 'leaseUnit', type: 'api-select', asyncFn: loadLeaseUnit },
    { label: '合同编号', name: 'contractNum', type: 'input' },
    { label: '业务部门', name: 'operatorDepart', type: 'depart-select' },
    { label: '业务员', name: 'operator', type: 'user-select' },
    { label: '服务处', name: 'serviceCenter', type: 'input' },
    { label: '停车场', name: 'park', type: 'input' },
    { label: '车位号', name: 'carportNum', type: 'input' },
    { label: '应收金额', name: 'receiveAmt', type: 'input' },
    { label: '已核销金额', name: 'consumedAmt', type: 'input' },
    { label: '未核销金额', name: 'notConsumedAmt', type: 'input' },
    { label: '未转金额', name: 'transferBalance', type: 'input' }
  ]
}
