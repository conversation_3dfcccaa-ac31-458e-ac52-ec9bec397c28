import request from '@/apis/http'

// 获取待办列表
export const todoList = (params) => {
  return request({
    method: 'get',
    url: '/flowable/task/todoList',
    params
  })
}

// 获取已办任务
export const finishedList = (params) => {
  return request({
    method: 'get',
    url: '/flowable/task/finishedList',
    params
  })
}

// 获取我发起的流程
export const myProcessList = (params) => {
  return request({
    method: 'get',
    url: '/flowable/task/myProcessList',
    params
  })
}
