<!-- 地址选择弹窗 -->
<template>
  <div class="flex">
    <a-cascader
      class="!mr-[4px]"
      ref="cascaderRef"
      v-model:value="ruleForm.localPcaCode"
      :options="areaList"
      placeholder="请选择区域"
      :disabled="disabled"
      @change="cascaderChange"
    />
    <a-input
      v-model:value="ruleForm.localAddress"
      placeholder="请输入详细地址"
      allow-clear
      :maxlength="64"
      :disabled="disabled"
      @change="inputChange"
    ></a-input>
  </div>
</template>
<script setup>
import areaList from '@/json/region.json'
const emits = defineEmits(['setAddress', 'update:address', 'update:modelValue'])
const { modelValue, address, disabled } = defineProps({
  modelValue: { required: true, type: Array },
  address: { required: true, type: String },
  disabled: { default: false, type: Boolean }
})

const ruleForm = ref({
  localPcaCode: [],
  localAddress: ''
})

const cascaderChange = (value, selectedOptions) => {
  emits('update:modelValue', value)
  emits('setAddress', selectedOptions)
}
const inputChange = () => {
  emits('update:address', ruleForm.value.localAddress)
}

watch(
  () => modelValue,
  (newValue) => {
    ruleForm.value.localPcaCode = newValue || []
  },
  { immediate: true }
)

watch(
  () => address,
  (newValue) => {
    ruleForm.value.localAddress = newValue || ''
  },
  { immediate: true }
)
</script>
