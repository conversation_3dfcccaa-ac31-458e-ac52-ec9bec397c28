<template>
  <a-drawer
    v-model:open="visible"
    title="招租方案详情"
    class="common-detail-drawer"
    placement="right"
    destroy-on-close
    width="1072px"
    :root-style="{ zIndex: 900 }"
    @close="handleClose"
  >
    <template #extra>
      <a-dropdown>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
          更多
          <i class="a-icon-arrow-down"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <div class="primary-btn" @click="handleAudit(true)">审核(临时功能)</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
              <div class="primary-btn" @click="handleEdit">编辑</div>
            </a-menu-item>
            <a-menu-item v-if="['TEMP', 'BACK'].includes(detail.status)">
              <div class="primary-btn" @click="handleRemove">删除</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITING'].includes(detail.status)">
              <div class="primary-btn" @click="handleWithdraw">撤回</div>
            </a-menu-item>
            <a-menu-item v-if="detail.bidResult">
              <div class="primary-btn" @click="handleViewCustomer(detail)">查看中标客户</div>
            </a-menu-item>
            <a-menu-item v-if="detail.contract">
              <div class="primary-btn" @click="handleViewContract">查看中标合同</div>
            </a-menu-item>
            <a-menu-item v-if="detail.bizStatus === 'Listing' && detail.status === 'AUDITOK'">
              <div class="primary-btn" @click="handleSetResult">招标结果</div>
            </a-menu-item>
            <a-menu-item v-if="detail.bizStatus === 'FailedBidding' && detail.status === 'AUDITOK'">
              <div class="primary-btn" @click="handleRegain">重新招标</div>
            </a-menu-item>
            <a-menu-item v-if="detail.bizStatus === 'SuccessBidding' && !detail.contract">
              <div class="primary-btn" @click="handleCreateContract">创建合同</div>
            </a-menu-item>
            <a-menu-item v-if="['AUDITOK'].includes(detail.status)">
              <div class="primary-btn" @click="handleAudit(false)">反审核</div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.title }}</h2>
        <status-tag
          v-if="detail.bizStatus"
          dict-code="CT_BASE_ENUM_RentScheme_BizStatus"
          :dict-value="detail.bizStatus"
        ></status-tag>
        <status-tag v-else dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>方案编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #baseInfo>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">公告标题: {{ detail.title }}</span>
            <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
            <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
            <span class="w-[50%]">经办人: {{ detail.operator_dictText }}</span>
            <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
            <span class="w-[50%]">
              <span class="mr-[4px]">业务状态:</span>
              <status-tag
                dict-code="CT_BASE_ENUM_RentScheme_BizStatus"
                :dict-value="detail.bizStatus"
                type="dot"
                v-if="detail.bizStatus"
              ></status-tag>
              <span v-else>-</span>
            </span>
            <span class="w-[50%]">流标次数: {{ detail.failedBiddingCount || '-' }}</span>
            <span class="w-[50%]">过会文号: {{ detail.reviewDocumentNo }}</span>
            <span class="w-[50%]">批复文号: {{ detail.auditDocumentNo }}</span>
            <span class="w-[50%]">招租方式: {{ detail.rentType_dictText }}</span>
            <span class="w-[50%]">公示开始日期: {{ detail.publicStartTime }}</span>
            <span class="w-[50%]">公示结束日期: {{ detail.publicEndTime }}</span>
            <span class="w-[50%]">招标信息发布日期: {{ detail.publicDate }}</span>
          </div>
        </template>
        <template #leaseUnit>
          <a-table
            :data-source="detail.rentSchemeEntryList"
            :columns="columns"
            :pagination="false"
            :scroll="{ x: 1500 }"
          ></a-table>
        </template>
        <template #scheme>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">承担对象要求: {{ detail.bearerObject_dictText }}</span>
            <span class="w-[50%]">招租总面积: {{ detail.totalArea ? `${detail.totalArea}m²` : '' }}</span>
            <span class="w-[50%]">市场参考价: {{ detail.referencePrice ? `${detail.referencePrice}元` : '-' }}</span>
            <span class="w-[50%]">招租底价: {{ detail.limitPrice ? `${detail.limitPrice}元` : '-' }}</span>
            <span class="w-[50%]">单位租金: {{ detail.price ? `${detail.price}元` : '-' }}</span>
            <span class="w-[50%]">租赁期限(月): {{ detail.rentMonths || '-' }}</span>
            <span class="w-[50%]">价格递增方式: {{ detail.priceIncrease_dictText || '-' }}</span>
            <span class="w-[50%]">经营范围: {{ detail.managerange || '-' }}</span>
            <span class="w-[50%]">环保条件: {{ detail.environmental || '-' }}</span>
            <span class="w-[50%]">配套情况: {{ detail.supporting || '-' }}</span>
            <span class="w-[50%]">区域优势: {{ detail.advantage || '-' }}</span>
            <span class="w-[50%]">对装修期要求: {{ detail.redecorateReq || '-' }}</span>
            <span class="w-[50%]">其他规范要求: {{ detail.otherReq || '-' }}</span>
          </div>
        </template>
        <template #customer-title>
          <div class="flex items-center justify-between mb-[12px]">
            <h4 class="text-[16px] font-bold">竞标客户</h4>
            <a-button type="primary" size="medium" @click="handleAddCustomer">
              <i class="a-icon-plus"></i>
              新增竞标客户
            </a-button>
          </div>
          <a-table :data-source="customerList" :columns="customerColumns" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'isSuccessBidding'">
                <status-tag v-if="record.isSuccessBidding" color="#6EC21B" type="dot">已中标</status-tag>
                <status-tag v-else color="#D7DAE0" type="dot">未中标</status-tag>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <span
                  class="primary-btn"
                  @click="handleEditCustomer(record)"
                  v-if="!record.isSuccessBidding || !record.customer"
                >
                  编辑
                </span>
                <span
                  class="primary-btn"
                  @click="handleViewCustomer({ customerId: record.customer })"
                  v-if="record.isSuccessBidding && record.customer"
                >
                  查看详情
                </span>
                <span
                  class="primary-btn"
                  @click="handleCreateCustomer(record)"
                  v-if="record.isSuccessBidding && !record.customer"
                >
                  创建客户
                </span>
                <a-popconfirm
                  title="确认删除该竞标客户？"
                  @confirm="handleRemoveCustomer(record)"
                  v-if="!record.isSuccessBidding"
                >
                  <span class="primary-btn">删除</span>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
          <section class="mt-[24px]">
            <div v-for="(item, index) in logList" :key="item.id" class="mb-[40px] last-of-type:mb-[0]">
              <div class="mb-[12px]" v-if="item.name === '流标'">
                <strong>第{{ index + 1 }}次流标材料</strong>
                <span class="text-tertiary ml-[40px]">{{ item.createTime.slice(0, 10) }}</span>
                <div class="text-tertiary mt-[12px]">流标说明: {{ item.remark }}</div>
              </div>
              <div class="mb-[12px]" v-else>
                <strong>中标</strong>
                <span class="text-tertiary ml-[40px]">{{ item.createTime.slice(0, 10) }}</span>
                <div class="mt-[12px]">
                  中标客户:
                  <span class="text-primary cursor-pointer" @click="handleViewCustomer(detail)">
                    {{ item.customer_dictText }}
                  </span>
                </div>
              </div>
              <file-list :biz-id="item.id" :show-empty="false"></file-list>
            </div>
          </section>
        </template>
      </anchor-tabs>
    </a-spin>
    <edit-customer ref="editCustomerRef" :detail-id="detail.id" @refresh="loadCustomerList(detail.id)"></edit-customer>
  </a-drawer>
</template>

<script setup>
import {
  detail as getDetail,
  queryLeaseUnit,
  deleteBatch,
  queryRentSchemeCustomers,
  logList as getLogList,
  removeCustomer
} from '../apis.js'
import { Modal, message } from 'ant-design-vue'
import EditCustomer from './EditCustomer.vue'
import { hasPermission } from '@/utils/permission'

const emit = defineEmits([
  'edit',
  'refresh',
  'withdraw',
  'audit',
  'createContract',
  'viewCustomer',
  'viewContract',
  'setResult',
  'regain'
])

const router = useRouter()

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadData(id)
}

const tabList = [
  { title: '基础信息', name: 'baseInfo' },
  { title: '租赁单元', name: 'leaseUnit' },
  { title: '承租方案', name: 'scheme' },
  { title: '竞标客户', name: 'customer' }
]

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([loadDetail(id), loadLeaseUnit(id), loadCustomerList(id), loadLogList(id)])
  loading.value = false
}

const detail = reactive({ id: '' })
const loadDetail = async (id) => {
  const { result } = await getDetail({ id })
  for (const key in result) {
    if (key !== 'rentSchemeEntryList') {
      detail[key] = result[key]
    }
  }
}

const loadLeaseUnit = async (id) => {
  const { result: list } = await queryLeaseUnit({ id })
  detail.rentSchemeEntryList = list && list.length ? list : []
}

const customerList = ref([])
// 获取竞标客户列表
const loadCustomerList = async (id) => {
  const { result } = await queryRentSchemeCustomers({ id })
  customerList.value = result
}

const editCustomerRef = ref()
const handleAddCustomer = () => {
  editCustomerRef.value.open()
}
const handleEditCustomer = (data) => {
  editCustomerRef.value.open({
    id: data.id,
    parent: data.parent,
    name: data.name,
    customerType: data.customerType,
    customer: data.customer,
    linkman: data.linkman,
    linkmanPhone: data.linkmanPhone,
    remark: data.remark,
    isSuccessBidding: false
  })
}

const handleRemoveCustomer = async (data) => {
  await removeCustomer({ id: data.id })
  message.success('删除成功')
  loadCustomerList(detail.id)
}

const handleCreateCustomer = (data) => {
  sessionStorage.setItem(
    'customerFromRentScheme',
    JSON.stringify({
      sourceBillId: data.id,
      linkman: data.linkman,
      linkmanPhone: data.linkmanPhone,
      name: data.name,
      customerType: data.customerType,
      customerSource: 'Bid'
    })
  )
  handleClose()
  router.push({ path: '/customer/manage' })
}

const logList = ref([])
// 获取流标材料列表
const loadLogList = async (id) => {
  const { result } = await getLogList({ parent: id })
  logList.value = result.records.filter((item) => ['中标', '流标'].includes(item.name))
}

const handleEdit = () => {
  handleClose()
  emit('edit', detail)
}

const handleWithdraw = () => {
  emit('withdraw', detail)
}

const handleAudit = (result) => {
  emit('audit', detail, result)
}

const handleCreateContract = () => {
  handleClose()
  emit('createContract', detail)
}

const handleRegain = () => {
  emit('regain', detail)
}

const handleViewCustomer = (detail) => {
  emit('viewCustomer', detail)
}

const handleViewContract = () => {
  emit('viewContract', detail)
}

const handleSetResult = () => {
  emit('setResult', detail)
}

const handleRemove = () => {
  if (!hasPermission('biz.leasemanage:ct_biz_rent_scheme:delete')) return
  Modal.confirm({
    title: '确认删除该招租方案？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

const columns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit_dictText', fixed: 'left' },
  { title: '原租金', dataIndex: 'originalRent', width: 200 },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' }
]

const customerColumns = [
  { title: '客户名称', dataIndex: 'name', fixed: 'left' },
  { title: '客户类型', dataIndex: 'customerType_dictText' },
  { title: '联系人', dataIndex: 'linkman' },
  { title: '手机号', dataIndex: 'linkmanPhone' },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  { title: '是否中标', dataIndex: 'isSuccessBidding' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

defineExpose({ open, visible, loadData })
</script>
