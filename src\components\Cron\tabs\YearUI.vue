<template>
  <div>
    <a-radio-group v-model:value="type">
      <div class="item">
        <a-radio :value="TypeEnum.every" v-bind="beforeRadioAttrs">每年</a-radio>
      </div>
      <div class="item">
        <a-radio :value="TypeEnum.range" v-bind="beforeRadioAttrs">区间</a-radio>
        <span>从</span>
        <a-input-number class="w80" v-model:value="valueRange.start" v-bind="typeRangeAttrs" />
        <span>年 至</span>
        <a-input-number class="w80" v-model:value="valueRange.end" v-bind="typeRangeAttrs" />
        <span>年</span>
      </div>
      <div class="item">
        <a-radio :value="TypeEnum.loop" v-bind="beforeRadioAttrs">循环</a-radio>
        <span>从</span>
        <a-input-number class="w80" v-model:value="valueLoop.start" v-bind="typeLoopAttrs" />
        <span>年开始，间隔</span>
        <a-input-number class="w80" v-model:value="valueLoop.interval" v-bind="typeLoopAttrs" />
        <span>年</span>
      </div>
    </a-radio-group>
  </div>
</template>

<script setup>
import { useTabProps, useTabEmits, useTabSetup } from './useTabMixin'
const props = defineProps(useTabProps({ defaultValue: '*' }))
const emits = defineEmits(useTabEmits())
const nowYear = new Date().getFullYear()
const { type, beforeRadioAttrs, valueRange, typeRangeAttrs, valueLoop, typeLoopAttrs, TypeEnum } = useTabSetup(
  props,
  emits,
  {
    defaultValue: '*',
    minValue: 0,
    valueRange: { start: nowYear, end: nowYear + 100 },
    valueLoop: { start: nowYear, interval: 1 }
  }
)

// export default defineComponent({
//   name: 'YearUI',
//   components: { InputNumber },
//   props: useTabProps({
//     defaultValue: '*'
//   }),
//   emits: useTabEmits(),
//   setup(props, context) {
//     const nowYear = new Date().getFullYear()
//     return useTabSetup(props, context, {
//       defaultValue: '*',
//       minValue: 0,
//       valueRange: { start: nowYear, end: nowYear + 100 },
//       valueLoop: { start: nowYear, interval: 1 }
//     })
//   }
// })
</script>
