import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/flowable/definition/list',
    params
  })
}

// 流程建模设计-导入流程文件,上传bpmn20的xml文件
export const importXml = (data) => {
  return request({
    method: 'post',
    url: '/flowable/definition/import',
    data
  })
}

// 流程建模设计-保存流程设计器内的xml文件
export const saveXml = (data) => {
  return request({
    method: 'post',
    url: '/flowable/definition/save',
    data
  })
}

// 流程建模设计-读取xml文件-通过单据数据ID
export const readXmlByDataId = (dataId) => {
  return request({
    method: 'get',
    url: `/flowable/definition/readXmlByDataId/${dataId}`
  })
}

// 流程建模设计-读取xml文件-通过流程建模设计id
export const readXml = (deployId) => {
  return request({
    method: 'get',
    url: `/flowable/definition/readXml/${deployId}`
  })
}

// 流程建模设计-读取图片文件-通过流程建模设计id
export const readImage = (deployId) => {
  return request({
    method: 'get',
    url: `/flowable/definition/readImage/${deployId}`
  })
}

// 流程建模设计-激活或挂起流程建模设计
export const updateState = (params) => {
  return request({
    method: 'post',
    url: '/flowable/definition/updateState',
    params
  })
}

export const copy = (params) => {
  return request({
    method: 'get',
    url: '/flowable/definition/copy',
    params
  })
}

export const del = (params) => {
  return request({
    method: 'delete',
    url: '/flowable/definition/delete',
    params
  })
}
