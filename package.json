{"name": "park-management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode dev", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode prod", "preview": "vite preview", "prepare": "husky install"}, "lint-staged": {"*.{js,vue}": ["eslint --fix", "eslint"]}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@codemirror/lang-javascript": "6.2.4", "@codemirror/theme-one-dark": "6.1.3", "@tinymce/tinymce-vue": "5.0.1", "ant-design-vue": "4.2.6", "axios": "1.8.4", "bpmn-js": "8.10.0", "bpmn-js-task-resize": "1.2.0", "bpmn-js-token-simulation": "0.10.0", "codemirror": "6.0.2", "cron-parser": "4.9.0", "dayjs": "1.10.5", "decimal.js": "10.5.0", "diagram-js": "7.8.2", "diagram-js-direct-editing": "1.6.3", "diagram-js-minimap": "2.1.1", "echarts": "5.6.0", "js-beautify": "1.15.4", "min-dash": "3.5.2", "mitt": "3.0.1", "pinia": "2.3.1", "pinia-plugin-persistedstate": "3.2.3", "tailwindcss": "4.1.4", "tinymce": "5.2.2", "vue": "3.5.13", "vue-codemirror": "6.1.1", "vue-cropper": "1.1.4", "vue-router": "4.5.0", "vue-tianditu": "2.7.6", "vuedraggable": "4.1.0"}, "devDependencies": {"@commitlint/cli": "17", "@commitlint/config-conventional": "17", "@eslint/config-helpers": "0.2.1", "@eslint/css": "0.7.0", "@eslint/js": "9.25.0", "@tailwindcss/vite": "4.1.4", "@vitejs/plugin-vue": "5.2.2", "eslint": "9.25.0", "eslint-plugin-vue": "10.0.0", "globals": "16.0.0", "husky": "8", "less": "4.3.0", "less-loader": "12.2.0", "lint-staged": "11", "prettier": "3.5.3", "unplugin-auto-import": "19.1.2", "unplugin-vue-components": "28.5.0", "vite": "6.3.1", "vite-plugin-zip-pack": "1.2.4"}}