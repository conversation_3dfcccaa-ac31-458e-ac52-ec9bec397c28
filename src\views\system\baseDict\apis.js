import request from '@/apis/http'

export const getDictList = (params) => {
  return request({
    method: 'get',
    url: '/sys/dict/baseList',
    params
  })
}

export const addDict = (data) => {
  return request({
    method: 'post',
    url: '/sys/dict/add',
    data
  })
}

export const editDict = (data) => {
  return request({
    method: 'post',
    url: '/sys/dict/edit',
    data
  })
}

export const deleteDict = (params) => {
  return request({
    method: 'delete',
    url: '/sys/dict/delete',
    params
  })
}

// 检查字典是否可用
export const checkDict = (tableName, fieldName, fieldVal) => {
  return request({
    method: 'get',
    url: '/sys/duplicate/check',
    params: { tableName, fieldName, fieldVal }
  })
}

export const getDictItemList = (params) => {
  return request({
    method: 'get',
    url: '/sys/dictItem/list',
    params
  })
}

export const addDictItem = (data) => {
  return request({
    method: 'post',
    url: '/sys/dictItem/add',
    data
  })
}

export const editDictItem = (data) => {
  return request({
    method: 'post',
    url: '/sys/dictItem/edit',
    data
  })
}

export const deleteDictItem = (params) => {
  return request({
    method: 'delete',
    url: '/sys/dictItem/delete',
    params
  })
}

// 检查字典是否可用
export const checkDictItem = (params) => {
  return request({
    method: 'get',
    url: '/sys/dictItem/dictItemCheck',
    params
  })
}

export const queryAllDictItems = () => {
  return request({
    method: 'get',
    url: '/sys/dict/queryAllDictItems'
  })
}
