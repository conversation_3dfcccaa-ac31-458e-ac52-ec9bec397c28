<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex items-center">
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">批量删除</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.esTitle"
          placeholder="搜索标题"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
      </div>
      <div class="flex items-center">
        <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)" v-auth="'message:sys_sms:view'">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="delete">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <message-detail ref="messageDetailRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { Modal, message } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getMessageList, deleteMessage, batchDeleteMessages } from './apis'
import MessageDetail from './components/MessageDetail.vue'

const messageDetailRef = ref()
const columnSetRef = ref()

const searchParams = reactive({
  column: 'createTime',
  order: 'desc',
  esTitle: undefined
})

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getMessageList)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

const defaultColumns = [
  { title: '消息标题', dataIndex: 'esTitle', fixed: 'left', width: 150 },
  { title: '发送内容', dataIndex: 'esContent', ellipsis: true },
  { title: '接收人', dataIndex: 'esReceiverName', width: 200 },
  { title: '发送次数', dataIndex: 'esSendNum', width: 120 },
  { title: '发送状态', dataIndex: 'esSendStatus_dictText', width: 120 },
  { title: '发送时间', dataIndex: 'esSendTime', width: 200, ellipsis: true },
  { title: '发送方式', dataIndex: 'esType_dictText', width: 120 },
  { title: '已读/未读', dataIndex: 'readFlag_dictText', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 查看消息详情
 */
const handleDetail = (record) => {
  messageDetailRef.value.open(record.id)
}

/**
 * 删除单条消息
 */
const handleDelete = (record) => {
  if (!hasPermission('message:sys_sms:delete')) return
  Modal.confirm({
    title: '提示',
    content: '确定要删除这条消息吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await deleteMessage({ id: record.id })
      message.success('删除成功')
      onTableChange()
      if (list.value.length === 1 && pagination.value.current > 1) {
        onTableChange({ pageNo: pagination.value.current - 1, pageSize: pagination.value.pageSize })
      }
    }
  })
}

/**
 * 批量删除消息
 */
const handleBatchDelete = () => {
  if (!hasPermission('message:sys_sms:deleteBatch')) return
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的数据')
    return
  }

  Modal.confirm({
    title: '提示',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条消息吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteMessages({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      selectedRowKeys.value = []
      onTableChange()
      if (list.value.length <= selectedRowKeys.value.length && pagination.value.current > 1) {
        onTableChange({ pageNo: pagination.value.current - 1, pageSize: pagination.value.pageSize })
      }
    }
  })
}

/**
 * 搜索输入处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格变化处理
 */
const onTableChange = ({ current = pagination.value.current, pageNo, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: pageNo ?? current, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})
</script>
