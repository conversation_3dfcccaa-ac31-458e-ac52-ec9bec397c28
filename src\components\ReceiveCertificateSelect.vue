<!-- 应收单选择弹窗 -->
<template>
  <a-modal
    v-model:open="visible"
    title="选择应收单"
    width="1072px"
    :mask-closable="false"
    class="common-modal"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <!-- 搜索区域 -->
    <a-form class="!mb-[12px]" autocomplete="off" layout="inline">
      <a-form-item label="">
        <s-input class="!w-[280px]" v-model:value="searchParams.customer" placeholder="请搜索"></s-input>
      </a-form-item>
      <a-form-item>
        <search-more v-model="searchParams" :search-list="searchList" @searchChange="handleFilterSearch"></search-more>
      </a-form-item>
    </a-form>

    <!-- 已选择提示 -->
    <!-- <div class="mb-[12px] px-[12px] py-[8px] bg-blue-50 rounded border border-blue-200" v-if="multiple">
      <span class="text-blue-600">已选择 {{ selectedCount }} 个收付款记录</span>
      <span class="primary-btn ml-2 !text-gray-400" @click="handleClearAll" v-if="selectedCount > 0">清空选择</span>
    </div> -->

    <!-- 表格区域 -->
    <a-table
      :data-source="list"
      :columns="(tableColumns.length && tableColumns) || columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        selectedRows,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_AuditStatus" type="dot"></status-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import { getPage } from '@/views/statement/receiveCertificate/apis.js'
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
const visible = ref(false)
/**
 * 打开选择器
 */
const open = (selectedUnits = []) => {
  onTableChange()
  if (selectedUnits && selectedUnits.length > 0) {
    const selectedIds = selectedUnits.map((unit) => unit.id)
    selectedRowKeys.value = selectedIds
    // 根据 id 从 list 中获取完整的对象数据
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  searchParams.value = params
  visible.value = true
}
defineExpose({ open })

const { multiple, asyncFunc, params, tableColumns } = defineProps({
  multiple: { default: true, type: Boolean },
  asyncFunc: { default: getPage, type: Function },
  tableColumns: {
    default: () => {
      return []
    },
    type: Array
  },
  params: {
    default: () => {
      return {
        bizStatus: '',
        customer: ''
      }
    },
    type: Object
  }
})
// const activeTab = ref('1')
const emits = defineEmits(['selectChange'])

const searchList = reactive([])
const columns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: true },
  { title: '客户名称', dataIndex: 'customer_dictText' },
  { title: '合同', dataIndex: 'contractNum' },
  { title: '租赁单元', dataIndex: 'leaseUnit' },
  { title: '款项类型', dataIndex: 'paymentType' },
  { title: '结算状态', dataIndex: '' },
  { title: '期数/总期数', dataIndex: '' },
  { title: '款项金额', dataIndex: '' },
  { title: '减免金额', dataIndex: '' },
  { title: '应收金额', dataIndex: 'receiveAmt' },
  { title: '已收金额', dataIndex: 'consumedAmt' },
  { title: '剩余金额', dataIndex: 'notConsumedAmt' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '结清日期', dataIndex: '' }
]
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(asyncFunc)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}
// const selectedCount = computed(() => selectedRows.value.length)
const searchParams = ref({ bizStatus: '', customer: '' })
/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  handleCancel()
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 清空所有选择
 */
// const handleClearAll = () => {
//   clearSelection()
// }

/**
 * 搜索处理
 */
// let timer
// const handleSearch = () => {
//   clearTimeout(timer)
//   timer = setTimeout(() => {
//     onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
//   }, 600)
// }

/**
 * 筛选搜索处理
 */
const handleFilterSearch = () => {
  pagination.value.current = 1
  onTableChange()
}
</script>
