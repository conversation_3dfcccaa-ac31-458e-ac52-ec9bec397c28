import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/leasemanage/rentScheme/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/leasemanage/rentScheme/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/unAudit',
    data
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/importExcel',
    data
  })
}

export const save = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/submit',
    data
  })
}

// 变更方案
export const modify = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/modify',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/edit',
    data
  })
}

export const withdraw = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/back',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/leasemanage/rentScheme/deleteBatch',
    params
  })
}

export const queryLeaseUnit = (params) => {
  return request({
    method: 'get',
    url: '/biz/leasemanage/rentScheme/queryRentSchemeEntryByMainId',
    params
  })
}

// 招租方案跟踪记录
export const logList = (params) => {
  return request({
    method: 'get',
    url: '/biz/leasemanage/rentSchemeFollowRecord/list',
    params
  })
}

// 招租方案ID提交中标用户
export const setSuccessResult = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/success',
    data
  })
}

// 招租方案流标
export const setFailureResult = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/failure',
    data
  })
}

export const queryRentSchemeCustomers = (params) => {
  return request({
    method: 'get',
    url: '/biz/leasemanage/rentScheme/queryRentSchemeCustomerDetailByMainId',
    params
  })
}

// 重新招标挂牌
export const regain = (data) => {
  return request({
    method: 'post',
    url: `/biz/leasemanage/rentScheme/reListing/${data.id}`
  })
}

// 添加竞标客户
export const addCustomer = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/addRentSchemeCustomer',
    data
  })
}

// 编辑竞标客户
export const editCustomer = (data) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/editRentSchemeCustomer',
    data
  })
}

// 竞标客户删除
export const removeCustomer = (params) => {
  return request({
    method: 'post',
    url: '/biz/leasemanage/rentScheme/deleteRentSchemeCustomerById',
    params
  })
}
