<template>
  <a-modal
    v-model:open="visible"
    :title="`修改密码[${userInfo.realname}]`"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form :model="form" ref="formRef" :rules="rules" autocomplete="off" :label-col="{ style: { width: '84px' } }">
      <a-form-item label="用户账号" name="username">
        <a-input v-model:value="form.username" disabled></a-input>
      </a-form-item>
      <a-form-item label="登录密码" name="password">
        <a-input-password v-model:value="form.password" :maxlength="20"></a-input-password>
      </a-form-item>
      <a-form-item label="确认密码" name="confirmPassword">
        <a-input-password v-model:value="form.confirmPassword" :maxlength="20"></a-input-password>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { validatePassword } from '@/utils/validate'
import { message } from 'ant-design-vue'
import { changePassword } from '../apis.js'

const visible = ref(false)

const userInfo = reactive({})
const open = (data) => {
  Object.assign(userInfo, data)
  form.username = data.username
  visible.value = true
}

const form = reactive({
  username: '',
  confirmPassword: '',
  password: ''
})
const rules = computed(() => ({
  username: [{ required: true, message: '请输入用户账号', trigger: 'blur' }],
  password: [{ required: true, validator: validatePassword('登录密码'), trigger: 'blur' }],
  confirmPassword: [{ required: true, validator: validatePassword('确认密码', form.password), trigger: 'blur' }]
}))

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    await changePassword(form)
    confirmLoading.value = false
    handleCancel()
    message.success('密码修改成功')
  } catch {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  formRef.value.resetFields()
  visible.value = false
}

defineExpose({ open })
</script>
