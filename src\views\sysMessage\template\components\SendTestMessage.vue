<template>
  <a-modal
    v-model:open="visible"
    class="common-modal"
    title="发送测试消息"
    width="800px"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="消息类型" name="msgType">
        <dict-select v-model="formData.msgType" code="messageType" placeholder="请选择消息类型" />
      </a-form-item>

      <a-form-item label="接收者" name="receiver">
        <a-form-item-rest>
          <customer-select v-model="formData.receiver" placeholder="请选择客户" :multiple="false" />
        </a-form-item-rest>
      </a-form-item>

      <a-form-item label="模板编码" name="templateCode">
        <a-input v-model:value="formData.templateCode" placeholder="请输入模板编码" :disabled="true" />
      </a-form-item>

      <a-form-item label="参数" name="param">
        <a-textarea
          v-model:value="formData.param"
          placeholder='请输入参数（JSON格式，如：{"name": "张三"}）'
          :rows="3"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { sendTestMessage } from '../apis'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  msgType: 'system',
  receiver: undefined,
  templateCode: undefined,
  param: undefined
}

const formData = reactive({ ...formDataDefault })

const rules = {
  msgType: [{ required: true, message: '请选择消息类型', trigger: 'change' }],
  receiver: [{ required: true, message: '请选择接收者', trigger: 'change' }],
  templateCode: [{ required: true, message: '模板编码不能为空', trigger: 'blur' }]
}

/**
 * 打开对话框
 */
const open = (record) => {
  visible.value = true
  nextTick(() => {
    formRef.value?.resetFields()
    Object.assign(formData, formDataDefault)
    formData.templateCode = record.templateCode || ''
  })
}

/**
 * 取消
 */
const handleCancel = () => {
  visible.value = false
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
}

/**
 * 确认发送
 */
const handleConfirm = async () => {
  if (confirmLoading.value) return

  await formRef.value?.validate()

  if (formData.param) {
    try {
      JSON.parse(formData.param)
    } catch {
      message.error('参数格式不正确，请输入有效的JSON格式')
      return
    }
  }

  try {
    confirmLoading.value = true
    await sendTestMessage({
      msgType: formData.msgType,
      receiver: formData.receiver,
      templateCode: formData.templateCode,
      param: formData.param
    })
    message.success('测试消息发送成功')
    handleCancel()
    emit('refresh')
  } finally {
    confirmLoading.value = false
  }
}

defineExpose({
  open
})
</script>
