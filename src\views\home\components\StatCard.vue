<template>
  <a-row :gutter="[16, 16]">
    <a-col v-for="(h, index) in houseOwnerList" :key="h.id" :xs="2" :sm="4" :md="6">
      <div
        class="house cursor-pointer"
        :style="{
          backgroundImage: `url(${bg('house', index)})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'inherit 100%',
          backgroundPosition: 'right center'
        }"
      >
        <div class="text-[#495A7A] flex items-center mb-[30px]">
          <i :class="h.icon"></i>
          <span class="ml-[4px]">{{ h.name }}</span>
        </div>
        <a-statistic :value="h.num" class="text-[#1D335C] text-[40px] font-bold" />
      </div>
    </a-col>
  </a-row>
</template>

<script setup>
import { houseOwnerData } from '@/views/home/<USER>'

const props = defineProps({
  manageCompany: { required: true, type: String, default: '' }
})

// 数据看板-资产总面积-资产数量(个)-本年资产应收(万元)-本年资产已收(万元)
const loadHouseOwnerData = async () => {
  const { result } = await houseOwnerData({ manageCompany: props.manageCompany })
  houseOwnerList[0].num = result.area
  houseOwnerList[1].num = result.count
  houseOwnerList[2].num = result.receivable
  houseOwnerList[3].num = result.receipts
}

watch(
  () => props.manageCompany,
  () => {
    loadHouseOwnerData()
  },
  { immediate: true }
)

const houseOwnerList = reactive([
  { id: 1, name: '资产总面积（m²）', num: 0, icon: 'a-icon-house', url: '' },
  { id: 2, name: '资产个数（个）', num: 0, icon: 'a-icon-coins', url: '' },
  { id: 3, name: '本年资产应收（万元）', num: 0, icon: 'a-icon-money2', url: '' },
  { id: 4, name: '本年资产已收（万元）', num: 0, icon: 'a-icon-wallet', url: '' }
])

const bg = (type, index) => {
  return new URL(`/src/assets/imgs/home/<USER>
}
</script>

<style lang="less" scoped>
.house {
  height: 132px;
  padding: 20px;
  padding: 20px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e6e9f0;
}
</style>
