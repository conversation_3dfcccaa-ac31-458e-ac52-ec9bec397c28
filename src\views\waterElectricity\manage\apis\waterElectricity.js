import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 获取水电表列表
export const getWaterElectricityList = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricity/list',
    params
  })
}

export const f7List = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricity/f7list',
    params
  })
}

// 通过id查询水电表详情
export const queryWaterElectricityById = (params) => {
  return request({
    method: 'get',
    url: '/bas/waterElectricity/queryById',
    params
  })
}

// 新增水电表
export const addWaterElectricity = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricity/add',
    data
  })
}

// 编辑水电表
export const editWaterElectricity = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricity/edit',
    data
  })
}

// 批量编辑水电表
export const batchEditWaterElectricity = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricity/batchEdit',
    data
  })
}

// 删除水电表
export const deleteWaterElectricity = (params) => {
  return request({
    method: 'delete',
    url: '/bas/waterElectricity/delete',
    params
  })
}

// 批量删除水电表
export const batchDeleteWaterElectricity = (params) => {
  return request({
    method: 'delete',
    url: '/bas/waterElectricity/deleteBatch',
    params
  })
}

// 更新启用/禁用状态
export const updateEnableDisableStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricity/updateEnableDisableStatus',
    data
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/waterElectricity/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/waterElectricity/importExcel', data, controller)
}
