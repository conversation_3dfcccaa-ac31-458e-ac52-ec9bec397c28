<template>
  <div>
    <div class="flex justify-between !mb-[14px]">
      <a-form autocomplete="off" layout="inline">
        <a-button v-auth="'biz.consume:ct_con_consumed_record:add'" class="mb-[10px]" type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button v-auth="'biz.consume:ct_con_consumed_record:importExcel'" class="mb-[10px]" @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button
          v-auth="'biz.consume:ct_con_consumed_record:exportXls'"
          class="mb-[10px]"
          :loading="exportLoading"
          @click="handleExport"
        >
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="mb-[10px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <div class="primary-btn" @click="rowWriteBack(false)">反核销</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button class="mb-[10px]" @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form-item class="!ml-[40px] !mb-[10px]" label="">
          <s-input
            v-model="search.number"
            placeholder="搜索核销序号"
            class="ml-[10px] !w-[280px]"
            @input="handleInput"
          ></s-input>
        </a-form-item>
        <a-form-item class="!mb-[10px]">
          <search-more v-model="searchFilter" :search-list="searchList" @searchChange="onTableChange"></search-more>
        </a-form-item>
      </a-form>
      <columns-set class="!mb-[10px]" :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2400 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowWriteBack(record)">反核销</span>
        </template>
      </template>
    </a-table>

    <add-edit ref="addEditRef"></add-edit>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import { Modal, message } from 'ant-design-vue'
import AddEdit from './components/AddEdit.vue'
import { getPage, undoConsumed, exportExcel, importExcel } from './apis'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { renderMoney, renderDict } from '@/utils/render'
onMounted(() => {
  // 收付款记录的主表id
  if (route.query.id) {
    search.value.sourceBillId = route.query.id
  }
  onTableChange()
})
const route = useRoute()
const search = ref({
  column: 'number',
  order: 'desc',
  number: undefined,
  sourceBillId: ''
})
const searchFilter = ref({})
const searchList = reactive([
  { label: '物业管理公司', name: 'manageCompany', type: 'companySelect', placeholder: '请选择物业管理公司' },
  { label: '合同编号', name: 'contractNum', type: 's-input', placeholder: '请输入合同编号' },
  {
    label: '租赁单元',
    name: 'leaseUnit',
    type: 'leaseUnitSelect',
    placeholder: '请选择租赁单元'
  },
  {
    label: '款项类型',
    name: 'paymentType',
    type: 'paymentTypeSelect',
    placeholder: '请选择款项类型',
    fieldNames: { label: 'name', value: 'id' }
  },
  // { label: '期数/总期数', name: 'periodTotalPeriod', type: 'input', placeholder: '请输入期数/总期数' },
  // { label: '归属年月', name: 'incomeBelongYm', type: 'date', placeholder: '请选择归属年月' },
  { label: '核销金额', name: 'thisConsumedAmt', type: 'number', placeholder: '请输入核销金额' },
  { label: '核销时间', name: 'consumedTime', type: 'date', placeholder: '请选择核销时间' },
  {
    label: '核销人',
    name: 'consumedPerson',
    type: 'userSelect',
    placeholder: '请选择核销人'
  },
  { label: '单据编号', name: 'billNumber', type: 'number', placeholder: '请输入单据编号' },
  { label: '单据类型', name: 'billType', type: 'input', placeholder: '请选择单据类型' },
  { label: '应收开始时间', name: 'receiveBeginDate', type: 'date', placeholder: '请选择应收开始时间' },
  { label: '应收结束时间', name: 'receiveEndDate', type: 'date', placeholder: '请选择应收结束时间' },
  { label: '客户', name: 'customer', type: 'customerSelect', placeholder: '请选择客户' },
  {
    label: '资金归集公司',
    name: 'collectionCompany',
    type: 'companySelect',
    companyType: 'all',
    placeholder: '请选择资金归集公司'
  },
  { label: '经办人', name: 'operator', type: 'userSelect', placeholder: '请选择经办人' },
  { label: '业务部门', name: 'operatorDepart', type: 'departSelect', placeholder: '请选择业务部门' },
  {
    label: '服务处',
    name: 'serviceCenter',
    type: 'dic',
    code: 'CT_BAS_ServiceCenter',
    placeholder: '请选择服务处'
  },
  { label: '停车场', name: 'park', type: 'dic', code: 'CT_BAS_Park', placeholder: '请选择停车场' }
])
const defaultColumns = [
  { title: '核销序号', dataIndex: 'number', width: 180, fixed: true, ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', ellipsis: true },
  { title: '合同编号', dataIndex: 'contractNum', ellipsis: true },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  // { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  // { title: '归属年月', dataIndex: 'incomeBelongYm' },
  { title: '核销金额', dataIndex: 'thisConsumedAmt', customRender: ({ text }) => renderMoney(text, 2) },
  { title: '核销时间', dataIndex: 'consumedTime', ellipsis: true },
  { title: '核销人', dataIndex: 'consumedPerson_dictText' },
  { title: '单据编号', dataIndex: 'billNumber', width: 180 },
  { title: '单据类型', dataIndex: 'billType' },
  { title: '应收开始时间', dataIndex: 'receiveBeginDate' },
  { title: '应收结束时间', dataIndex: 'receiveEndDate' },
  { title: '客户', dataIndex: 'customer_dictText', ellipsis: true },
  { title: '资金归集公司', dataIndex: 'collectionCompany_dictText', ellipsis: true },
  { title: '经办人', dataIndex: 'operator_dictText' },
  { title: '业务部门', dataIndex: 'operatorDepart_dictText', ellipsis: true },
  { title: '服务处', dataIndex: 'serviceCenter', customRender: ({ text }) => renderDict(text, 'CT_BAS_ServiceCenter') },
  { title: '停车场', dataIndex: 'park', customRender: ({ text }) => renderDict(text, 'CT_BAS_Park') },

  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value, ...searchFilter.value })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
// 反核销
const rowWriteBack = (row) => {
  Modal.confirm({
    title: row ? '确定反核销？' : '确定批量反核销？',
    content: '',
    centered: true,
    onOk: async () => {
      const data = await undoConsumed(row ? [row.id] : selectedRowKeys.value)
      message.success(data.message)
      onTableChange({ pageNo: pagination.value.current, pageSize: pagination.value.pageSize })
    }
  })
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('核销记录数据导出.xls', {
      ...search.value,
      ...searchFilter.value,
      id: selectedRowKeys.value.join(',')
    })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}
</script>
