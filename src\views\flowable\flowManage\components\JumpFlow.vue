<template>
  <a-modal
    v-model:open="visible"
    title="跳转流程"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :label-col="{ style: { width: '150px' } }" autocomplete="off">
        <a-form-item label="当前节点" name="currentTaskNameId">
          <a-radio-group v-model:value="form.currentTaskNameId" button-style="solid">
            <a-radio-button :value="currentTask.taskNameId">{{ currentTask.value.taskName }}</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="跳转节点" name="targetKey">
          <a-radio-group v-model:value="form.targetKey" button-style="solid">
            <a-radio-button v-for="j in jumpTaskList" :key="j.id" :value="j.id">{{ j.name }}</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="跳转候选人" name="candidateUsersTemp">
          <f7-select v-model="form.candidateUsersTemp" f7-type="user" mode="multiple"></f7-select>
        </a-form-item>
        <a-form-item label="跳转意见" name="comment" :rules="[{ required: true, message: '请输入跳转意见' }]">
          <a-input v-model:value="form.comment" placeholder="请输入跳转意见" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { flowRecord, processVariables, jumpList, jumpTask } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const loading = ref(false)
const open = async (data) => {
  loading.value = true
  form.deployId = data.deploymentId
  form.taskId = data.taskId
  form.procInsId = data.processInstanceId
  form.dataId = data.dataId
  await loadFlowRecord()
  await loadProcessVariables()
  await loadJumpList()
  loading.value = false
  visible.value = true
}

/** 流程流转记录 */
const currentTask = ref({})
const loadFlowRecord = async () => {
  const { result } = await flowRecord({ procInsId: form.procInsId, dataId: form.dataId })
  currentTask.value = result.currentTaskJsonObj
  form.currentTaskNameId = result.currentTaskJsonObj.taskNameId
}

/** 获取流程变量内容 */
const loadProcessVariables = async () => {
  const { result } = await processVariables(form.taskId)
  form.values = { ...result, dataId: form.dataId, isPass: form.isPass }
}

const jumpTaskList = ref([])
const loadJumpList = async () => {
  const { result } = await jumpList(form)
  jumpTaskList.value = result
}

const form = reactive({
  comment: '', // 意见内容
  currentTaskNameId: '', // 当前任务流程key
  procInsId: '', // 流程实例编号
  instanceId: '', // 流程实例编号
  deployId: '', // 流程定义编号
  taskId: '', // 流程任务编号
  procDefId: '', // 流程编号
  values: {},
  candidateUsersTemp: [], // 跳转候选人
  isPass: 1 // 默认同意
})

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = {
      ...form,
      candidateUsers: form.candidateUsersTemp.join(',')
    }
    await jumpTask(params)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.comment = ''
  form.currentTaskNameId = ''
  form.procInsId = ''
  form.instanceId = ''
  form.deployId = ''
  form.taskId = ''
  form.procDefId = ''
  form.values = {}
  form.candidateUsersTemp = []
  form.isPass = 1
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
