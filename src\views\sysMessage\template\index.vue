<template>
  <div>
    <div class="flex justify-between my-[16px]">
      <div>
        <a-button type="primary" @click="handleAdd" v-auth="'message:sys_sms_template:add'">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport" v-auth="'message:sys_sms_template:importExcel'">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport" v-auth="'message:sys_sms_template:exportXls'">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">
                <div class="primary-btn" @click="handleBatchDelete">删除</div>
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
        </a-dropdown>
        <s-input
          v-model="searchParams.templateName"
          placeholder="搜索模板标题"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'templateContent'">
          <div class="line-clamp-2" :title="record.templateContent">{{ record.templateContent }}</div>
        </template>
        <template v-if="column.dataIndex === 'useStatus'">
          <a-switch
            :checked="record.useStatus === '1'"
            :loading="useStatusLoading"
            @change="handleUseStatusChange(record)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)" v-auth="'message:sys_sms_template:view'">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit">
                  <div class="primary-btn" @click="handleEdit(record)">编辑</div>
                </a-menu-item>
                <a-menu-item key="test">
                  <div class="primary-btn" @click="handleTestSend(record)">测试发送</div>
                </a-menu-item>
                <a-menu-item key="delete">
                  <div class="primary-btn" @click="handleDelete(record)">删除</div>
                </a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      modal-title="批量导入消息模板"
      :download-fn="() => exportExcel('消息模板导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <edit-template ref="editTemplateRef" @refresh="onTableChange" />
    <template-detail ref="templateDetailRef" @refresh="onTableChange" />
    <test-send-dialog ref="testSendDialogRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { hasPermission } from '@/utils/permission'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getTemplateList, deleteTemplate, batchDeleteTemplate, updateTemplate, exportExcel, importExcel } from './apis'
import EditTemplate from './components/EditTemplate.vue'
import TemplateDetail from './components/TemplateDetail.vue'
import TestSendDialog from './components/SendTestMessage.vue'

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getTemplateList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const columnSetRef = ref()
const editTemplateRef = ref()
const templateDetailRef = ref()
const testSendDialogRef = ref()
const commonImportRef = ref()

const useStatusLoading = ref(false)
const exportLoading = ref(false)

const searchParams = reactive({
  column: 'createTime',
  order: 'desc',
  templateCode: undefined,
  templateName: undefined,
  templateType: undefined,
  useStatus: undefined
})

const defaultColumns = [
  { title: '模板编码', dataIndex: 'templateCode', fixed: 'left', width: 200 },
  { title: '模版标题', dataIndex: 'templateName', width: 200 },
  { title: '模版内容', dataIndex: 'templateContent', ellipsis: true },
  { title: '模版类型', dataIndex: 'templateType_dictText', width: 120 },
  { title: '应用状态', dataIndex: 'useStatus', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 新增模板
 */
const handleAdd = () => {
  editTemplateRef.value.open()
}

/**
 * 编辑模板
 */
const handleEdit = (record) => {
  if (!hasPermission('message:sys_sms_template:edit')) return
  editTemplateRef.value.open(record)
}

/**
 * 查看模板详情
 */
const handleDetail = (record) => {
  templateDetailRef.value.open(record.id)
}

/**
 * 发送测试消息
 */
const handleTestSend = (record) => {
  if (!hasPermission('message:sys_sms_template:testSend')) return
  testSendDialogRef.value.open(record)
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出消息模板数据
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('消息模版清单.xls', { ...searchParams, id: selectedRowKeys.value.join(',') })
    message.success('导出成功')
    clearSelection()
  } finally {
    exportLoading.value = false
  }
}

/**
 * 更新模板使用状态
 */
const handleUseStatusChange = async (record) => {
  if (useStatusLoading.value) return
  useStatusLoading.value = true
  try {
    const newUseStatus = record.useStatus !== '1' ? '1' : '0'
    await updateTemplate({
      ...record,
      useStatus: newUseStatus
    })
    message.success('更新使用状态成功')
    record.useStatus = newUseStatus
  } catch {
    message.error('更新使用状态失败')
  } finally {
    useStatusLoading.value = false
  }
}

/**
 * 删除单个模板
 */
const handleDelete = (record) => {
  if (!hasPermission('message:sys_sms_template:delete')) return
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${record.templateName}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteTemplate({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除模板
 */
const handleBatchDelete = () => {
  if (!hasPermission('message:sys_sms_template:deleteBatch')) return
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个模板吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteTemplate({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 表格变化处理
 */
const onTableChange = ({ current = pagination.value.current, pageNo, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: pageNo ?? current, pageSize, ...searchParams })
}

/**
 * 搜索输入处理
 */
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

onMounted(() => {
  onTableChange()
})
</script>
