<template>
  <a-modal
    v-model:open="visible"
    :title="config[f7Type].modalTitle"
    width="1000px"
    class="common-modal f7-select-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex items-center">
        <depart-select
          v-model="params.depart"
          placeholder="搜索所属部门"
          @change="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
          style="width: 220px; margin-right: 16px"
          :disabled="Boolean(departId)"
          v-if="f7Type === 'user'"
        ></depart-select>
        <s-input
          v-model="params[config[f7Type].keywordKey]"
          :placeholder="config[f7Type].keywordPlaceholder"
          @input="handleInput"
          class="!w-[240px] mr-[16px]"
        ></s-input>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <filter-more
          :params="params"
          :search-list="searchList"
          :width="config[f7Type].filterWidth || '290px'"
          :label-width="config[f7Type].filterLabelWidth || '80px'"
          :clear-ignore-keys="clearIgnoreKeys"
          :stat-ignore-keys="statIgnoreKeys"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
          @filterItemChange="filterItemChange"
        ></filter-more>
      </div>
      <div class="flex items-center">
        <a-checkbox v-model:checked="viewSelected">只看已选</a-checkbox>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="viewSelected ? false : pagination"
      :row-key="config[f7Type].rowKey"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ y: 'calc(75vh - 180px)', x: config[f7Type].scrollX }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import config from './config'
import mitt from '@/utils/mitt'

const { multiple, f7Type, departId, extraParams, mittId } = defineProps({
  // 通信id，与组件f7-table-select配套使用，该id值必须和f7-table-select的mittId值一致，才能正确保持弹窗与select之间的通信
  mittId: { type: String },
  multiple: { type: Boolean, default: false },
  departId: { type: String, default: '' }, // 部门id，只有f7Type=user时会用到
  /**
   * 查询的额外参数，用于与params进行拼接
   * 有时候打开弹窗时，需要带入查询参数打开弹窗，而清空和统计筛选条件的时候，需要将这些额外参数排除在外
   */
  extraParams: { type: Object, default: () => ({}) },
  f7Type: {
    required: true,
    type: String,
    validator: (value) => {
      return [
        'user', // 系统用户
        'contract', // 合同
        'customer', // 客户
        'waterElectricity', // 水电表
        'leaseUnit', // 租赁单元
        'paymentType', // 款项类型
        'receiptPayment', // 收付款记录
        'receiptPaymentDetail', // 收付款记录明细
        'receiveBill', // 应收单
        'receiveBillDetail', // 应收单明细
        'refund', // 退款明细
        'transfer', // 转款明细
        'debt', // 抵扣欠款明细
        'project', // 项目
        'orderBillingItem', // 订单计费项
        'assets'
      ].includes(value)
    }
  }
})

const emit = defineEmits(['updateValue'])

const params = reactive(Object.assign(config[f7Type].params, extraParams))
const searchList = computed(() => {
  const keys = Object.keys(extraParams)
  return config[f7Type].searchList.filter((i) => !keys.includes(i.name))
})

const clearIgnoreKeys = computed(() => {
  const keys = Object.keys(extraParams)
  return (config[f7Type].clearIgnoreKeys || []).concat(keys)
})

const statIgnoreKeys = computed(() => {
  const keys = Object.keys(extraParams)
  return (config[f7Type].statIgnoreKeys || []).concat(keys)
})

const columns = config[f7Type].columns

const { list, pagination, tableLoading, onTableFetch } = usePageTable(config[f7Type].request)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(
  list,
  config[f7Type].rowKey,
  !multiple
)

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  // 处于只看已选模式
  if (viewSelected.value) {
    const id = (selectedRowKeys.value || []).join(',')
    onTableFetch({ pageNo: 1, pageSize: 200, ...params, id: id || '0' })
    return
  }
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const viewSelected = ref(false) // 只看已选
watch(viewSelected, (val) => {
  if (!visible.value) return
  if (val) {
    const id = (selectedRowKeys.value || []).join(',')
    onTableFetch({ pageNo: 1, pageSize: 200, ...params, id: id || '0' })
  } else {
    onTableChange({ pageNo: 1, pageSize: 10 })
  }
})

watch(
  () => extraParams,
  (val) => {
    Object.assign(params, Object.assign(config[f7Type].params, val))
  },
  { deep: true }
)

const visible = ref(false)
const open = (rows) => {
  visible.value = true
  if (departId) {
    params.depart = departId
  }
  onTableChange({ pageNo: 1, pageSize: 10 })
  selectedRowKeys.value = rows && rows.length ? rows.map((i) => i[config[f7Type].rowKey]) : []
  selectedRows.value = rows && rows.length ? rows : []
}

const handleConfirm = () => {
  emit('updateValue', selectedRowKeys.value, selectedRows.value)
  mitt.emit('updateValue', {
    mittId,
    uid,
    rows: selectedRows.value,
    keys: selectedRowKeys.value
  })
  handleCancel()
}

const handleReset = () => {
  clearSelection()
  const ignores = ['column', 'order']
    .concat(config[f7Type].clearIgnoreKeys || [])
    .concat(config[f7Type].statIgnoreKeys || [])
    .concat(Object.keys(extraParams))
  for (const key in params) {
    if (!ignores.includes(key)) {
      params[key] = undefined
    }
  }
  list.value = []
  viewSelected.value = false
}

const handleCancel = () => {
  visible.value = false
  handleReset()
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

let uid
mitt.on('openF7Modal', (data) => {
  if (!mittId || data.mittId !== mittId) return
  uid = data.uid
  visible.value = true
  if (departId) {
    params.depart = departId
  }
  onTableChange({ pageNo: 1, pageSize: 10 })
  if (data.rows && data.rows.length) {
    selectedRowKeys.value = data.rows && data.rows.length ? data.rows.map((i) => i[config[f7Type].rowKey]) : []
    selectedRows.value = data.rows && data.rows.length ? data.rows : []
  }
})

// 筛选项字段和对应的值
const filterItemChange = (id, name) => {
  // 关联项目
  if (name === 'wyProject') {
    searchList.value.forEach((item) => {
      if (item.name === 'wyBuilding') {
        // config[f7Type].wyBuildingPar.id = id
      }
    })
  }
  // 楼栋
  if (name === 'wyBuilding') {
    searchList.value.forEach((item) => {
      if (item.name === 'wyFloor') {
        // wyFloorPar.id = id
      }
    })
  }
}
defineExpose({ open })
</script>

<style lang="less">
.f7-select-modal {
  top: 5vh;
  .ant-input-affix-wrapper,
  .ant-select-selector {
    border-color: #d9d9d9 !important;
  }
  .ant-modal-body {
    max-height: 75vh !important;
  }
}
</style>
