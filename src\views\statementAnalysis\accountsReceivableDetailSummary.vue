<template>
  <div>
    <div class="flex items-center mb-[20px]">
      <a-form layout="inline">
        <a-form-item label="物业管理公司">
          <company-select v-model="userInfo.currentCompany" type="all" disabled width="240px"></company-select>
        </a-form-item>
        <a-form-item label="报表区间">
          <a-range-picker
            v-model:value="periodDate"
            picker="month"
            value-format="YYYY-MM"
            format="YYYY-MM"
            style="width: 240px"
            @change="periodDateChange"
          />
        </a-form-item>
      </a-form>
      <a-button @click="loadData">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <search-more
        v-model="searchFilter"
        :search-list="searchList"
        btn-text="组合条件"
        @searchChange="loadData"
      ></search-more>
    </div>
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="false"
      bordered
      size="middle"
      :row-class-name="(record) => (record[singleArrKey] === '小计' ? 'sum-row' : '')"
    >
      <template #summary>
        <a-table-summary-row>
          <a-table-summary-cell>合计</a-table-summary-cell>
          <a-table-summary-cell v-for="item in recAccMapArr" :key="item.dataIndex"></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell>
            {{ totalRow.totalReceiveAmt }}
          </a-table-summary-cell>
          <a-table-summary-cell>
            {{ totalRow.totalHaveReceiveAmt }}
          </a-table-summary-cell>
          <a-table-summary-cell>
            {{ totalRow.totalInitAmt }}
          </a-table-summary-cell>
        </a-table-summary-row>
      </template>
    </a-table>
  </div>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { renderMoney } from '@/utils/render'
import { useUserStore } from '@/store/modules/user'
import { accountsReceivableDetailSummaryList } from './apis'
const store = useUserStore()
const userInfo = computed(() => store.userInfo)
const periodDateChange = (val) => {
  if (val) {
    search.value.periodFrom = val[0]
    search.value.periodTo = val[1]
  } else {
    search.value.periodFrom = undefined
    search.value.periodTo = undefined
  }
  loadData()
}
const periodDate = ref([])
const search = ref({
  company: '37',
  periodFrom: '',
  periodTo: ''
})
const searchFilter = ref({})
// 动态组合条件的表头数据
const recAccMapArr = computed(() => {
  if (searchFilter.value.recAccMap) {
    const newArr = []
    Object.values(searchFilter.value.recAccMap).forEach((item) => {
      newArr.push(
        {
          title: item.label,
          dataIndex: item.item,
          customCell: (record, index) => {
            // 如果是第一行或与上一行不同，则设置rowSpan
            if (index === 0 || record[item.item] !== tableData.value[index - 1][item.item]) {
              let rowSpan = 1
              // 计算相同值的行数
              for (let i = index + 1; i < tableData.value.length; i++) {
                if (tableData.value[i][item.item] === record[item.item]) {
                  rowSpan++
                } else {
                  break
                }
              }
              return {
                rowSpan
              }
            }
            return {
              rowSpan: 0
            }
          }
        },
        {
          title: `${item.label}编码`,
          dataIndex: `${item.item}Num`,
          customCell: (record, index) => {
            // 如果是第一行或与上一行不同，则设置rowSpan
            if (index === 0 || record[item.item] !== tableData.value[index - 1][item.item]) {
              let rowSpan = 1
              // 计算相同值的行数
              for (let i = index + 1; i < tableData.value.length; i++) {
                if (tableData.value[i][item.item] === record[item.item]) {
                  rowSpan++
                } else {
                  break
                }
              }
              return {
                rowSpan
              }
            }
            return {
              rowSpan: 0
            }
          }
        }
      )
    })
    return newArr
  }
  return []
})
// 当动态组合条件的表头数据刚好是一个的时候的key值
const singleArrKey = computed(() => {
  if (recAccMapArr.value.length === 2) {
    return recAccMapArr.value[0].dataIndex
  }
  return ''
})

const searchList = [
  {
    label: '不含押金应收',
    name: 'notIncPressPayReceive',
    type: 'check'
  },
  {
    label: '含未审核单据',
    name: 'includeNotAudit',
    type: 'check'
  },
  {
    label: '',
    name: 'recAccMap',
    type: 'map'
  }
]
const loading = ref(false)
const totalRow = ref({})
const loadData = async () => {
  if (periodDate.value && !periodDate.value.length) {
    return message.warning('请选择报表区间！')
  }
  if (!searchFilter.value.recAccMap) {
    return message.warning('请选择组合条件！')
  }
  try {
    loading.value = true
    const { result } = await accountsReceivableDetailSummaryList({ ...search.value, ...searchFilter.value })
    if (result && !result.length) return
    if (singleArrKey.value) {
      tableData.value = addSubtotal(result, singleArrKey.value)
      totalRow.value.totalReceiveAmt = renderMoney(
        tableData.value
          .filter((item) => item[singleArrKey.value] !== '小计')
          .reduce((sum, item) => sum + item.receiveAmt, 0)
      )
      totalRow.value.totalHaveReceiveAmt = renderMoney(
        tableData.value
          .filter((item) => item[singleArrKey.value] !== '小计')
          .reduce((sum, item) => sum + item.haveReceiveAmt, 0)
      )
      totalRow.value.totalInitAmt = renderMoney(
        tableData.value
          .filter((item) => item[singleArrKey.value] !== '小计')
          .reduce((sum, item) => sum + item.initAmt, 0)
      )
    } else {
      tableData.value = result
      totalRow.value.totalReceiveAmt = renderMoney(tableData.value.reduce((sum, item) => sum + item.receiveAmt, 0))
      totalRow.value.totalHaveReceiveAmt = renderMoney(
        tableData.value.reduce((sum, item) => sum + item.haveReceiveAmt, 0)
      )
      totalRow.value.totalInitAmt = renderMoney(tableData.value.reduce((sum, item) => sum + item.initAmt, 0))
    }
  } finally {
    loading.value = false
  }
}
// 生成小计行数据
const addSubtotal = (data, dynamicName) => {
  const result = []
  let currentName = null
  let totalReceiveAmt = 0
  let totalHaveReceiveAmt = 0
  let totalInitAmt = 0
  for (const item of data) {
    // 如果遇到新公司，且不是第一条数据
    if (currentName !== null && currentName !== item[dynamicName]) {
      // 添加小计行
      result.push({
        [dynamicName]: '小计',
        receiveAmt: totalReceiveAmt,
        haveReceiveAmt: totalHaveReceiveAmt,
        initAmt: totalInitAmt
      })
      // 重置小计
      totalReceiveAmt = 0
      totalHaveReceiveAmt = 0
      totalInitAmt = 0
    }

    // 添加当前条目
    result.push(item)
    totalReceiveAmt += item.receiveAmt
    totalHaveReceiveAmt += item.haveReceiveAmt
    totalInitAmt += item.initAmt
    currentName = item[dynamicName]
  }

  // 添加最后一个小计行
  if (currentName !== null) {
    result.push({
      [dynamicName]: '小计',
      receiveAmt: totalReceiveAmt,
      haveReceiveAmt: totalHaveReceiveAmt,
      initAmt: totalInitAmt
    })
  }
  return result
}
const tableData = ref([])
const columns = computed(() => {
  const commonList = [
    { title: '年月', dataIndex: 'receiveDate' },
    { title: '单据编号', dataIndex: 'fnumber' },
    { title: '应收开始日期', dataIndex: 'receiveBeginDate' },
    { title: '应收结束日期', dataIndex: 'receiveEndDate' },
    { title: '单据类别', dataIndex: 'billType' },
    { title: '本期应收', dataIndex: 'receiveAmt', customRender: ({ text }) => renderMoney(text) },
    { title: '本期已收', dataIndex: 'haveReceiveAmt', customRender: ({ text }) => renderMoney(text) },
    { title: '余额', dataIndex: 'initAmt', customRender: ({ text }) => renderMoney(text) }
  ]
  if (searchFilter.value.recAccMap) {
    return [
      { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
      {
        title: '动态组合条件',
        children: recAccMapArr.value
      },
      ...commonList
    ]
  }
  return [
    { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
    ...commonList
  ]
})
</script>

<style lang="less" scoped>
:deep(.ant-table-wrapper) {
  .ant-table-tbody > tr.ant-table-row:hover > td {
    background: transparent;
  }
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    background: transparent;
  }
  .ant-table-tbody > tr.ant-table-row > td:hover {
    background-color: #eaf0fe;
  }
  .ant-table-tbody {
    .sum-row td {
      background-color: rgba(var(--color-primary-rgb), 0.1) !important;
    }
  }
  .ant-table-summary {
    .ant-table-cell {
      background-color: rgba(var(--color-primary-rgb), 0.1);
    }
  }
}
</style>
