<!--
 通用选择器，旨在使用时，只需传入options，以及根据需求传入field-names即可
 封装该组件的主要目的，其一是为了使用方便
 其二是为了解决类似api-select这类需要发起请求的组件，被渲染多个，就会发起多次请求的问题
-->
<template>
  <a-select
    v-bind="$attrs"
    show-search
    :value="value || undefined"
    :style="{ width }"
    :options="options"
    :filter-option="filterOption"
    :field-names="fieldNames"
    allow-clear
    @change="onchange"
  ></a-select>
</template>

<script setup>
const { fieldNames, modelValue } = defineProps({
  width: { type: String, default: '100%' },
  modelValue: { type: [Array, String, Number] },
  options: { required: true, type: Array },
  fieldNames: { type: Object, default: () => ({ label: 'label', value: 'value', options: 'options' }) }
})

const emit = defineEmits(['update:modelValue', 'change'])

const value = computed(() => {
  if (modelValue === undefined) return undefined
  if (modelValue === null) return null
  if (typeof modelValue === 'string') return modelValue
  if (typeof modelValue === 'number') return String(modelValue)
  return modelValue.map((item) => String(item))
})

const onchange = (val) => {
  emit('update:modelValue', val)
  emit('change', val)
}

const filterOption = (input, option) => {
  return option[fieldNames.label].toLowerCase().indexOf(input.toLowerCase()) >= 0
}
</script>
