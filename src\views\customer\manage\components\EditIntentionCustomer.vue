<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}意向客户`"
    class="common-drawer edit-customer-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <div>
        <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">客户基础信息</h4>
        <a-form ref="basicFormRef" :model="formData" :rules="basicRules" :label-col="{ style: { width: '140px' } }">
          <a-form-item label="客户名称" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入客户名称" show-count :maxlength="50" />
          </a-form-item>
          <a-form-item label="物业管理公司" name="manageCompany">
            <company-select v-model="formData.manageCompany" placeholder="请选择物业管理公司" disabled />
          </a-form-item>
          <a-form-item label="客户来源" name="customerSource">
            <dict-select
              v-model="formData.customerSource"
              placeholder="客户来源"
              code="CT_BASE_ENUM_Customer_CustomerSource"
            />
          </a-form-item>
          <a-form-item label="客户类型" name="customerType">
            <dict-select
              v-model="formData.customerType"
              placeholder="客户类型"
              code="CT_BASE_ENUM_Customer_CustomerType"
            />
          </a-form-item>
          <a-form-item label="联系电话" name="linkmanPhone">
            <a-input v-model:value="formData.linkmanPhone" placeholder="请输入联系电话" />
          </a-form-item>
        </a-form>

        <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">客户需求</h4>
        <a-form
          ref="requirementFormRef"
          :model="formData"
          :rules="requirementRules"
          :label-col="{ style: { width: '140px' } }"
        >
          <a-form-item label="维护日期" name="maintainDate">
            <a-date-picker v-model:value="formData.maintainDate" value-format="YYYY-MM-DD" style="width: 100%" />
          </a-form-item>
          <a-form-item label="维护人员" name="maintainPerson">
            <a-form-item-rest>
              <f7-select v-model="formData.maintainPerson" placeholder="请选择维护人员" f7-type="user" />
            </a-form-item-rest>
          </a-form-item>
          <a-form-item label="初步需求" name="initRequire" class="form-item-full">
            <a-textarea
              v-model:value="formData.initRequire"
              placeholder="请输入初步需求"
              :rows="4"
              show-count
              :maxlength="500"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { addCustomer, editCustomer, verifyNameExists } from '../apis'
import userStore from '@/store/modules/user.js'

const { userInfo } = userStore()

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)

const basicFormRef = ref()
const requirementFormRef = ref()

const formDataDefault = {
  id: undefined,
  name: undefined,
  customerStatus: 'Intention',
  manageCompany: undefined,
  customerSource: undefined,
  customerType: undefined,
  linkmanPhone: undefined,
  maintainDate: undefined,
  maintainPerson: undefined,
  initRequire: undefined
}
const formData = reactive({ ...formDataDefault })

const basicRules = {
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '客户名称长度应为2-50个字符', trigger: 'blur' },
    {
      validator: async (_, value) => {
        if (!value || value.trim() === '' || formData.id) {
          return Promise.resolve()
        }

        try {
          const response = await verifyNameExists({ name: value.trim() })
          if (response.result === true) {
            return Promise.reject(new Error('客户名称已存在，请使用其他名称'))
          }
          return Promise.resolve()
        } catch {
          return Promise.resolve()
        }
      },
      trigger: 'blur'
    }
  ],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }]
}

const requirementRules = {
  maintainDate: [{ required: true, message: '请选择维护日期', trigger: 'change' }]
}

/**
 * 打开编辑抽屉
 */
const open = (record) => {
  formData.manageCompany = userInfo.value.currentCompany || ''
  formData.maintainDate = dayjs(Date.now()).format('YYYY-MM-DD')
  formData.maintainPerson = userInfo.value.id || ''
  if (record && record.id) {
    Object.assign(formData, record)
  }
  visible.value = true
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  // 保存并启用
  if (!isTemporary) {
    formData.status = 'ENABLE'
    try {
      // 验证所有表单
      await Promise.all([basicFormRef.value?.validate(), requirementFormRef.value?.validate()])
    } catch {
      message.error('请检查并确认表单信息完整、正确')
      confirmLoading.value = false
      return
    }
  }

  // 根据操作类型选择对应的API
  const api = formData.id ? editCustomer : addCustomer

  try {
    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '新建'
    message.success(`意向客户${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 提交客户信息
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存客户信息
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 取消编辑并重置表单
 */
const handleCancel = () => {
  basicFormRef.value?.resetFields()
  requirementFormRef.value?.resetFields()

  Object.assign(formData, formDataDefault)
  formData.status = undefined

  emits('refresh')

  visible.value = false
}

onMounted(() => {
  const infoStr = sessionStorage.getItem('customerFromRentScheme')
  if (!infoStr) return
  const info = JSON.parse(infoStr)
  formData.name = info.name
  formData.customerType = info.customerType
  formData.linkmanPhone = info.linkmanPhone
  formData.customerSource = info.customerSource
  formData.sourceBillId = info.sourceBillId
  sessionStorage.removeItem('customerFromRentScheme')
  open()
})

defineExpose({ open })
</script>

<style lang="less">
.edit-customer-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }
}
</style>
